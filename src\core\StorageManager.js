import Dexie from 'dexie';
import { performanceMonitor } from '../utils/performanceMonitor.js';

class QuoteCanvasDB extends Dexie {
  constructor() {
    super('QuoteCanvasProDB');
    
    this.version(1).stores({
      projects: '++id, name, createdAt, updatedAt, templateId, category',
      templates: '++id, name, category, isCustom, createdAt',
      assets: '++id, name, type, size, createdAt',
      analytics: '++id, event, timestamp, data',
      settings: '++id, key, value, updatedAt',
      brandKits: '++id, name, createdAt, colors, fonts, logos'
    });

    // Define table mappings
    this.projects = this.table('projects');
    this.templates = this.table('templates');
    this.assets = this.table('assets');
    this.analytics = this.table('analytics');
    this.settings = this.table('settings');
    this.brandKits = this.table('brandKits');
  }
}

export class StorageManager {
  static db = null;
  static isInitialized = false;
  static maxStorageSize = 100 * 1024 * 1024; // 100MB limit
  static currentStorageSize = 0;

  // Initialize the storage manager
  static async initialize() {
    try {
      performanceMonitor.startTimer('storage_initialization');

      this.db = new QuoteCanvasDB();
      await this.db.open();

      // Calculate current storage usage
      await this.calculateStorageUsage();

      // Set up periodic cleanup
      this.setupPeriodicCleanup();

      this.isInitialized = true;
      performanceMonitor.endTimer('storage_initialization');

      console.log('Storage manager initialized successfully');
      return true;

    } catch (error) {
      console.error('Failed to initialize storage manager:', error);
      performanceMonitor.logError(error, { context: 'storage_initialization' });
      throw error;
    }
  }

  // Calculate current storage usage
  static async calculateStorageUsage() {
    try {
      let totalSize = 0;

      // Calculate projects size
      const projects = await this.db.projects.toArray();
      totalSize += JSON.stringify(projects).length;

      // Calculate templates size
      const templates = await this.db.templates.toArray();
      totalSize += JSON.stringify(templates).length;

      // Calculate assets size
      const assets = await this.db.assets.toArray();
      totalSize += assets.reduce((sum, asset) => sum + (asset.size || 0), 0);

      this.currentStorageSize = totalSize;
      return totalSize;

    } catch (error) {
      console.error('Failed to calculate storage usage:', error);
      return 0;
    }
  }

  // Check if storage has enough space
  static async checkStorageSpace(requiredSize) {
    const currentUsage = await this.calculateStorageUsage();
    return (currentUsage + requiredSize) <= this.maxStorageSize;
  }

  // Clean up old data to free space
  static async cleanupStorage(targetSize = this.maxStorageSize * 0.8) {
    try {
      performanceMonitor.startTimer('storage_cleanup');

      // Delete old analytics data (keep last 30 days)
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      await this.db.analytics.where('timestamp').below(thirtyDaysAgo).delete();

      // Delete unused assets
      await this.cleanupUnusedAssets();

      // Delete old projects if still over limit
      const currentUsage = await this.calculateStorageUsage();
      if (currentUsage > targetSize) {
        await this.cleanupOldProjects();
      }

      performanceMonitor.endTimer('storage_cleanup');
      performanceMonitor.logEvent('storage_cleaned', {
        beforeSize: this.currentStorageSize,
        afterSize: await this.calculateStorageUsage()
      });

    } catch (error) {
      console.error('Storage cleanup failed:', error);
      performanceMonitor.logError(error, { context: 'storage_cleanup' });
    }
  }

  // Setup periodic cleanup
  static setupPeriodicCleanup() {
    // Run cleanup every hour
    setInterval(() => {
      this.cleanupStorage();
    }, 60 * 60 * 1000);
  }

  // Project Management
  static async saveProject(project) {
    try {
      if (!this.isInitialized) {
        throw new Error('Storage manager not initialized');
      }

      // Check storage space
      const projectSize = JSON.stringify(project).length;
      if (!(await this.checkStorageSpace(projectSize))) {
        await this.cleanupStorage();
        
        if (!(await this.checkStorageSpace(projectSize))) {
          throw new Error('Insufficient storage space');
        }
      }

      const now = new Date().toISOString();
      const projectData = {
        ...project,
        updatedAt: now,
        createdAt: project.createdAt || now
      };

      await this.db.projects.put(projectData);

      performanceMonitor.logEvent('project_saved', {
        projectId: project.id,
        size: projectSize
      });

      return projectData;

    } catch (error) {
      console.error('Failed to save project:', error);
      performanceMonitor.logError(error, { context: 'project_save' });
      throw error;
    }
  }

  static async getProject(id) {
    try {
      const project = await this.db.projects.get(id);
      
      if (project) {
        performanceMonitor.logEvent('project_loaded', {
          projectId: id
        });
      }

      return project;

    } catch (error) {
      console.error('Failed to get project:', error);
      throw error;
    }
  }

  static async getAllProjects() {
    try {
      const projects = await this.db.projects
        .orderBy('updatedAt')
        .reverse()
        .toArray();

      performanceMonitor.logEvent('projects_listed', {
        count: projects.length
      });

      return projects;

    } catch (error) {
      console.error('Failed to get all projects:', error);
      throw error;
    }
  }

  static async deleteProject(id) {
    try {
      await this.db.projects.delete(id);
      
      performanceMonitor.logEvent('project_deleted', {
        projectId: id
      });

      return true;

    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  static async getRecentProjects(limit = 10) {
    try {
      const projects = await this.db.projects
        .orderBy('updatedAt')
        .reverse()
        .limit(limit)
        .toArray();

      return projects;

    } catch (error) {
      console.error('Failed to get recent projects:', error);
      return [];
    }
  }

  static async addToRecentProjects(project) {
    try {
      // Update the project's updatedAt timestamp
      await this.db.projects.update(project.id, {
        updatedAt: new Date().toISOString()
      });

      return true;

    } catch (error) {
      console.error('Failed to add to recent projects:', error);
      return false;
    }
  }

  // Template Management
  static async saveCustomTemplate(template) {
    try {
      const templateData = {
        ...template,
        isCustom: true,
        createdAt: template.createdAt || new Date().toISOString()
      };

      await this.db.templates.put(templateData);

      performanceMonitor.logEvent('custom_template_saved', {
        templateId: template.id
      });

      return templateData;

    } catch (error) {
      console.error('Failed to save custom template:', error);
      throw error;
    }
  }

  static async getCustomTemplates() {
    try {
      const templates = await this.db.templates
        .where('isCustom')
        .equals(1)
        .toArray();

      return templates;

    } catch (error) {
      console.error('Failed to get custom templates:', error);
      return [];
    }
  }

  static async deleteCustomTemplate(id) {
    try {
      await this.db.templates.delete(id);
      
      performanceMonitor.logEvent('custom_template_deleted', {
        templateId: id
      });

      return true;

    } catch (error) {
      console.error('Failed to delete custom template:', error);
      throw error;
    }
  }

  // Asset Management
  static async saveAsset(asset) {
    try {
      const assetData = {
        ...asset,
        createdAt: new Date().toISOString(),
        size: asset.data ? asset.data.length : 0
      };

      // Check storage space
      if (!(await this.checkStorageSpace(assetData.size))) {
        await this.cleanupStorage();
        
        if (!(await this.checkStorageSpace(assetData.size))) {
          throw new Error('Asset too large for available storage');
        }
      }

      const id = await this.db.assets.add(assetData);
      
      performanceMonitor.logEvent('asset_saved', {
        assetId: id,
        type: asset.type,
        size: assetData.size
      });

      return { ...assetData, id };

    } catch (error) {
      console.error('Failed to save asset:', error);
      throw error;
    }
  }

  static async getAsset(id) {
    try {
      return await this.db.assets.get(id);
    } catch (error) {
      console.error('Failed to get asset:', error);
      return null;
    }
  }

  static async deleteAsset(id) {
    try {
      await this.db.assets.delete(id);
      return true;
    } catch (error) {
      console.error('Failed to delete asset:', error);
      return false;
    }
  }

  static async cleanupUnusedAssets() {
    try {
      // Get all assets
      const assets = await this.db.assets.toArray();
      
      // Get all projects to check asset usage
      const projects = await this.db.projects.toArray();
      const templates = await this.db.templates.toArray();

      // Find unused assets
      const usedAssetIds = new Set();
      
      [...projects, ...templates].forEach(item => {
        if (item.assets) {
          item.assets.forEach(assetId => usedAssetIds.add(assetId));
        }
      });

      const unusedAssets = assets.filter(asset => !usedAssetIds.has(asset.id));
      
      // Delete unused assets
      for (const asset of unusedAssets) {
        await this.db.assets.delete(asset.id);
      }

      console.log(`Cleaned up ${unusedAssets.length} unused assets`);
      return unusedAssets.length;

    } catch (error) {
      console.error('Failed to cleanup unused assets:', error);
      return 0;
    }
  }

  // Settings Management
  static async saveSetting(key, value) {
    try {
      const settingData = {
        key,
        value: JSON.stringify(value),
        updatedAt: new Date().toISOString()
      };

      await this.db.settings.put(settingData);
      return true;

    } catch (error) {
      console.error('Failed to save setting:', error);
      return false;
    }
  }

  static async getSetting(key, defaultValue = null) {
    try {
      const setting = await this.db.settings.where('key').equals(key).first();
      
      if (setting) {
        return JSON.parse(setting.value);
      }
      
      return defaultValue;

    } catch (error) {
      console.error('Failed to get setting:', error);
      return defaultValue;
    }
  }

  static async getAllSettings() {
    try {
      const settings = await this.db.settings.toArray();
      const settingsObj = {};
      
      settings.forEach(setting => {
        try {
          settingsObj[setting.key] = JSON.parse(setting.value);
        } catch (e) {
          settingsObj[setting.key] = setting.value;
        }
      });

      return settingsObj;

    } catch (error) {
      console.error('Failed to get all settings:', error);
      return {};
    }
  }

  // Analytics Management
  static async logAnalyticsEvent(event, data = {}) {
    try {
      const eventData = {
        event,
        data: JSON.stringify(data),
        timestamp: Date.now()
      };

      await this.db.analytics.add(eventData);
      return true;

    } catch (error) {
      console.error('Failed to log analytics event:', error);
      return false;
    }
  }

  static async getAnalyticsEvents(startDate, endDate, eventType = null) {
    try {
      let query = this.db.analytics
        .where('timestamp')
        .between(startDate, endDate);

      if (eventType) {
        query = query.and(item => item.event === eventType);
      }

      const events = await query.toArray();
      
      return events.map(event => ({
        ...event,
        data: JSON.parse(event.data)
      }));

    } catch (error) {
      console.error('Failed to get analytics events:', error);
      return [];
    }
  }

  // Brand Kit Management
  static async saveBrandKit(brandKit) {
    try {
      const brandKitData = {
        ...brandKit,
        createdAt: brandKit.createdAt || new Date().toISOString()
      };

      await this.db.brandKits.put(brandKitData);

      performanceMonitor.logEvent('brand_kit_saved', {
        brandKitId: brandKit.id
      });

      return brandKitData;

    } catch (error) {
      console.error('Failed to save brand kit:', error);
      throw error;
    }
  }

  static async getBrandKits() {
    try {
      return await this.db.brandKits.orderBy('createdAt').reverse().toArray();
    } catch (error) {
      console.error('Failed to get brand kits:', error);
      return [];
    }
  }

  static async deleteBrandKit(id) {
    try {
      await this.db.brandKits.delete(id);
      return true;
    } catch (error) {
      console.error('Failed to delete brand kit:', error);
      return false;
    }
  }

  // Cleanup old projects
  static async cleanupOldProjects() {
    try {
      // Keep only the 100 most recent projects
      const allProjects = await this.db.projects
        .orderBy('updatedAt')
        .reverse()
        .toArray();

      if (allProjects.length > 100) {
        const projectsToDelete = allProjects.slice(100);
        
        for (const project of projectsToDelete) {
          await this.db.projects.delete(project.id);
        }

        console.log(`Cleaned up ${projectsToDelete.length} old projects`);
        return projectsToDelete.length;
      }

      return 0;

    } catch (error) {
      console.error('Failed to cleanup old projects:', error);
      return 0;
    }
  }

  // Export data for backup
  static async exportData() {
    try {
      const data = {
        projects: await this.db.projects.toArray(),
        templates: await this.db.templates.toArray(),
        brandKits: await this.db.brandKits.toArray(),
        settings: await this.db.settings.toArray(),
        exportDate: new Date().toISOString()
      };

      return data;

    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }

  // Import data from backup
  static async importData(data) {
    try {
      if (data.projects) {
        await this.db.projects.bulkPut(data.projects);
      }
      
      if (data.templates) {
        await this.db.templates.bulkPut(data.templates);
      }
      
      if (data.brandKits) {
        await this.db.brandKits.bulkPut(data.brandKits);
      }
      
      if (data.settings) {
        await this.db.settings.bulkPut(data.settings);
      }

      performanceMonitor.logEvent('data_imported', {
        projects: data.projects?.length || 0,
        templates: data.templates?.length || 0,
        brandKits: data.brandKits?.length || 0
      });

      return true;

    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }

  // Get storage statistics
  static async getStorageStats() {
    try {
      const stats = {
        totalSize: await this.calculateStorageUsage(),
        maxSize: this.maxStorageSize,
        projects: await this.db.projects.count(),
        templates: await this.db.templates.count(),
        assets: await this.db.assets.count(),
        brandKits: await this.db.brandKits.count(),
        analytics: await this.db.analytics.count()
      };

      stats.usagePercentage = (stats.totalSize / stats.maxSize) * 100;
      
      return stats;

    } catch (error) {
      console.error('Failed to get storage stats:', error);
      return null;
    }
  }
}
