import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

// Core components
import Header from './components/UI/Header.js';
import Sidebar from './components/UI/Sidebar.js';
import CanvasEditor from './components/Canvas/CanvasEditor.js';
import TemplateGallery from './components/Templates/TemplateGallery.js';
import ExportPanel from './components/Export/ExportPanel.js';
import MobileControls from './components/UI/MobileControls.js';

// Core engines
import { CanvasEngine } from './core/CanvasEngine.js';
import { TemplateEngine } from './core/TemplateEngine.js';
import { StorageManager } from './core/StorageManager.js';
import { AIOptimizer } from './core/AIOptimizer.js';

// Utils
import { performanceMonitor } from './utils/performanceMonitor.js';

// Styles
import './styles/canvas.css';
import './styles/mobile.css';
import './styles/animations.css';

function App() {
  // Core state
  const [currentProject, setCurrentProject] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [canvasEngine, setCanvasEngine] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // UI state
  const [activePanel, setActivePanel] = useState('templates'); // templates, canvas, export
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [showMobileControls, setShowMobileControls] = useState(false);

  // Template and project state
  const [templates, setTemplates] = useState([]);
  const [projects, setProjects] = useState([]);
  const [recentProjects, setRecentProjects] = useState([]);

  // Performance state
  const [renderTime, setRenderTime] = useState(0);
  const [lastSaved, setLastSaved] = useState(null);

  // Initialize engines and load data
  useEffect(() => {
    initializeApp();
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      
      if (mobile && activePanel === 'canvas') {
        setShowMobileControls(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [activePanel]);

  // Auto-save functionality
  useEffect(() => {
    if (currentProject && canvasEngine) {
      const autoSaveInterval = setInterval(() => {
        saveProject();
      }, 30000); // Auto-save every 30 seconds

      return () => clearInterval(autoSaveInterval);
    }
  }, [currentProject, canvasEngine]);

  // Initialize the application
  const initializeApp = async () => {
    try {
      setIsLoading(true);
      performanceMonitor.startTimer('app_initialization');

      // Initialize storage
      await StorageManager.initialize();

      // Initialize template engine
      const templateEngine = new TemplateEngine();
      await templateEngine.initialize();
      const loadedTemplates = await templateEngine.getAllTemplates();
      setTemplates(loadedTemplates);

      // Initialize canvas engine
      const canvas = new CanvasEngine();
      await canvas.initialize();
      setCanvasEngine(canvas);

      // Load recent projects
      const recentProjects = await StorageManager.getRecentProjects();
      setRecentProjects(recentProjects);

      // Load all projects
      const allProjects = await StorageManager.getAllProjects();
      setProjects(allProjects);

      performanceMonitor.endTimer('app_initialization');
      setIsLoading(false);

      // Log successful initialization
      performanceMonitor.logEvent('app_ready', {
        templatesLoaded: loadedTemplates.length,
        projectsLoaded: allProjects.length
      });

    } catch (err) {
      console.error('Failed to initialize app:', err);
      setError(err.message);
      setIsLoading(false);
      performanceMonitor.logError(err, { context: 'app_initialization' });
    }
  };

  // Create new project
  const createNewProject = useCallback(async (template = null, text = '') => {
    try {
      performanceMonitor.startTimer('project_creation');

      const project = {
        id: crypto.randomUUID(),
        name: `Untitled Project ${projects.length + 1}`,
        text: text,
        template: template,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        settings: {
          fontSize: 24,
          fontFamily: 'Inter',
          textColor: '#000000',
          backgroundColor: '#ffffff',
          alignment: 'center'
        }
      };

      if (template) {
        // Apply template to canvas
        await canvasEngine.loadTemplate(template);
        if (text) {
          await canvasEngine.setText(text);
        }
      }

      setCurrentProject(project);
      setSelectedTemplate(template);
      setActivePanel('canvas');

      // Save project
      await StorageManager.saveProject(project);
      setProjects(prev => [project, ...prev]);

      performanceMonitor.endTimer('project_creation');
      performanceMonitor.logEvent('project_created', {
        templateId: template?.id,
        hasText: !!text
      });

    } catch (err) {
      console.error('Failed to create project:', err);
      setError(err.message);
      performanceMonitor.logError(err, { context: 'project_creation' });
    }
  }, [canvasEngine, projects.length]);

  // Load existing project
  const loadProject = useCallback(async (project) => {
    try {
      performanceMonitor.startTimer('project_loading');

      setCurrentProject(project);
      
      if (project.template) {
        await canvasEngine.loadTemplate(project.template);
        setSelectedTemplate(project.template);
      }

      if (project.text) {
        await canvasEngine.setText(project.text);
      }

      // Apply project settings
      if (project.settings) {
        await canvasEngine.applySettings(project.settings);
      }

      setActivePanel('canvas');

      // Update recent projects
      await StorageManager.addToRecentProjects(project);
      const updatedRecent = await StorageManager.getRecentProjects();
      setRecentProjects(updatedRecent);

      performanceMonitor.endTimer('project_loading');
      performanceMonitor.logEvent('project_loaded', {
        projectId: project.id,
        templateId: project.template?.id
      });

    } catch (err) {
      console.error('Failed to load project:', err);
      setError(err.message);
      performanceMonitor.logError(err, { context: 'project_loading' });
    }
  }, [canvasEngine]);

  // Save current project
  const saveProject = useCallback(async () => {
    if (!currentProject || !canvasEngine) return;

    try {
      const updatedProject = {
        ...currentProject,
        updatedAt: new Date().toISOString(),
        canvasData: await canvasEngine.getCanvasData(),
        settings: await canvasEngine.getSettings()
      };

      await StorageManager.saveProject(updatedProject);
      setCurrentProject(updatedProject);
      setLastSaved(new Date());

      performanceMonitor.logEvent('project_saved', {
        projectId: updatedProject.id
      });

    } catch (err) {
      console.error('Failed to save project:', err);
      performanceMonitor.logError(err, { context: 'project_saving' });
    }
  }, [currentProject, canvasEngine]);

  // Handle template selection
  const handleTemplateSelect = useCallback(async (template) => {
    try {
      if (currentProject) {
        // Update existing project
        await canvasEngine.loadTemplate(template);
        setSelectedTemplate(template);
        
        const updatedProject = {
          ...currentProject,
          template: template,
          updatedAt: new Date().toISOString()
        };
        setCurrentProject(updatedProject);
      } else {
        // Create new project with template
        await createNewProject(template);
      }

      performanceMonitor.logEvent('template_selected', {
        templateId: template.id,
        category: template.category
      });

    } catch (err) {
      console.error('Failed to select template:', err);
      setError(err.message);
    }
  }, [currentProject, canvasEngine, createNewProject]);

  // Keyboard shortcuts
  useHotkeys('ctrl+s, cmd+s', (e) => {
    e.preventDefault();
    saveProject();
  });

  useHotkeys('ctrl+n, cmd+n', (e) => {
    e.preventDefault();
    createNewProject();
  });

  useHotkeys('ctrl+z, cmd+z', (e) => {
    e.preventDefault();
    if (canvasEngine) {
      canvasEngine.undo();
    }
  });

  useHotkeys('ctrl+y, cmd+y', (e) => {
    e.preventDefault();
    if (canvasEngine) {
      canvasEngine.redo();
    }
  });

  // Memoized components for performance
  const memoizedTemplateGallery = useMemo(() => (
    <TemplateGallery
      templates={templates}
      onTemplateSelect={handleTemplateSelect}
      selectedTemplate={selectedTemplate}
    />
  ), [templates, handleTemplateSelect, selectedTemplate]);

  const memoizedCanvasEditor = useMemo(() => (
    canvasEngine ? (
      <CanvasEditor
        canvasEngine={canvasEngine}
        currentProject={currentProject}
        onProjectUpdate={setCurrentProject}
        onRenderTimeUpdate={setRenderTime}
      />
    ) : null
  ), [canvasEngine, currentProject]);

  // Loading state
  if (isLoading) {
    return (
      <div className="app-loading">
        <div className="loading-content">
          <div className="loading-spinner"></div>
          <p>Initializing QuoteCanvas Pro...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="app-error">
        <div className="error-content">
          <h2>⚠️ Something went wrong</h2>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>
            Reload App
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`app ${isMobile ? 'mobile' : 'desktop'}`}>
      <Header
        currentProject={currentProject}
        onSave={saveProject}
        onNewProject={() => createNewProject()}
        lastSaved={lastSaved}
        renderTime={renderTime}
      />

      <div className="app-body">
        {!isMobile && (
          <Sidebar
            collapsed={sidebarCollapsed}
            onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
            activePanel={activePanel}
            onPanelChange={setActivePanel}
            recentProjects={recentProjects}
            onProjectLoad={loadProject}
          />
        )}

        <main className="app-main">
          {activePanel === 'templates' && memoizedTemplateGallery}
          {activePanel === 'canvas' && memoizedCanvasEditor}
          {activePanel === 'export' && (
            <ExportPanel
              canvasEngine={canvasEngine}
              currentProject={currentProject}
            />
          )}
        </main>

        {isMobile && showMobileControls && (
          <MobileControls
            activePanel={activePanel}
            onPanelChange={setActivePanel}
            onClose={() => setShowMobileControls(false)}
            canvasEngine={canvasEngine}
          />
        )}
      </div>

      {isMobile && (
        <div className="mobile-fab">
          <button
            className="fab-button"
            onClick={() => setShowMobileControls(true)}
          >
            ⚙️
          </button>
        </div>
      )}
    </div>
  );
}

export default App;
