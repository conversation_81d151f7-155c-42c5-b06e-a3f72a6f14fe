class PerformanceMonitor {
  constructor() {
    this.timers = new Map();
    this.metrics = new Map();
    this.events = [];
    this.errors = [];
    this.isInitialized = false;
    this.maxEvents = 1000;
    this.maxErrors = 100;
    this.sessionId = this.generateSessionId();
    this.startTime = performance.now();
  }

  // Initialize performance monitoring
  init() {
    if (this.isInitialized) return;

    // Monitor page load performance
    this.monitorPageLoad();

    // Monitor memory usage
    this.monitorMemoryUsage();

    // Monitor frame rate
    this.monitorFrameRate();

    // Monitor network status
    this.monitorNetworkStatus();

    // Set up periodic reporting
    this.setupPeriodicReporting();

    this.isInitialized = true;
    this.logEvent('performance_monitor_initialized', {
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    });
  }

  // Generate unique session ID
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Start a performance timer
  startTimer(name) {
    this.timers.set(name, {
      startTime: performance.now(),
      name: name
    });
  }

  // End a performance timer and record the duration
  endTimer(name) {
    const timer = this.timers.get(name);
    if (!timer) {
      console.warn(`Timer ${name} not found`);
      return null;
    }

    const duration = performance.now() - timer.startTime;
    this.timers.delete(name);

    // Record the metric
    this.recordMetric(name, duration);

    // Log slow operations
    if (duration > 1000) { // More than 1 second
      this.logEvent('slow_operation', {
        operation: name,
        duration: duration,
        severity: 'warning'
      });
    }

    return duration;
  }

  // Record a performance metric
  recordMetric(name, value, unit = 'ms') {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, {
        name: name,
        values: [],
        unit: unit,
        count: 0,
        sum: 0,
        min: Infinity,
        max: -Infinity,
        avg: 0
      });
    }

    const metric = this.metrics.get(name);
    metric.values.push({
      value: value,
      timestamp: Date.now()
    });

    // Keep only last 100 values to prevent memory issues
    if (metric.values.length > 100) {
      metric.values.shift();
    }

    // Update statistics
    metric.count++;
    metric.sum += value;
    metric.min = Math.min(metric.min, value);
    metric.max = Math.max(metric.max, value);
    metric.avg = metric.sum / metric.count;
  }

  // Log an event
  logEvent(eventName, data = {}) {
    const event = {
      name: eventName,
      data: data,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.events.push(event);

    // Limit events array size
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Event] ${eventName}:`, data);
    }

    // Send to analytics service (would be implemented in production)
    this.sendToAnalytics(event);
  }

  // Log an error
  logError(error, context = {}) {
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context: context,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.errors.push(errorData);

    // Limit errors array size
    if (this.errors.length > this.maxErrors) {
      this.errors.shift();
    }

    // Log to console
    console.error('[Performance Monitor] Error logged:', errorData);

    // Send to error tracking service
    this.sendErrorToTracking(errorData);
  }

  // Monitor page load performance
  monitorPageLoad() {
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        // Use requestIdleCallback to avoid blocking
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            this.recordPageLoadMetrics();
          });
        } else {
          setTimeout(() => {
            this.recordPageLoadMetrics();
          }, 0);
        }
      });
    }
  }

  // Record page load metrics
  recordPageLoadMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0];
    if (navigation) {
      this.recordMetric('page_load_time', navigation.loadEventEnd - navigation.fetchStart);
      this.recordMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
      this.recordMetric('first_paint', navigation.responseEnd - navigation.fetchStart);
      
      this.logEvent('page_load_complete', {
        loadTime: navigation.loadEventEnd - navigation.fetchStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        transferSize: navigation.transferSize || 0
      });
    }

    // Record paint metrics
    const paintEntries = performance.getEntriesByType('paint');
    paintEntries.forEach(entry => {
      this.recordMetric(entry.name.replace('-', '_'), entry.startTime);
    });
  }

  // Monitor memory usage
  monitorMemoryUsage() {
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = performance.memory;
        this.recordMetric('memory_used', memory.usedJSHeapSize, 'bytes');
        this.recordMetric('memory_total', memory.totalJSHeapSize, 'bytes');
        this.recordMetric('memory_limit', memory.jsHeapSizeLimit, 'bytes');

        // Warn if memory usage is high
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        if (usagePercent > 80) {
          this.logEvent('high_memory_usage', {
            usagePercent: usagePercent,
            usedMB: Math.round(memory.usedJSHeapSize / 1024 / 1024),
            totalMB: Math.round(memory.totalJSHeapSize / 1024 / 1024)
          });
        }
      };

      // Check memory every 30 seconds
      setInterval(checkMemory, 30000);
      checkMemory(); // Initial check
    }
  }

  // Monitor frame rate
  monitorFrameRate() {
    let lastTime = performance.now();
    let frameCount = 0;
    let totalFrameTime = 0;

    const measureFrameRate = (currentTime) => {
      frameCount++;
      const deltaTime = currentTime - lastTime;
      totalFrameTime += deltaTime;

      // Calculate FPS every second
      if (totalFrameTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / totalFrameTime);
        this.recordMetric('frame_rate', fps, 'fps');

        // Warn about low frame rate
        if (fps < 30) {
          this.logEvent('low_frame_rate', {
            fps: fps,
            severity: fps < 15 ? 'error' : 'warning'
          });
        }

        frameCount = 0;
        totalFrameTime = 0;
      }

      lastTime = currentTime;
      requestAnimationFrame(measureFrameRate);
    };

    requestAnimationFrame(measureFrameRate);
  }

  // Monitor network status
  monitorNetworkStatus() {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      
      const logConnectionInfo = () => {
        this.logEvent('network_info', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        });
      };

      connection.addEventListener('change', logConnectionInfo);
      logConnectionInfo(); // Initial log
    }

    // Monitor online/offline status
    window.addEventListener('online', () => {
      this.logEvent('network_online');
    });

    window.addEventListener('offline', () => {
      this.logEvent('network_offline');
    });
  }

  // Set up periodic reporting
  setupPeriodicReporting() {
    // Report metrics every 5 minutes
    setInterval(() => {
      this.generatePerformanceReport();
    }, 5 * 60 * 1000);
  }

  // Generate performance report
  generatePerformanceReport() {
    const report = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      sessionDuration: Date.now() - this.startTime,
      metrics: this.getMetricsSummary(),
      recentEvents: this.events.slice(-10),
      recentErrors: this.errors.slice(-5),
      systemInfo: this.getSystemInfo()
    };

    this.logEvent('performance_report', report);
    return report;
  }

  // Get metrics summary
  getMetricsSummary() {
    const summary = {};
    
    this.metrics.forEach((metric, name) => {
      summary[name] = {
        count: metric.count,
        avg: Math.round(metric.avg * 100) / 100,
        min: Math.round(metric.min * 100) / 100,
        max: Math.round(metric.max * 100) / 100,
        unit: metric.unit
      };
    });

    return summary;
  }

  // Get system information
  getSystemInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      colorDepth: screen.colorDepth,
      pixelRatio: window.devicePixelRatio || 1,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      memory: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      } : null
    };
  }

  // Log performance metrics for debugging
  logPerformanceMetrics() {
    const metrics = this.getMetricsSummary();
    console.group('Performance Metrics');
    Object.entries(metrics).forEach(([name, data]) => {
      console.log(`${name}: ${data.avg}${data.unit} (min: ${data.min}, max: ${data.max}, count: ${data.count})`);
    });
    console.groupEnd();
  }

  // Send event to analytics service (placeholder)
  sendToAnalytics(event) {
    // In production, this would send to your analytics service
    // For now, we'll store in localStorage for debugging
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const stored = JSON.parse(localStorage.getItem('quotecanvas_analytics') || '[]');
        stored.push(event);
        
        // Keep only last 100 events in localStorage
        if (stored.length > 100) {
          stored.splice(0, stored.length - 100);
        }
        
        localStorage.setItem('quotecanvas_analytics', JSON.stringify(stored));
      } catch (error) {
        console.warn('Failed to store analytics event:', error);
      }
    }
  }

  // Send error to tracking service (placeholder)
  sendErrorToTracking(errorData) {
    // In production, this would send to your error tracking service (Sentry, etc.)
    console.error('Error tracked:', errorData);
    
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const stored = JSON.parse(localStorage.getItem('quotecanvas_errors') || '[]');
        stored.push(errorData);
        
        // Keep only last 50 errors in localStorage
        if (stored.length > 50) {
          stored.splice(0, stored.length - 50);
        }
        
        localStorage.setItem('quotecanvas_errors', JSON.stringify(stored));
      } catch (error) {
        console.warn('Failed to store error:', error);
      }
    }
  }

  // Get all events
  getEvents(limit = 100) {
    return this.events.slice(-limit);
  }

  // Get all errors
  getErrors(limit = 50) {
    return this.errors.slice(-limit);
  }

  // Get specific metric
  getMetric(name) {
    return this.metrics.get(name);
  }

  // Clear all data
  clear() {
    this.timers.clear();
    this.metrics.clear();
    this.events = [];
    this.errors = [];
  }

  // Export data for analysis
  exportData() {
    return {
      sessionId: this.sessionId,
      startTime: this.startTime,
      metrics: Object.fromEntries(this.metrics),
      events: this.events,
      errors: this.errors,
      systemInfo: this.getSystemInfo(),
      exportTime: Date.now()
    };
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export class for testing
export { PerformanceMonitor };
