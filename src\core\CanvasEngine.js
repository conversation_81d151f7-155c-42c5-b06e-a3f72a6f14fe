import { fabric } from 'fabric';
import { performanceMonitor } from '../utils/performanceMonitor.js';

export class CanvasEngine {
  constructor() {
    this.canvas = null;
    this.history = [];
    this.historyIndex = -1;
    this.maxHistory = 50;
    this.isInitialized = false;
    this.currentTemplate = null;
    this.settings = {
      width: 1080,
      height: 1080,
      backgroundColor: '#ffffff',
      quality: 1
    };
    this.animationFrame = null;
    this.renderCallbacks = [];
  }

  // Initialize the canvas engine
  async initialize(canvasElement = null) {
    try {
      performanceMonitor.startTimer('canvas_initialization');

      // Create canvas element if not provided
      if (!canvasElement) {
        canvasElement = document.createElement('canvas');
        canvasElement.id = 'main-canvas';
        canvasElement.width = this.settings.width;
        canvasElement.height = this.settings.height;
      }

      // Initialize Fabric.js canvas
      this.canvas = new fabric.Canvas(canvasElement, {
        width: this.settings.width,
        height: this.settings.height,
        backgroundColor: this.settings.backgroundColor,
        preserveObjectStacking: true,
        renderOnAddRemove: false, // Manual rendering for performance
        skipTargetFind: false,
        selection: true,
        hoverCursor: 'pointer',
        moveCursor: 'grab'
      });

      // Enable high DPI rendering
      this.enableHighDPI();

      // Set up event listeners
      this.setupEventListeners();

      // Initialize history
      this.saveState();

      this.isInitialized = true;
      performanceMonitor.endTimer('canvas_initialization');

      console.log('Canvas engine initialized successfully');
      return true;

    } catch (error) {
      console.error('Failed to initialize canvas engine:', error);
      performanceMonitor.logError(error, { context: 'canvas_initialization' });
      throw error;
    }
  }

  // Enable high DPI rendering for crisp images
  enableHighDPI() {
    const ratio = window.devicePixelRatio || 1;
    const canvas = this.canvas.getElement();
    const context = canvas.getContext('2d');

    // Set actual size in memory (scaled up for high DPI)
    canvas.width = this.settings.width * ratio;
    canvas.height = this.settings.height * ratio;

    // Scale down using CSS
    canvas.style.width = this.settings.width + 'px';
    canvas.style.height = this.settings.height + 'px';

    // Scale the drawing context
    context.scale(ratio, ratio);

    // Update Fabric.js canvas
    this.canvas.setDimensions({
      width: this.settings.width,
      height: this.settings.height
    });
  }

  // Set up event listeners for canvas interactions
  setupEventListeners() {
    // Object modification events
    this.canvas.on('object:modified', () => {
      this.saveState();
      this.triggerRender();
    });

    this.canvas.on('object:added', () => {
      this.saveState();
      this.triggerRender();
    });

    this.canvas.on('object:removed', () => {
      this.saveState();
      this.triggerRender();
    });

    // Selection events
    this.canvas.on('selection:created', (e) => {
      this.onObjectSelected(e.selected);
    });

    this.canvas.on('selection:updated', (e) => {
      this.onObjectSelected(e.selected);
    });

    this.canvas.on('selection:cleared', () => {
      this.onObjectDeselected();
    });

    // Performance monitoring
    this.canvas.on('after:render', () => {
      this.onAfterRender();
    });
  }

  // Load template into canvas
  async loadTemplate(template) {
    try {
      performanceMonitor.startTimer('template_loading');

      if (!this.isInitialized) {
        throw new Error('Canvas engine not initialized');
      }

      // Clear existing objects
      this.canvas.clear();

      // Set canvas dimensions and background
      if (template.dimensions) {
        this.canvas.setDimensions(template.dimensions);
        this.settings.width = template.dimensions.width;
        this.settings.height = template.dimensions.height;
      }

      if (template.backgroundColor) {
        this.canvas.setBackgroundColor(template.backgroundColor, () => {
          this.canvas.renderAll();
        });
      }

      // Load template objects
      if (template.objects && template.objects.length > 0) {
        await this.loadTemplateObjects(template.objects);
      }

      // Apply template styles
      if (template.styles) {
        await this.applyTemplateStyles(template.styles);
      }

      this.currentTemplate = template;
      this.saveState();

      performanceMonitor.endTimer('template_loading');
      performanceMonitor.logEvent('template_loaded', {
        templateId: template.id,
        objectCount: template.objects?.length || 0
      });

      return true;

    } catch (error) {
      console.error('Failed to load template:', error);
      performanceMonitor.logError(error, { context: 'template_loading' });
      throw error;
    }
  }

  // Load template objects into canvas
  async loadTemplateObjects(objects) {
    const loadPromises = objects.map(async (obj) => {
      try {
        let fabricObject;

        switch (obj.type) {
          case 'text':
            fabricObject = new fabric.Text(obj.text || 'Sample Text', {
              left: obj.left || 0,
              top: obj.top || 0,
              fontSize: obj.fontSize || 24,
              fontFamily: obj.fontFamily || 'Inter',
              fill: obj.fill || '#000000',
              textAlign: obj.textAlign || 'center',
              fontWeight: obj.fontWeight || 'normal',
              fontStyle: obj.fontStyle || 'normal'
            });
            break;

          case 'textbox':
            fabricObject = new fabric.Textbox(obj.text || 'Sample Text', {
              left: obj.left || 0,
              top: obj.top || 0,
              width: obj.width || 200,
              fontSize: obj.fontSize || 24,
              fontFamily: obj.fontFamily || 'Inter',
              fill: obj.fill || '#000000',
              textAlign: obj.textAlign || 'center'
            });
            break;

          case 'rect':
            fabricObject = new fabric.Rect({
              left: obj.left || 0,
              top: obj.top || 0,
              width: obj.width || 100,
              height: obj.height || 100,
              fill: obj.fill || '#000000',
              stroke: obj.stroke,
              strokeWidth: obj.strokeWidth || 0,
              rx: obj.rx || 0,
              ry: obj.ry || 0
            });
            break;

          case 'circle':
            fabricObject = new fabric.Circle({
              left: obj.left || 0,
              top: obj.top || 0,
              radius: obj.radius || 50,
              fill: obj.fill || '#000000',
              stroke: obj.stroke,
              strokeWidth: obj.strokeWidth || 0
            });
            break;

          case 'image':
            if (obj.src) {
              fabricObject = await this.loadImage(obj.src, obj);
            }
            break;

          default:
            console.warn(`Unknown object type: ${obj.type}`);
            return null;
        }

        if (fabricObject) {
          // Apply common properties
          if (obj.opacity !== undefined) fabricObject.set('opacity', obj.opacity);
          if (obj.angle !== undefined) fabricObject.set('angle', obj.angle);
          if (obj.scaleX !== undefined) fabricObject.set('scaleX', obj.scaleX);
          if (obj.scaleY !== undefined) fabricObject.set('scaleY', obj.scaleY);
          if (obj.selectable !== undefined) fabricObject.set('selectable', obj.selectable);

          // Add custom properties
          fabricObject.set('templateId', obj.id);
          fabricObject.set('templateType', obj.type);

          return fabricObject;
        }

      } catch (error) {
        console.error(`Failed to load object ${obj.id}:`, error);
        return null;
      }
    });

    const fabricObjects = await Promise.all(loadPromises);
    const validObjects = fabricObjects.filter(obj => obj !== null);

    // Add objects to canvas
    validObjects.forEach(obj => {
      this.canvas.add(obj);
    });

    this.canvas.renderAll();
  }

  // Load image object
  async loadImage(src, properties = {}) {
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(src, (img) => {
        if (img) {
          img.set({
            left: properties.left || 0,
            top: properties.top || 0,
            scaleX: properties.scaleX || 1,
            scaleY: properties.scaleY || 1,
            angle: properties.angle || 0,
            opacity: properties.opacity || 1
          });

          // Fit image if dimensions specified
          if (properties.width && properties.height) {
            img.scaleToWidth(properties.width);
            if (img.getScaledHeight() > properties.height) {
              img.scaleToHeight(properties.height);
            }
          }

          resolve(img);
        } else {
          reject(new Error(`Failed to load image: ${src}`));
        }
      }, {
        crossOrigin: 'anonymous'
      });
    });
  }

  // Set text content
  async setText(text, targetId = null) {
    try {
      const textObjects = this.canvas.getObjects().filter(obj => 
        obj.type === 'text' || obj.type === 'textbox'
      );

      if (textObjects.length === 0) {
        // Create new text object if none exists
        const textObj = new fabric.Textbox(text, {
          left: this.settings.width / 2,
          top: this.settings.height / 2,
          fontSize: 24,
          fontFamily: 'Inter',
          fill: '#000000',
          textAlign: 'center',
          originX: 'center',
          originY: 'center'
        });

        this.canvas.add(textObj);
      } else {
        // Update existing text objects
        if (targetId) {
          const targetObj = textObjects.find(obj => obj.templateId === targetId);
          if (targetObj) {
            targetObj.set('text', text);
          }
        } else {
          // Update first text object
          textObjects[0].set('text', text);
        }
      }

      this.canvas.renderAll();
      this.saveState();

      performanceMonitor.logEvent('text_updated', {
        textLength: text.length,
        objectCount: textObjects.length
      });

    } catch (error) {
      console.error('Failed to set text:', error);
      throw error;
    }
  }

  // Apply settings to canvas
  async applySettings(settings) {
    try {
      if (settings.backgroundColor) {
        this.canvas.setBackgroundColor(settings.backgroundColor, () => {
          this.canvas.renderAll();
        });
      }

      if (settings.width && settings.height) {
        this.canvas.setDimensions({
          width: settings.width,
          height: settings.height
        });
        this.settings.width = settings.width;
        this.settings.height = settings.height;
      }

      // Apply text settings to selected objects
      const activeObject = this.canvas.getActiveObject();
      if (activeObject && (activeObject.type === 'text' || activeObject.type === 'textbox')) {
        if (settings.fontSize) activeObject.set('fontSize', settings.fontSize);
        if (settings.fontFamily) activeObject.set('fontFamily', settings.fontFamily);
        if (settings.textColor) activeObject.set('fill', settings.textColor);
        if (settings.textAlign) activeObject.set('textAlign', settings.textAlign);
      }

      this.settings = { ...this.settings, ...settings };
      this.canvas.renderAll();
      this.saveState();

    } catch (error) {
      console.error('Failed to apply settings:', error);
      throw error;
    }
  }

  // Save current state for undo/redo
  saveState() {
    try {
      const state = JSON.stringify(this.canvas.toJSON(['templateId', 'templateType']));
      
      // Remove states after current index
      this.history = this.history.slice(0, this.historyIndex + 1);
      
      // Add new state
      this.history.push(state);
      this.historyIndex++;

      // Limit history size
      if (this.history.length > this.maxHistory) {
        this.history.shift();
        this.historyIndex--;
      }

    } catch (error) {
      console.error('Failed to save state:', error);
    }
  }

  // Undo last action
  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.loadState(this.history[this.historyIndex]);
      performanceMonitor.logEvent('undo_action');
    }
  }

  // Redo last undone action
  redo() {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.loadState(this.history[this.historyIndex]);
      performanceMonitor.logEvent('redo_action');
    }
  }

  // Load state from history
  loadState(state) {
    try {
      this.canvas.loadFromJSON(state, () => {
        this.canvas.renderAll();
      });
    } catch (error) {
      console.error('Failed to load state:', error);
    }
  }

  // Export canvas as image
  async exportImage(format = 'png', quality = 1, dimensions = null) {
    try {
      performanceMonitor.startTimer('image_export');

      const originalDimensions = {
        width: this.canvas.width,
        height: this.canvas.height
      };

      // Temporarily resize if different dimensions requested
      if (dimensions) {
        this.canvas.setDimensions(dimensions);
      }

      const dataURL = this.canvas.toDataURL({
        format: format,
        quality: quality,
        multiplier: quality >= 1 ? 2 : 1 // 2x for high quality
      });

      // Restore original dimensions
      if (dimensions) {
        this.canvas.setDimensions(originalDimensions);
      }

      performanceMonitor.endTimer('image_export');
      performanceMonitor.logEvent('image_exported', {
        format,
        quality,
        dimensions: dimensions || originalDimensions
      });

      return dataURL;

    } catch (error) {
      console.error('Failed to export image:', error);
      performanceMonitor.logError(error, { context: 'image_export' });
      throw error;
    }
  }

  // Get canvas data for saving
  async getCanvasData() {
    try {
      return this.canvas.toJSON(['templateId', 'templateType']);
    } catch (error) {
      console.error('Failed to get canvas data:', error);
      throw error;
    }
  }

  // Get current settings
  getSettings() {
    return { ...this.settings };
  }

  // Trigger render with performance monitoring
  triggerRender() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    this.animationFrame = requestAnimationFrame(() => {
      const startTime = performance.now();
      this.canvas.renderAll();
      const renderTime = performance.now() - startTime;

      // Notify render callbacks
      this.renderCallbacks.forEach(callback => {
        try {
          callback(renderTime);
        } catch (error) {
          console.error('Render callback error:', error);
        }
      });
    });
  }

  // Add render callback
  onRender(callback) {
    this.renderCallbacks.push(callback);
  }

  // Remove render callback
  offRender(callback) {
    const index = this.renderCallbacks.indexOf(callback);
    if (index > -1) {
      this.renderCallbacks.splice(index, 1);
    }
  }

  // Event handlers
  onObjectSelected(objects) {
    performanceMonitor.logEvent('object_selected', {
      count: objects.length,
      types: objects.map(obj => obj.type)
    });
  }

  onObjectDeselected() {
    performanceMonitor.logEvent('object_deselected');
  }

  onAfterRender() {
    // Performance monitoring can be added here
  }

  // Cleanup
  dispose() {
    if (this.canvas) {
      this.canvas.dispose();
    }
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }
    this.renderCallbacks = [];
  }
}
