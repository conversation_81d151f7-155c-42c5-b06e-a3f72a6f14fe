import { performanceMonitor } from '../utils/performanceMonitor.js';

export class ExportEngine {
  constructor() {
    this.isInitialized = false;
    this.exportFormats = new Map();
    this.platformOptimizations = new Map();
    this.qualityPresets = new Map();
    this.watermarkSettings = null;
    
    this.initializeExportEngine();
  }

  // Initialize export engine with formats and optimizations
  initializeExportEngine() {
    // Define export formats
    this.exportFormats.set('png', {
      mimeType: 'image/png',
      extension: 'png',
      quality: 1.0,
      supportsTransparency: true,
      compression: 'lossless',
      maxSize: 10 * 1024 * 1024 // 10MB
    });

    this.exportFormats.set('jpeg', {
      mimeType: 'image/jpeg',
      extension: 'jpg',
      quality: 0.9,
      supportsTransparency: false,
      compression: 'lossy',
      maxSize: 5 * 1024 * 1024 // 5MB
    });

    this.exportFormats.set('webp', {
      mimeType: 'image/webp',
      extension: 'webp',
      quality: 0.85,
      supportsTransparency: true,
      compression: 'lossy',
      maxSize: 3 * 1024 * 1024 // 3MB
    });

    this.exportFormats.set('svg', {
      mimeType: 'image/svg+xml',
      extension: 'svg',
      quality: 1.0,
      supportsTransparency: true,
      compression: 'vector',
      maxSize: 1 * 1024 * 1024 // 1MB
    });

    // Platform-specific optimizations
    this.platformOptimizations.set('twitter', {
      dimensions: { width: 1200, height: 675 },
      aspectRatio: 16/9,
      maxFileSize: 5 * 1024 * 1024,
      recommendedFormat: 'jpeg',
      colorProfile: 'sRGB',
      dpi: 72
    });

    this.platformOptimizations.set('instagram-post', {
      dimensions: { width: 1080, height: 1080 },
      aspectRatio: 1/1,
      maxFileSize: 30 * 1024 * 1024,
      recommendedFormat: 'jpeg',
      colorProfile: 'sRGB',
      dpi: 72
    });

    this.platformOptimizations.set('instagram-story', {
      dimensions: { width: 1080, height: 1920 },
      aspectRatio: 9/16,
      maxFileSize: 30 * 1024 * 1024,
      recommendedFormat: 'jpeg',
      colorProfile: 'sRGB',
      dpi: 72
    });

    this.platformOptimizations.set('linkedin', {
      dimensions: { width: 1200, height: 627 },
      aspectRatio: 1.91/1,
      maxFileSize: 20 * 1024 * 1024,
      recommendedFormat: 'png',
      colorProfile: 'sRGB',
      dpi: 72
    });

    this.platformOptimizations.set('facebook', {
      dimensions: { width: 1200, height: 630 },
      aspectRatio: 1.91/1,
      maxFileSize: 8 * 1024 * 1024,
      recommendedFormat: 'jpeg',
      colorProfile: 'sRGB',
      dpi: 72
    });

    this.platformOptimizations.set('pinterest', {
      dimensions: { width: 1000, height: 1500 },
      aspectRatio: 2/3,
      maxFileSize: 20 * 1024 * 1024,
      recommendedFormat: 'png',
      colorProfile: 'sRGB',
      dpi: 72
    });

    // Quality presets
    this.qualityPresets.set('web', {
      quality: 0.8,
      dpi: 72,
      compression: 'optimized',
      description: 'Optimized for web sharing'
    });

    this.qualityPresets.set('print', {
      quality: 1.0,
      dpi: 300,
      compression: 'minimal',
      description: 'High quality for printing'
    });

    this.qualityPresets.set('social', {
      quality: 0.85,
      dpi: 72,
      compression: 'balanced',
      description: 'Balanced quality for social media'
    });

    this.isInitialized = true;
  }

  // Export canvas to specified format
  async exportCanvas(canvas, options = {}) {
    try {
      performanceMonitor.startTimer('canvas_export');

      if (!canvas) {
        throw new Error('Canvas element is required');
      }

      const exportOptions = {
        format: 'png',
        quality: 0.9,
        width: null,
        height: null,
        platform: null,
        preset: 'web',
        addWatermark: false,
        filename: null,
        ...options
      };

      // Apply platform optimizations if specified
      if (exportOptions.platform) {
        const platformConfig = this.platformOptimizations.get(exportOptions.platform);
        if (platformConfig) {
          exportOptions.width = exportOptions.width || platformConfig.dimensions.width;
          exportOptions.height = exportOptions.height || platformConfig.dimensions.height;
          exportOptions.format = exportOptions.format || platformConfig.recommendedFormat;
        }
      }

      // Apply quality preset
      const preset = this.qualityPresets.get(exportOptions.preset);
      if (preset) {
        exportOptions.quality = exportOptions.quality || preset.quality;
      }

      // Get format configuration
      const formatConfig = this.exportFormats.get(exportOptions.format);
      if (!formatConfig) {
        throw new Error(`Unsupported export format: ${exportOptions.format}`);
      }

      // Create export canvas
      const exportCanvas = await this.prepareExportCanvas(canvas, exportOptions);

      // Add watermark if requested
      if (exportOptions.addWatermark && this.watermarkSettings) {
        await this.addWatermark(exportCanvas, this.watermarkSettings);
      }

      // Generate the export data
      const exportData = await this.generateExportData(exportCanvas, formatConfig, exportOptions);

      // Validate file size
      if (exportData.size > formatConfig.maxSize) {
        console.warn(`Export file size (${Math.round(exportData.size / 1024)}KB) exceeds recommended maximum (${Math.round(formatConfig.maxSize / 1024)}KB)`);
      }

      performanceMonitor.endTimer('canvas_export');
      performanceMonitor.logEvent('canvas_exported', {
        format: exportOptions.format,
        platform: exportOptions.platform,
        width: exportOptions.width,
        height: exportOptions.height,
        fileSize: exportData.size,
        quality: exportOptions.quality
      });

      return {
        blob: exportData.blob,
        dataUrl: exportData.dataUrl,
        filename: exportOptions.filename || this.generateFilename(exportOptions),
        format: exportOptions.format,
        size: exportData.size,
        dimensions: {
          width: exportCanvas.width,
          height: exportCanvas.height
        },
        metadata: {
          platform: exportOptions.platform,
          preset: exportOptions.preset,
          quality: exportOptions.quality,
          exportTime: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Canvas export failed:', error);
      performanceMonitor.logError(error, { context: 'canvas_export' });
      throw error;
    }
  }

  // Prepare canvas for export with proper dimensions and quality
  async prepareExportCanvas(sourceCanvas, options) {
    const exportCanvas = document.createElement('canvas');
    const ctx = exportCanvas.getContext('2d');

    // Set canvas dimensions
    const sourceWidth = sourceCanvas.width || sourceCanvas.getWidth?.();
    const sourceHeight = sourceCanvas.height || sourceCanvas.getHeight?.();

    let targetWidth = options.width || sourceWidth;
    let targetHeight = options.height || sourceHeight;

    // Maintain aspect ratio if only one dimension is specified
    if (options.width && !options.height) {
      targetHeight = (sourceHeight / sourceWidth) * targetWidth;
    } else if (options.height && !options.width) {
      targetWidth = (sourceWidth / sourceHeight) * targetHeight;
    }

    // Set high-DPI rendering for better quality
    const pixelRatio = window.devicePixelRatio || 1;
    const scaleFactor = Math.min(2, pixelRatio); // Cap at 2x for performance

    exportCanvas.width = targetWidth * scaleFactor;
    exportCanvas.height = targetHeight * scaleFactor;
    exportCanvas.style.width = targetWidth + 'px';
    exportCanvas.style.height = targetHeight + 'px';

    // Configure context for high quality
    ctx.scale(scaleFactor, scaleFactor);
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Fill background if format doesn't support transparency
    const formatConfig = this.exportFormats.get(options.format);
    if (!formatConfig.supportsTransparency) {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, targetWidth, targetHeight);
    }

    // Draw the source canvas
    if (sourceCanvas.toDataURL) {
      // Standard canvas element
      ctx.drawImage(sourceCanvas, 0, 0, targetWidth, targetHeight);
    } else if (sourceCanvas.toCanvasElement) {
      // Fabric.js canvas
      const fabricCanvas = sourceCanvas.toCanvasElement(scaleFactor);
      ctx.drawImage(fabricCanvas, 0, 0, targetWidth, targetHeight);
    } else {
      throw new Error('Unsupported canvas type');
    }

    return exportCanvas;
  }

  // Add watermark to canvas
  async addWatermark(canvas, watermarkSettings) {
    const ctx = canvas.getContext('2d');
    const { text, position, opacity, fontSize, color } = watermarkSettings;

    ctx.save();
    ctx.globalAlpha = opacity || 0.3;
    ctx.fillStyle = color || '#000000';
    ctx.font = `${fontSize || 16}px Arial`;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'bottom';

    // Position watermark
    let x, y;
    switch (position) {
      case 'top-left':
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        x = 20;
        y = 20;
        break;
      case 'top-right':
        ctx.textAlign = 'right';
        ctx.textBaseline = 'top';
        x = canvas.width - 20;
        y = 20;
        break;
      case 'bottom-left':
        ctx.textAlign = 'left';
        ctx.textBaseline = 'bottom';
        x = 20;
        y = canvas.height - 20;
        break;
      case 'bottom-right':
      default:
        ctx.textAlign = 'right';
        ctx.textBaseline = 'bottom';
        x = canvas.width - 20;
        y = canvas.height - 20;
        break;
    }

    ctx.fillText(text, x, y);
    ctx.restore();
  }

  // Generate export data (blob and data URL)
  async generateExportData(canvas, formatConfig, options) {
    return new Promise((resolve, reject) => {
      try {
        // Generate data URL
        const dataUrl = canvas.toDataURL(formatConfig.mimeType, options.quality);
        
        // Convert to blob
        canvas.toBlob((blob) => {
          if (blob) {
            resolve({
              blob: blob,
              dataUrl: dataUrl,
              size: blob.size
            });
          } else {
            reject(new Error('Failed to generate blob'));
          }
        }, formatConfig.mimeType, options.quality);

      } catch (error) {
        reject(error);
      }
    });
  }

  // Generate filename for export
  generateFilename(options) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const platform = options.platform ? `_${options.platform}` : '';
    const format = options.format || 'png';
    
    return `quotecanvas_${timestamp}${platform}.${format}`;
  }

  // Download exported file
  async downloadExport(exportResult) {
    try {
      const url = URL.createObjectURL(exportResult.blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = exportResult.filename;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up object URL
      setTimeout(() => URL.revokeObjectURL(url), 1000);

      performanceMonitor.logEvent('export_downloaded', {
        filename: exportResult.filename,
        format: exportResult.format,
        size: exportResult.size
      });

      return true;

    } catch (error) {
      console.error('Download failed:', error);
      performanceMonitor.logError(error, { context: 'export_download' });
      throw error;
    }
  }

  // Copy export to clipboard
  async copyToClipboard(exportResult) {
    try {
      if (!navigator.clipboard || !navigator.clipboard.write) {
        throw new Error('Clipboard API not supported');
      }

      const clipboardItem = new ClipboardItem({
        [exportResult.blob.type]: exportResult.blob
      });

      await navigator.clipboard.write([clipboardItem]);

      performanceMonitor.logEvent('export_copied_to_clipboard', {
        format: exportResult.format,
        size: exportResult.size
      });

      return true;

    } catch (error) {
      console.error('Copy to clipboard failed:', error);
      performanceMonitor.logError(error, { context: 'clipboard_copy' });
      throw error;
    }
  }

  // Share export using Web Share API
  async shareExport(exportResult, shareData = {}) {
    try {
      if (!navigator.share) {
        throw new Error('Web Share API not supported');
      }

      const file = new File([exportResult.blob], exportResult.filename, {
        type: exportResult.blob.type
      });

      const shareOptions = {
        title: shareData.title || 'Created with QuoteCanvas Pro',
        text: shareData.text || 'Check out this design I created!',
        files: [file],
        ...shareData
      };

      await navigator.share(shareOptions);

      performanceMonitor.logEvent('export_shared', {
        format: exportResult.format,
        size: exportResult.size,
        platform: 'native_share'
      });

      return true;

    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Share failed:', error);
        performanceMonitor.logError(error, { context: 'export_share' });
      }
      throw error;
    }
  }

  // Batch export for multiple formats/platforms
  async batchExport(canvas, exportConfigs) {
    const results = [];
    const errors = [];

    for (const config of exportConfigs) {
      try {
        const result = await this.exportCanvas(canvas, config);
        results.push({
          config: config,
          result: result,
          success: true
        });
      } catch (error) {
        errors.push({
          config: config,
          error: error,
          success: false
        });
      }
    }

    performanceMonitor.logEvent('batch_export_completed', {
      totalConfigs: exportConfigs.length,
      successCount: results.length,
      errorCount: errors.length
    });

    return {
      results: results,
      errors: errors,
      success: errors.length === 0
    };
  }

  // Get platform-specific export recommendations
  getPlatformRecommendations(platform) {
    const platformConfig = this.platformOptimizations.get(platform);
    if (!platformConfig) {
      return null;
    }

    return {
      platform: platform,
      dimensions: platformConfig.dimensions,
      aspectRatio: platformConfig.aspectRatio,
      maxFileSize: platformConfig.maxFileSize,
      recommendedFormat: platformConfig.recommendedFormat,
      colorProfile: platformConfig.colorProfile,
      dpi: platformConfig.dpi,
      tips: this.getPlatformTips(platform)
    };
  }

  // Get platform-specific tips
  getPlatformTips(platform) {
    const tips = {
      'twitter': [
        'Use high contrast text for better readability',
        'Keep important content away from edges',
        'Consider mobile viewing experience'
      ],
      'instagram-post': [
        'Square format works best for feed posts',
        'Use bright, eye-catching colors',
        'Keep text large and readable on mobile'
      ],
      'instagram-story': [
        'Vertical format optimized for mobile',
        'Place important content in the center',
        'Use story-specific features like polls or questions'
      ],
      'linkedin': [
        'Professional appearance is important',
        'Include your company branding',
        'Use business-appropriate colors and fonts'
      ],
      'facebook': [
        'Images with text should have minimal text overlay',
        'Use engaging visuals to stop scroll',
        'Consider both desktop and mobile viewing'
      ],
      'pinterest': [
        'Vertical format performs better',
        'Use bright, high-contrast images',
        'Include descriptive text overlay'
      ]
    };

    return tips[platform] || [];
  }

  // Set watermark settings
  setWatermarkSettings(settings) {
    this.watermarkSettings = {
      text: 'QuoteCanvas Pro',
      position: 'bottom-right',
      opacity: 0.3,
      fontSize: 16,
      color: '#000000',
      ...settings
    };
  }

  // Get available export formats
  getAvailableFormats() {
    return Array.from(this.exportFormats.keys());
  }

  // Get available platforms
  getAvailablePlatforms() {
    return Array.from(this.platformOptimizations.keys());
  }

  // Get quality presets
  getQualityPresets() {
    return Array.from(this.qualityPresets.entries()).map(([key, preset]) => ({
      key: key,
      ...preset
    }));
  }

  // Validate export options
  validateExportOptions(options) {
    const errors = [];

    if (options.format && !this.exportFormats.has(options.format)) {
      errors.push(`Unsupported format: ${options.format}`);
    }

    if (options.platform && !this.platformOptimizations.has(options.platform)) {
      errors.push(`Unsupported platform: ${options.platform}`);
    }

    if (options.quality && (options.quality < 0 || options.quality > 1)) {
      errors.push('Quality must be between 0 and 1');
    }

    if (options.width && options.width < 1) {
      errors.push('Width must be greater than 0');
    }

    if (options.height && options.height < 1) {
      errors.push('Height must be greater than 0');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  // Get export statistics
  getExportStats() {
    // This would typically come from analytics/storage
    return {
      totalExports: 0,
      formatBreakdown: {},
      platformBreakdown: {},
      averageFileSize: 0,
      popularFormats: [],
      popularPlatforms: []
    };
  }
}
