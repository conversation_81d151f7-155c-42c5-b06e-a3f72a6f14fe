<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="The Ultimate Text-to-Image Social Media Dominator - Create viral quotes and social media images instantly" />
  <meta name="keywords" content="text to image, social media, quotes, viral content, canvas editor" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://quotecanvaspro.com/" />
  <meta property="og:title" content="QuoteCanvas Pro - Ultimate Text-to-Image Generator" />
  <meta property="og:description" content="Create viral social media images instantly. 50+ templates, multi-platform export, offline capable." />
  <meta property="og:image" content="/og-image.png" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://quotecanvaspro.com/" />
  <meta property="twitter:title" content="QuoteCanvas Pro - Ultimate Text-to-Image Generator" />
  <meta property="twitter:description" content="Create viral social media images instantly. 50+ templates, multi-platform export, offline capable." />
  <meta property="twitter:image" content="/og-image.png" />

  <!-- Icons -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  <link rel="manifest" href="/manifest.json" />

  <!-- Preconnect to external resources -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  
  <!-- Critical CSS for performance -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      overflow-x: hidden;
    }
    
    #root {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: white;
      margin-top: 20px;
      font-size: 18px;
      font-weight: 500;
    }
  </style>

  <title>QuoteCanvas Pro - Ultimate Text-to-Image Generator</title>
</head>
<body>
  <div id="root">
    <div class="loading-screen" id="loading-screen">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading QuoteCanvas Pro...</div>
      </div>
    </div>
  </div>
  
  <script>
    // Remove loading screen when app loads
    window.addEventListener('load', () => {
      setTimeout(() => {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
          loadingScreen.style.opacity = '0';
          setTimeout(() => {
            loadingScreen.remove();
          }, 500);
        }
      }, 1000);
    });
    
    // Register service worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
  
  <script type="module" src="/src/index.jsx"></script>
</body>
</html>
