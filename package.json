{"name": "quotecanvas-pro", "version": "1.0.0", "description": "The Ultimate Text-to-Image Social Media Dominator", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "fabric": "^5.3.0", "dexie": "^3.2.4", "html2canvas": "^1.4.1", "canvas-to-blob": "^1.0.0", "file-saver": "^2.0.5", "react-color": "^2.19.3", "react-draggable": "^4.4.5", "react-hotkeys-hook": "^4.4.1", "uuid": "^9.0.0", "lodash": "^4.17.21", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "vite-plugin-pwa": "^0.16.4", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vitest": "^0.34.0"}, "keywords": ["text-to-image", "social-media", "canvas", "quotes", "viral-content", "pwa", "offline-first"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/QuoteCanvas-Pro.git"}, "bugs": {"url": "https://github.com/HectorTa1989/QuoteCanvas-Pro/issues"}, "homepage": "https://github.com/HectorTa1989/QuoteCanvas-Pro#readme"}