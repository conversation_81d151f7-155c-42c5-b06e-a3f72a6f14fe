/* Canvas Editor Styles */

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--gray-100);
  overflow: hidden;
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-sticky);
}

.canvas-toolbar-left,
.canvas-toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.canvas-toolbar-center {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.canvas-zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs);
}

.zoom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.zoom-button:hover {
  background-color: var(--gray-200);
}

.zoom-level {
  min-width: 60px;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.canvas-workspace {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: 
    radial-gradient(circle, var(--gray-300) 1px, transparent 1px),
    radial-gradient(circle, var(--gray-300) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.canvas-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform var(--transition-normal);
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.canvas-element {
  display: block;
  max-width: 100%;
  max-height: 100%;
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  background-color: white;
}

.canvas-element:focus {
  outline: none;
  border-color: var(--primary-dark);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

/* Canvas Loading State */
.canvas-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: var(--z-modal);
}

.canvas-loading-content {
  text-align: center;
  color: var(--gray-600);
}

.canvas-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

/* Canvas Properties Panel */
.canvas-properties {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  width: 280px;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-popover);
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
}

.properties-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.properties-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.properties-body {
  padding: var(--spacing-md);
}

.property-group {
  margin-bottom: var(--spacing-lg);
}

.property-group:last-child {
  margin-bottom: 0;
}

.property-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-sm);
}

.property-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.property-input {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  transition: border-color var(--transition-fast);
}

.property-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.color-picker-wrapper {
  position: relative;
  display: inline-block;
}

.color-picker-button {
  width: 32px;
  height: 32px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: border-color var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.color-picker-button:hover {
  border-color: var(--primary-color);
}

.color-picker-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
}

/* Font Controls */
.font-family-select {
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  background-color: white;
}

.font-size-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.font-size-input {
  width: 60px;
  text-align: center;
}

.font-style-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.font-style-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--gray-300);
  background-color: white;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: bold;
}

.font-style-button:hover {
  background-color: var(--gray-50);
}

.font-style-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.text-align-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.text-align-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--gray-300);
  background-color: white;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.text-align-button:hover {
  background-color: var(--gray-50);
}

.text-align-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Layer Panel */
.layers-panel {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  width: 250px;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-popover);
  max-height: 300px;
  overflow-y: auto;
}

.layers-header {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.layers-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.layers-list {
  padding: var(--spacing-sm);
}

.layer-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.layer-item:hover {
  background-color: var(--gray-50);
}

.layer-item.active {
  background-color: var(--primary-color);
  color: white;
}

.layer-visibility {
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.layer-visibility:hover {
  opacity: 1;
}

.layer-name {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.layer-type {
  font-size: 0.75rem;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Canvas Shortcuts */
.canvas-shortcuts {
  position: absolute;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
  z-index: var(--z-tooltip);
}

.canvas-shortcuts.visible {
  opacity: 1;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.shortcut-item:last-child {
  margin-bottom: 0;
}

.shortcut-key {
  font-family: var(--font-family-mono);
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  margin-left: var(--spacing-sm);
}

/* Canvas Performance Indicator */
.canvas-performance {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-family: var(--font-family-mono);
  z-index: var(--z-tooltip);
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.canvas-performance:hover {
  opacity: 1;
}

.performance-metric {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-sm);
}

.performance-value {
  font-weight: 600;
}

.performance-value.good {
  color: var(--success-color);
}

.performance-value.warning {
  color: var(--warning-color);
}

.performance-value.error {
  color: var(--error-color);
}

/* Canvas Responsive Behavior */
@media (max-width: 768px) {
  .canvas-properties {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    max-width: 320px;
    border-radius: 0;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
  }

  .canvas-properties.open {
    transform: translateX(0);
  }

  .layers-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 50vh;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
  }

  .layers-panel.open {
    transform: translateY(0);
  }

  .canvas-toolbar {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .canvas-toolbar-center {
    order: 3;
    width: 100%;
    justify-content: center;
    margin-top: var(--spacing-sm);
  }

  .canvas-shortcuts {
    display: none;
  }

  .canvas-performance {
    position: relative;
    top: auto;
    left: auto;
    background-color: transparent;
    color: var(--gray-600);
    padding: 0;
    opacity: 1;
  }
}

/* Canvas Animations */
@keyframes canvas-fade-in {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.canvas-wrapper.animate-in {
  animation: canvas-fade-in 0.3s ease-out;
}

@keyframes property-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.canvas-properties.animate-in {
  animation: property-slide-in 0.3s ease-out;
}

/* Canvas Selection Styles */
.fabric-selection {
  border: 2px solid var(--primary-color) !important;
  border-radius: 4px;
}

.fabric-selection-controls {
  background-color: var(--primary-color) !important;
  border: 1px solid var(--primary-dark) !important;
}

.fabric-selection-controls:hover {
  background-color: var(--primary-dark) !important;
}

/* Canvas Grid */
.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  opacity: 0.3;
  background-image: 
    linear-gradient(var(--gray-300) 1px, transparent 1px),
    linear-gradient(90deg, var(--gray-300) 1px, transparent 1px);
  background-size: 20px 20px;
  transition: opacity var(--transition-fast);
}

.canvas-grid.visible {
  opacity: 0.5;
}

/* Canvas Rulers */
.canvas-ruler {
  background-color: var(--gray-100);
  border: 1px solid var(--gray-200);
  font-size: 0.75rem;
  color: var(--gray-600);
  user-select: none;
}

.canvas-ruler-horizontal {
  height: 20px;
  border-bottom: none;
}

.canvas-ruler-vertical {
  width: 20px;
  border-right: none;
}

.canvas-ruler-corner {
  width: 20px;
  height: 20px;
  background-color: var(--gray-200);
  border-bottom: 1px solid var(--gray-300);
  border-right: 1px solid var(--gray-300);
}
