import { performanceMonitor } from '../utils/performanceMonitor.js';

export class AIOptimizer {
  constructor() {
    this.isInitialized = false;
    this.engagementPatterns = new Map();
    this.hashtagDatabase = new Map();
    this.trendingTopics = [];
    this.platformOptimizations = new Map();
    
    this.initializeOptimizations();
  }

  // Initialize AI optimization patterns
  initializeOptimizations() {
    // High-engagement text patterns
    this.engagementPatterns.set('question_hooks', [
      'What if I told you',
      'Have you ever wondered',
      'Why do most people',
      'What would happen if',
      'Did you know that'
    ]);

    this.engagementPatterns.set('power_words', [
      'Revolutionary', 'Breakthrough', 'Exclusive', 'Proven', 'Ultimate',
      'Secret', 'Instant', 'Guaranteed', 'Amazing', 'Incredible',
      'Powerful', 'Essential', 'Critical', 'Urgent', 'Limited'
    ]);

    this.engagementPatterns.set('emotional_triggers', [
      'Transform your life', 'Change everything', 'Never again',
      'Finally revealed', 'The truth about', 'Hidden secrets',
      'Shocking discovery', 'Life-changing', 'Game-changer'
    ]);

    // Platform-specific optimizations
    this.platformOptimizations.set('twitter', {
      maxLength: 280,
      optimalLength: 100,
      hashtagLimit: 2,
      engagementTimes: ['9:00', '12:00', '17:00', '19:00'],
      bestFormats: ['question', 'statistic', 'quote', 'thread']
    });

    this.platformOptimizations.set('linkedin', {
      maxLength: 3000,
      optimalLength: 150,
      hashtagLimit: 5,
      engagementTimes: ['8:00', '12:00', '17:00'],
      bestFormats: ['insight', 'story', 'tip', 'question']
    });

    this.platformOptimizations.set('instagram', {
      maxLength: 2200,
      optimalLength: 125,
      hashtagLimit: 30,
      engagementTimes: ['11:00', '13:00', '17:00', '19:00'],
      bestFormats: ['story', 'quote', 'tip', 'behind-scenes']
    });

    // Initialize hashtag database
    this.initializeHashtagDatabase();
    
    this.isInitialized = true;
  }

  // Initialize hashtag database with trending and evergreen hashtags
  initializeHashtagDatabase() {
    const hashtagCategories = {
      business: [
        'entrepreneur', 'startup', 'business', 'leadership', 'success',
        'motivation', 'productivity', 'innovation', 'strategy', 'growth',
        'mindset', 'hustle', 'goals', 'achievement', 'inspiration'
      ],
      tech: [
        'technology', 'ai', 'machinelearning', 'coding', 'programming',
        'developer', 'software', 'innovation', 'digital', 'future',
        'automation', 'data', 'cloud', 'cybersecurity', 'blockchain'
      ],
      personal: [
        'selfimprovement', 'personalgrowth', 'mindfulness', 'wellness',
        'mentalhealth', 'lifestyle', 'habits', 'discipline', 'focus',
        'balance', 'happiness', 'confidence', 'resilience', 'purpose'
      ],
      marketing: [
        'marketing', 'socialmedia', 'branding', 'content', 'digital',
        'advertising', 'seo', 'engagement', 'influence', 'viral',
        'storytelling', 'community', 'audience', 'conversion', 'roi'
      ],
      finance: [
        'finance', 'investing', 'money', 'wealth', 'financial', 'budget',
        'savings', 'investment', 'trading', 'crypto', 'stocks',
        'retirement', 'debt', 'income', 'passive'
      ]
    };

    Object.entries(hashtagCategories).forEach(([category, hashtags]) => {
      this.hashtagDatabase.set(category, hashtags);
    });
  }

  // Optimize text for engagement
  async optimizeText(text, options = {}) {
    try {
      performanceMonitor.startTimer('text_optimization');

      const platform = options.platform || 'general';
      const category = options.category || 'general';
      const optimization = {
        originalText: text,
        optimizedText: text,
        suggestions: [],
        hashtags: [],
        engagement_score: 0,
        readability_score: 0,
        platform_score: 0
      };

      // Apply text enhancements
      optimization.optimizedText = await this.enhanceText(text, platform);
      
      // Generate suggestions
      optimization.suggestions = await this.generateSuggestions(text, platform);
      
      // Generate hashtags
      optimization.hashtags = await this.generateHashtags(text, category, platform);
      
      // Calculate scores
      optimization.engagement_score = this.calculateEngagementScore(optimization.optimizedText);
      optimization.readability_score = this.calculateReadabilityScore(optimization.optimizedText);
      optimization.platform_score = this.calculatePlatformScore(optimization.optimizedText, platform);

      performanceMonitor.endTimer('text_optimization');
      performanceMonitor.logEvent('text_optimized', {
        platform,
        category,
        originalLength: text.length,
        optimizedLength: optimization.optimizedText.length,
        engagementScore: optimization.engagement_score
      });

      return optimization;

    } catch (error) {
      console.error('Text optimization failed:', error);
      performanceMonitor.logError(error, { context: 'text_optimization' });
      throw error;
    }
  }

  // Enhance text with engagement patterns
  async enhanceText(text, platform) {
    let enhanced = text;

    // Add power words if missing
    if (!this.containsPowerWords(text)) {
      enhanced = this.addPowerWords(enhanced);
    }

    // Optimize for platform length
    const platformConfig = this.platformOptimizations.get(platform);
    if (platformConfig && enhanced.length > platformConfig.optimalLength) {
      enhanced = this.optimizeLength(enhanced, platformConfig.optimalLength);
    }

    // Add emotional triggers for low-engagement text
    if (this.calculateEngagementScore(enhanced) < 50) {
      enhanced = this.addEmotionalTriggers(enhanced);
    }

    return enhanced;
  }

  // Check if text contains power words
  containsPowerWords(text) {
    const powerWords = this.engagementPatterns.get('power_words');
    const lowerText = text.toLowerCase();
    return powerWords.some(word => lowerText.includes(word.toLowerCase()));
  }

  // Add power words to text
  addPowerWords(text) {
    const powerWords = this.engagementPatterns.get('power_words');
    const randomPowerWord = powerWords[Math.floor(Math.random() * powerWords.length)];
    
    // Try to naturally integrate the power word
    if (text.includes('tips') || text.includes('ways')) {
      return text.replace(/(\d+\s+)(tips|ways)/i, `$1${randomPowerWord.toLowerCase()} $2`);
    }
    
    return `${randomPowerWord}: ${text}`;
  }

  // Add emotional triggers
  addEmotionalTriggers(text) {
    const triggers = this.engagementPatterns.get('emotional_triggers');
    const randomTrigger = triggers[Math.floor(Math.random() * triggers.length)];
    
    // Add trigger as prefix for low-engagement content
    return `${randomTrigger}: ${text}`;
  }

  // Optimize text length for platform
  optimizeLength(text, targetLength) {
    if (text.length <= targetLength) return text;

    // Smart truncation - preserve complete sentences
    const sentences = text.split(/[.!?]+/);
    let optimized = '';
    
    for (const sentence of sentences) {
      if ((optimized + sentence).length <= targetLength - 3) {
        optimized += sentence + '.';
      } else {
        break;
      }
    }

    return optimized || text.substring(0, targetLength - 3) + '...';
  }

  // Generate improvement suggestions
  async generateSuggestions(text, platform) {
    const suggestions = [];
    const platformConfig = this.platformOptimizations.get(platform);

    // Length suggestions
    if (platformConfig) {
      if (text.length > platformConfig.maxLength) {
        suggestions.push({
          type: 'length',
          severity: 'error',
          message: `Text is too long for ${platform}. Maximum: ${platformConfig.maxLength} characters.`,
          suggestion: `Reduce by ${text.length - platformConfig.maxLength} characters.`
        });
      } else if (text.length > platformConfig.optimalLength) {
        suggestions.push({
          type: 'length',
          severity: 'warning',
          message: `Consider shortening for better engagement on ${platform}.`,
          suggestion: `Optimal length: ${platformConfig.optimalLength} characters.`
        });
      }
    }

    // Engagement suggestions
    const engagementScore = this.calculateEngagementScore(text);
    if (engagementScore < 30) {
      suggestions.push({
        type: 'engagement',
        severity: 'warning',
        message: 'Low engagement potential detected.',
        suggestion: 'Consider adding power words, questions, or emotional triggers.'
      });
    }

    // Readability suggestions
    const readabilityScore = this.calculateReadabilityScore(text);
    if (readabilityScore < 60) {
      suggestions.push({
        type: 'readability',
        severity: 'info',
        message: 'Text may be difficult to read.',
        suggestion: 'Use shorter sentences and simpler words for better readability.'
      });
    }

    // Question hook suggestions
    if (!text.includes('?') && text.length > 50) {
      suggestions.push({
        type: 'engagement',
        severity: 'info',
        message: 'Consider adding a question to increase engagement.',
        suggestion: 'Questions encourage audience interaction and comments.'
      });
    }

    return suggestions;
  }

  // Generate relevant hashtags
  async generateHashtags(text, category, platform) {
    const platformConfig = this.platformOptimizations.get(platform);
    const maxHashtags = platformConfig?.hashtagLimit || 5;

    // Get base hashtags from category
    const categoryHashtags = this.hashtagDatabase.get(category) || [];
    
    // Analyze text for relevant keywords
    const textKeywords = this.extractKeywords(text);
    
    // Combine and score hashtags
    const allHashtags = [...categoryHashtags, ...textKeywords];
    const scoredHashtags = allHashtags.map(hashtag => ({
      tag: hashtag,
      score: this.calculateHashtagRelevance(hashtag, text, category)
    }));

    // Sort by relevance and return top hashtags
    const topHashtags = scoredHashtags
      .sort((a, b) => b.score - a.score)
      .slice(0, maxHashtags)
      .map(item => `#${item.tag}`);

    return topHashtags;
  }

  // Extract keywords from text
  extractKeywords(text) {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);

    // Remove common stop words
    const stopWords = ['this', 'that', 'with', 'have', 'will', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'];
    
    return words.filter(word => !stopWords.includes(word));
  }

  // Calculate hashtag relevance score
  calculateHashtagRelevance(hashtag, text, category) {
    let score = 0;
    const lowerText = text.toLowerCase();
    const lowerHashtag = hashtag.toLowerCase();

    // Direct mention in text
    if (lowerText.includes(lowerHashtag)) {
      score += 50;
    }

    // Category relevance
    const categoryHashtags = this.hashtagDatabase.get(category) || [];
    if (categoryHashtags.includes(hashtag)) {
      score += 30;
    }

    // Trending bonus (simulated)
    if (this.trendingTopics.includes(hashtag)) {
      score += 20;
    }

    // Length penalty for very long hashtags
    if (hashtag.length > 15) {
      score -= 10;
    }

    return score;
  }

  // Calculate engagement score (0-100)
  calculateEngagementScore(text) {
    let score = 0;
    const lowerText = text.toLowerCase();

    // Power words bonus
    const powerWords = this.engagementPatterns.get('power_words');
    const powerWordCount = powerWords.filter(word => 
      lowerText.includes(word.toLowerCase())
    ).length;
    score += Math.min(powerWordCount * 10, 30);

    // Question bonus
    if (text.includes('?')) {
      score += 15;
    }

    // Emotional triggers bonus
    const triggers = this.engagementPatterns.get('emotional_triggers');
    const triggerCount = triggers.filter(trigger => 
      lowerText.includes(trigger.toLowerCase())
    ).length;
    score += Math.min(triggerCount * 15, 25);

    // Numbers and statistics bonus
    if (/\d+/.test(text)) {
      score += 10;
    }

    // Call-to-action bonus
    const ctaWords = ['share', 'comment', 'like', 'follow', 'subscribe', 'click', 'learn', 'discover'];
    const ctaCount = ctaWords.filter(word => lowerText.includes(word)).length;
    score += Math.min(ctaCount * 5, 15);

    // Length penalty for very long text
    if (text.length > 500) {
      score -= 10;
    }

    return Math.min(Math.max(score, 0), 100);
  }

  // Calculate readability score (Flesch Reading Ease approximation)
  calculateReadabilityScore(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);

    if (sentences.length === 0 || words.length === 0) return 0;

    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // Simplified Flesch Reading Ease formula
    const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    
    return Math.min(Math.max(score, 0), 100);
  }

  // Count syllables in a word (approximation)
  countSyllables(word) {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }

    // Handle silent 'e'
    if (word.endsWith('e')) {
      count--;
    }

    return Math.max(count, 1);
  }

  // Calculate platform-specific score
  calculatePlatformScore(text, platform) {
    const platformConfig = this.platformOptimizations.get(platform);
    if (!platformConfig) return 50;

    let score = 100;

    // Length scoring
    const lengthRatio = text.length / platformConfig.optimalLength;
    if (lengthRatio > 1.5) {
      score -= 30;
    } else if (lengthRatio > 1.2) {
      score -= 15;
    } else if (lengthRatio < 0.5) {
      score -= 10;
    }

    // Format scoring
    const lowerText = text.toLowerCase();
    const formatBonus = platformConfig.bestFormats.some(format => {
      switch (format) {
        case 'question':
          return text.includes('?');
        case 'statistic':
          return /\d+%|\d+x|\d+ times/.test(text);
        case 'quote':
          return text.includes('"') || text.includes("'");
        case 'tip':
          return lowerText.includes('tip') || lowerText.includes('how to');
        default:
          return false;
      }
    });

    if (formatBonus) {
      score += 10;
    }

    return Math.min(Math.max(score, 0), 100);
  }

  // Get optimal posting times for platform
  getOptimalPostingTimes(platform) {
    const platformConfig = this.platformOptimizations.get(platform);
    return platformConfig?.engagementTimes || ['12:00', '17:00'];
  }

  // Analyze trending topics (would connect to real APIs in production)
  async analyzeTrendingTopics(platform = 'general') {
    // Simulated trending topics - in production, this would fetch from APIs
    const mockTrending = {
      general: ['ai', 'productivity', 'remote', 'sustainability', 'wellness'],
      twitter: ['breaking', 'thread', 'opinion', 'news', 'viral'],
      linkedin: ['leadership', 'career', 'networking', 'industry', 'professional'],
      instagram: ['lifestyle', 'behind', 'story', 'inspiration', 'visual']
    };

    this.trendingTopics = mockTrending[platform] || mockTrending.general;
    
    performanceMonitor.logEvent('trending_topics_analyzed', {
      platform,
      topicCount: this.trendingTopics.length
    });

    return this.trendingTopics;
  }

  // Get content recommendations based on performance data
  async getContentRecommendations(userHistory = []) {
    const recommendations = [];

    // Analyze user's best-performing content
    const topPerforming = userHistory
      .sort((a, b) => (b.engagement || 0) - (a.engagement || 0))
      .slice(0, 5);

    if (topPerforming.length > 0) {
      // Extract patterns from top content
      const commonWords = this.findCommonWords(topPerforming.map(p => p.text));
      const commonHashtags = this.findCommonHashtags(topPerforming.map(p => p.hashtags || []));

      recommendations.push({
        type: 'content_pattern',
        title: 'Replicate Your Success',
        description: 'Your best content often includes these elements:',
        suggestions: [
          `Common words: ${commonWords.slice(0, 3).join(', ')}`,
          `Effective hashtags: ${commonHashtags.slice(0, 3).join(', ')}`,
          `Average length: ${Math.round(topPerforming.reduce((sum, p) => sum + p.text.length, 0) / topPerforming.length)} characters`
        ]
      });
    }

    // Time-based recommendations
    recommendations.push({
      type: 'timing',
      title: 'Optimal Posting Times',
      description: 'Post at these times for maximum engagement:',
      suggestions: this.getOptimalPostingTimes('general')
    });

    // Trending topic recommendations
    await this.analyzeTrendingTopics();
    recommendations.push({
      type: 'trending',
      title: 'Trending Topics',
      description: 'Consider incorporating these trending topics:',
      suggestions: this.trendingTopics.slice(0, 5)
    });

    return recommendations;
  }

  // Find common words in successful content
  findCommonWords(texts) {
    const wordCount = new Map();
    
    texts.forEach(text => {
      const words = this.extractKeywords(text);
      words.forEach(word => {
        wordCount.set(word, (wordCount.get(word) || 0) + 1);
      });
    });

    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([word]) => word);
  }

  // Find common hashtags in successful content
  findCommonHashtags(hashtagArrays) {
    const hashtagCount = new Map();
    
    hashtagArrays.forEach(hashtags => {
      hashtags.forEach(hashtag => {
        const cleanTag = hashtag.replace('#', '');
        hashtagCount.set(cleanTag, (hashtagCount.get(cleanTag) || 0) + 1);
      });
    });

    return Array.from(hashtagCount.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([hashtag]) => `#${hashtag}`);
  }
}
