import { performanceMonitor } from '../utils/performanceMonitor.js';

export class TemplateEngine {
  constructor() {
    this.templates = new Map();
    this.categories = new Map();
    this.isInitialized = false;
    this.templateCache = new Map();
  }

  // Initialize template engine
  async initialize() {
    try {
      performanceMonitor.startTimer('template_engine_init');

      // Load built-in templates
      await this.loadBuiltInTemplates();

      // Load custom templates from storage
      await this.loadCustomTemplates();

      // Organize templates by categories
      this.organizeTemplatesByCategory();

      this.isInitialized = true;
      performanceMonitor.endTimer('template_engine_init');

      console.log(`Template engine initialized with ${this.templates.size} templates`);
      return true;

    } catch (error) {
      console.error('Failed to initialize template engine:', error);
      performanceMonitor.logError(error, { context: 'template_engine_init' });
      throw error;
    }
  }

  // Load built-in templates
  async loadBuiltInTemplates() {
    const builtInTemplates = [
      // Viral Quote Templates
      {
        id: 'viral-quote-1',
        name: 'Modern Quote Card',
        category: 'viral-quotes',
        description: 'Clean, modern design perfect for inspirational quotes',
        thumbnail: '/templates/viral-quote-1-thumb.png',
        isPremium: false,
        dimensions: { width: 1080, height: 1080 },
        backgroundColor: '#1a1a1a',
        objects: [
          {
            id: 'quote-text',
            type: 'textbox',
            text: 'Your quote here',
            left: 540,
            top: 400,
            width: 800,
            fontSize: 48,
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fill: '#ffffff',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'author-text',
            type: 'text',
            text: '— Author Name',
            left: 540,
            top: 600,
            fontSize: 24,
            fontFamily: 'Inter',
            fontStyle: 'italic',
            fill: '#cccccc',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'accent-line',
            type: 'rect',
            left: 440,
            top: 350,
            width: 200,
            height: 4,
            fill: '#667eea',
            selectable: false
          }
        ],
        styles: {
          textOptimization: true,
          autoResize: true,
          platforms: ['twitter', 'instagram', 'linkedin']
        }
      },

      // Business Insight Templates
      {
        id: 'business-insight-1',
        name: 'Professional Insight',
        category: 'business-insights',
        description: 'Perfect for sharing business tips and insights',
        thumbnail: '/templates/business-insight-1-thumb.png',
        isPremium: false,
        dimensions: { width: 1080, height: 1080 },
        backgroundColor: '#f8fafc',
        objects: [
          {
            id: 'title-text',
            type: 'textbox',
            text: 'Business Insight',
            left: 540,
            top: 200,
            width: 800,
            fontSize: 36,
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fill: '#1e293b',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'content-text',
            type: 'textbox',
            text: 'Share your valuable business insight here. Keep it concise and actionable.',
            left: 540,
            top: 400,
            width: 900,
            fontSize: 28,
            fontFamily: 'Inter',
            fill: '#334155',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'background-shape',
            type: 'rect',
            left: 90,
            top: 150,
            width: 900,
            height: 500,
            fill: '#ffffff',
            stroke: '#e2e8f0',
            strokeWidth: 2,
            rx: 20,
            ry: 20,
            selectable: false
          }
        ]
      },

      // Tech Quote Templates
      {
        id: 'tech-quote-1',
        name: 'Code & Wisdom',
        category: 'tech-quotes',
        description: 'Perfect for tech quotes and programming wisdom',
        thumbnail: '/templates/tech-quote-1-thumb.png',
        isPremium: false,
        dimensions: { width: 1080, height: 1080 },
        backgroundColor: '#0f172a',
        objects: [
          {
            id: 'quote-text',
            type: 'textbox',
            text: '"Code is poetry written in logic."',
            left: 540,
            top: 400,
            width: 800,
            fontSize: 42,
            fontFamily: 'JetBrains Mono',
            fontWeight: 'medium',
            fill: '#00ff88',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'code-block',
            type: 'rect',
            left: 140,
            top: 300,
            width: 800,
            height: 200,
            fill: '#1e293b',
            stroke: '#334155',
            strokeWidth: 1,
            rx: 8,
            ry: 8,
            selectable: false
          },
          {
            id: 'terminal-header',
            type: 'rect',
            left: 140,
            top: 300,
            width: 800,
            height: 40,
            fill: '#374151',
            rx: 8,
            ry: 8,
            selectable: false
          }
        ]
      },

      // Motivational Templates
      {
        id: 'motivational-1',
        name: 'Daily Motivation',
        category: 'motivational',
        description: 'Energizing design for daily motivation posts',
        thumbnail: '/templates/motivational-1-thumb.png',
        isPremium: false,
        dimensions: { width: 1080, height: 1080 },
        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        objects: [
          {
            id: 'main-text',
            type: 'textbox',
            text: 'BELIEVE IN YOURSELF',
            left: 540,
            top: 400,
            width: 900,
            fontSize: 56,
            fontFamily: 'Inter',
            fontWeight: 'black',
            fill: '#ffffff',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            shadow: {
              color: 'rgba(0,0,0,0.3)',
              blur: 10,
              offsetX: 0,
              offsetY: 4
            }
          },
          {
            id: 'subtitle-text',
            type: 'text',
            text: 'You have the power to achieve anything',
            left: 540,
            top: 550,
            fontSize: 24,
            fontFamily: 'Inter',
            fill: '#ffffff',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            opacity: 0.9
          }
        ]
      },

      // Thread Summary Template
      {
        id: 'thread-summary-1',
        name: 'Thread Summary',
        category: 'thread-summaries',
        description: 'Perfect for summarizing Twitter threads',
        thumbnail: '/templates/thread-summary-1-thumb.png',
        isPremium: false,
        dimensions: { width: 1080, height: 1350 }, // Taller for thread content
        backgroundColor: '#ffffff',
        objects: [
          {
            id: 'header-text',
            type: 'text',
            text: 'THREAD SUMMARY',
            left: 540,
            top: 100,
            fontSize: 28,
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fill: '#1da1f2',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'title-text',
            type: 'textbox',
            text: 'Thread Title Here',
            left: 540,
            top: 200,
            width: 900,
            fontSize: 36,
            fontFamily: 'Inter',
            fontWeight: 'bold',
            fill: '#14171a',
            textAlign: 'center',
            originX: 'center',
            originY: 'center'
          },
          {
            id: 'content-text',
            type: 'textbox',
            text: '• Key point one\n• Key point two\n• Key point three\n• Key point four',
            left: 540,
            top: 500,
            width: 800,
            fontSize: 24,
            fontFamily: 'Inter',
            fill: '#14171a',
            textAlign: 'left',
            originX: 'center',
            originY: 'center'
          }
        ]
      }
    ];

    // Add templates to the map
    builtInTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });

    console.log(`Loaded ${builtInTemplates.length} built-in templates`);
  }

  // Load custom templates from storage
  async loadCustomTemplates() {
    try {
      // This would load from IndexedDB in a real implementation
      // For now, we'll just log that custom templates would be loaded here
      console.log('Custom templates loading placeholder');
    } catch (error) {
      console.error('Failed to load custom templates:', error);
    }
  }

  // Organize templates by categories
  organizeTemplatesByCategory() {
    this.categories.clear();

    this.templates.forEach(template => {
      const category = template.category;
      if (!this.categories.has(category)) {
        this.categories.set(category, []);
      }
      this.categories.get(category).push(template);
    });

    // Sort templates within each category by name
    this.categories.forEach(templates => {
      templates.sort((a, b) => a.name.localeCompare(b.name));
    });
  }

  // Get all templates
  getAllTemplates() {
    return Array.from(this.templates.values());
  }

  // Get templates by category
  getTemplatesByCategory(category) {
    return this.categories.get(category) || [];
  }

  // Get all categories
  getCategories() {
    return Array.from(this.categories.keys());
  }

  // Get template by ID
  getTemplate(id) {
    return this.templates.get(id);
  }

  // Search templates
  searchTemplates(query, filters = {}) {
    const allTemplates = this.getAllTemplates();
    let results = allTemplates;

    // Text search
    if (query && query.trim()) {
      const searchTerm = query.toLowerCase().trim();
      results = results.filter(template => 
        template.name.toLowerCase().includes(searchTerm) ||
        template.description.toLowerCase().includes(searchTerm) ||
        template.category.toLowerCase().includes(searchTerm)
      );
    }

    // Category filter
    if (filters.category) {
      results = results.filter(template => template.category === filters.category);
    }

    // Premium filter
    if (filters.isPremium !== undefined) {
      results = results.filter(template => template.isPremium === filters.isPremium);
    }

    // Platform filter
    if (filters.platform) {
      results = results.filter(template => 
        template.styles?.platforms?.includes(filters.platform)
      );
    }

    // Sort results
    if (filters.sortBy) {
      results.sort((a, b) => {
        switch (filters.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'category':
            return a.category.localeCompare(b.category);
          case 'popularity':
            return (b.usage || 0) - (a.usage || 0);
          default:
            return 0;
        }
      });
    }

    performanceMonitor.logEvent('template_search', {
      query,
      filters,
      resultCount: results.length
    });

    return results;
  }

  // Get trending templates
  getTrendingTemplates(limit = 10) {
    const allTemplates = this.getAllTemplates();
    
    // Sort by usage (in a real app, this would be based on actual usage data)
    const trending = allTemplates
      .sort((a, b) => (b.usage || 0) - (a.usage || 0))
      .slice(0, limit);

    return trending;
  }

  // Get recommended templates based on user activity
  getRecommendedTemplates(userActivity = {}, limit = 6) {
    const allTemplates = this.getAllTemplates();
    
    // Simple recommendation based on most used categories
    const categoryPreferences = userActivity.categories || {};
    const preferredCategories = Object.keys(categoryPreferences)
      .sort((a, b) => categoryPreferences[b] - categoryPreferences[a]);

    let recommended = [];
    
    // Get templates from preferred categories first
    preferredCategories.forEach(category => {
      const categoryTemplates = this.getTemplatesByCategory(category);
      recommended.push(...categoryTemplates.slice(0, 2));
    });

    // Fill remaining slots with popular templates
    if (recommended.length < limit) {
      const remaining = limit - recommended.length;
      const popular = this.getTrendingTemplates(remaining * 2);
      const filtered = popular.filter(t => !recommended.find(r => r.id === t.id));
      recommended.push(...filtered.slice(0, remaining));
    }

    return recommended.slice(0, limit);
  }

  // Clone template for customization
  cloneTemplate(templateId, customizations = {}) {
    const original = this.getTemplate(templateId);
    if (!original) {
      throw new Error(`Template ${templateId} not found`);
    }

    const cloned = {
      ...JSON.parse(JSON.stringify(original)),
      id: crypto.randomUUID(),
      name: `${original.name} (Copy)`,
      isCustom: true,
      originalId: templateId,
      ...customizations
    };

    this.templates.set(cloned.id, cloned);
    this.organizeTemplatesByCategory();

    performanceMonitor.logEvent('template_cloned', {
      originalId: templateId,
      newId: cloned.id
    });

    return cloned;
  }

  // Save custom template
  async saveCustomTemplate(template) {
    try {
      template.id = template.id || crypto.randomUUID();
      template.isCustom = true;
      template.createdAt = new Date().toISOString();

      this.templates.set(template.id, template);
      this.organizeTemplatesByCategory();

      // In a real implementation, this would save to IndexedDB
      console.log('Custom template saved:', template.id);

      performanceMonitor.logEvent('custom_template_saved', {
        templateId: template.id,
        category: template.category
      });

      return template;

    } catch (error) {
      console.error('Failed to save custom template:', error);
      throw error;
    }
  }

  // Delete custom template
  async deleteCustomTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (!template || !template.isCustom) {
      throw new Error('Cannot delete built-in template');
    }

    this.templates.delete(templateId);
    this.organizeTemplatesByCategory();

    performanceMonitor.logEvent('custom_template_deleted', {
      templateId
    });

    return true;
  }

  // Get template statistics
  getTemplateStats() {
    const stats = {
      total: this.templates.size,
      categories: this.categories.size,
      premium: 0,
      custom: 0
    };

    this.templates.forEach(template => {
      if (template.isPremium) stats.premium++;
      if (template.isCustom) stats.custom++;
    });

    return stats;
  }

  // Preload template assets
  async preloadTemplateAssets(templateIds = []) {
    const loadPromises = templateIds.map(async (id) => {
      const template = this.getTemplate(id);
      if (!template || this.templateCache.has(id)) return;

      try {
        // Preload images and other assets
        const imageObjects = template.objects?.filter(obj => obj.type === 'image') || [];
        const imagePromises = imageObjects.map(obj => {
          return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => resolve(); // Don't fail on image errors
            img.src = obj.src;
          });
        });

        await Promise.all(imagePromises);
        this.templateCache.set(id, true);

      } catch (error) {
        console.warn(`Failed to preload assets for template ${id}:`, error);
      }
    });

    await Promise.all(loadPromises);
  }
}
