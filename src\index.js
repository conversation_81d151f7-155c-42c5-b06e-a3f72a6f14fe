import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.js';
import './styles/main.css';

// Performance monitoring
import { performanceMonitor } from './utils/performanceMonitor.js';

// Initialize performance monitoring
performanceMonitor.init();

// Error boundary for production
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log error to analytics
    performanceMonitor.logError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <div className="error-container">
            <h1>🚨 Something went wrong</h1>
            <p>We're sorry, but something unexpected happened.</p>
            <details className="error-details">
              <summary>Error Details</summary>
              <pre>{this.state.error && this.state.error.toString()}</pre>
              <pre>{this.state.errorInfo.componentStack}</pre>
            </details>
            <button 
              onClick={() => window.location.reload()}
              className="reload-button"
            >
              Reload App
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Initialize the app
function initializeApp() {
  const container = document.getElementById('root');
  const root = createRoot(container);

  // Render the app with error boundary
  root.render(
    <React.StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </React.StrictMode>
  );

  // Log app initialization
  performanceMonitor.logEvent('app_initialized', {
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    }
  });
}

// Check for browser compatibility
function checkBrowserCompatibility() {
  const requiredFeatures = [
    'Canvas',
    'IndexedDB',
    'ServiceWorker',
    'WebGL',
    'FileReader'
  ];

  const unsupportedFeatures = requiredFeatures.filter(feature => {
    switch (feature) {
      case 'Canvas':
        return !document.createElement('canvas').getContext;
      case 'IndexedDB':
        return !window.indexedDB;
      case 'ServiceWorker':
        return !('serviceWorker' in navigator);
      case 'WebGL':
        const canvas = document.createElement('canvas');
        return !canvas.getContext('webgl') && !canvas.getContext('experimental-webgl');
      case 'FileReader':
        return !window.FileReader;
      default:
        return false;
    }
  });

  if (unsupportedFeatures.length > 0) {
    showCompatibilityWarning(unsupportedFeatures);
    return false;
  }

  return true;
}

// Show compatibility warning
function showCompatibilityWarning(unsupportedFeatures) {
  const warningHTML = `
    <div class="compatibility-warning">
      <div class="warning-container">
        <h1>⚠️ Browser Compatibility Issue</h1>
        <p>Your browser doesn't support some features required for QuoteCanvas Pro:</p>
        <ul>
          ${unsupportedFeatures.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        <p>Please update your browser or try using:</p>
        <ul>
          <li>Chrome 90+</li>
          <li>Firefox 88+</li>
          <li>Safari 14+</li>
          <li>Edge 90+</li>
        </ul>
        <button onclick="window.location.reload()" class="retry-button">
          Retry
        </button>
      </div>
    </div>
  `;

  document.getElementById('root').innerHTML = warningHTML;
}

// Initialize app when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    if (checkBrowserCompatibility()) {
      initializeApp();
    }
  });
} else {
  if (checkBrowserCompatibility()) {
    initializeApp();
  }
}

// Handle app updates
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.addEventListener('controllerchange', () => {
    // Show update notification
    const updateNotification = document.createElement('div');
    updateNotification.className = 'update-notification';
    updateNotification.innerHTML = `
      <div class="update-content">
        <span>🚀 New version available!</span>
        <button onclick="window.location.reload()" class="update-button">
          Update Now
        </button>
      </div>
    `;
    document.body.appendChild(updateNotification);

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (updateNotification.parentNode) {
        updateNotification.remove();
      }
    }, 10000);
  });
}

// Handle online/offline status
window.addEventListener('online', () => {
  performanceMonitor.logEvent('connection_restored');
  
  // Show online notification
  const notification = document.createElement('div');
  notification.className = 'connection-notification online';
  notification.textContent = '🌐 Back online!';
  document.body.appendChild(notification);
  
  setTimeout(() => notification.remove(), 3000);
});

window.addEventListener('offline', () => {
  performanceMonitor.logEvent('connection_lost');
  
  // Show offline notification
  const notification = document.createElement('div');
  notification.className = 'connection-notification offline';
  notification.textContent = '📱 Working offline';
  document.body.appendChild(notification);
  
  setTimeout(() => notification.remove(), 3000);
});

// Handle app installation
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
  e.preventDefault();
  deferredPrompt = e;
  
  // Show install button
  const installButton = document.createElement('button');
  installButton.className = 'install-button';
  installButton.innerHTML = '📱 Install App';
  installButton.onclick = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      performanceMonitor.logEvent('install_prompt_result', { outcome });
      deferredPrompt = null;
      installButton.remove();
    }
  };
  
  document.body.appendChild(installButton);
});

// Log performance metrics
window.addEventListener('load', () => {
  // Use requestIdleCallback for non-critical performance logging
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      performanceMonitor.logPerformanceMetrics();
    });
  } else {
    setTimeout(() => {
      performanceMonitor.logPerformanceMetrics();
    }, 1000);
  }
});

export default initializeApp;
