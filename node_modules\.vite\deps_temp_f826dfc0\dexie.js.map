{"version": 3, "sources": ["../../dexie/src/globals/global.ts", "../../dexie/src/functions/utils.ts", "../../dexie/src/helpers/debug.ts", "../../dexie/src/errors/errors.js", "../../dexie/src/functions/chaining-functions.js", "../../dexie/src/helpers/promise.js", "../../dexie/src/functions/temp-transaction.ts", "../../dexie/src/globals/constants.ts", "../../dexie/src/functions/combine.ts", "../../dexie/src/dbcore/keyrange.ts", "../../dexie/src/functions/workaround-undefined-primkey.ts", "../../dexie/src/classes/table/table.ts", "../../dexie/src/helpers/Events.js", "../../dexie/src/functions/make-class-constructor.ts", "../../dexie/src/classes/table/table-constructor.ts", "../../dexie/src/classes/collection/collection-helpers.ts", "../../dexie/src/functions/cmp.ts", "../../dexie/src/classes/collection/collection.ts", "../../dexie/src/classes/collection/collection-constructor.ts", "../../dexie/src/functions/compare-functions.ts", "../../dexie/src/classes/where-clause/where-clause-helpers.ts", "../../dexie/src/classes/where-clause/where-clause.ts", "../../dexie/src/classes/where-clause/where-clause-constructor.ts", "../../dexie/src/functions/event-wrappers.ts", "../../dexie/src/globals/global-events.ts", "../../dexie/src/classes/transaction/transaction.ts", "../../dexie/src/classes/transaction/transaction-constructor.ts", "../../dexie/src/helpers/index-spec.ts", "../../dexie/src/helpers/table-schema.ts", "../../dexie/src/functions/quirks.ts", "../../dexie/src/dbcore/get-key-extractor.ts", "../../dexie/src/dbcore/dbcore-indexeddb.ts", "../../dexie/src/classes/dexie/generate-middleware-stacks.ts", "../../dexie/src/classes/version/schema-helpers.ts", "../../dexie/src/classes/version/version.ts", "../../dexie/src/classes/version/version-constructor.ts", "../../dexie/src/helpers/database-enumerator.ts", "../../dexie/src/classes/dexie/vip.ts", "../../dexie/node_modules/safari-14-idb-fix/dist/index.js", "../../dexie/src/classes/dexie/dexie-open.ts", "../../dexie/src/helpers/yield-support.ts", "../../dexie/src/classes/dexie/transaction-helpers.ts", "../../dexie/src/dbcore/virtual-index-middleware.ts", "../../dexie/src/functions/get-object-diff.ts", "../../dexie/src/dbcore/get-effective-keys.ts", "../../dexie/src/hooks/hooks-middleware.ts", "../../dexie/src/dbcore/cache-existing-values-middleware.ts", "../../dexie/src/helpers/rangeset.ts", "../../dexie/src/live-query/observability-middleware.ts", "../../dexie/src/classes/dexie/dexie.ts", "../../dexie/src/classes/observable/observable.ts", "../../dexie/src/live-query/extend-observability-set.ts", "../../dexie/src/live-query/live-query.ts", "../../dexie/src/classes/dexie/dexie-dom-dependencies.ts", "../../dexie/src/classes/dexie/dexie-static-props.ts", "../../dexie/src/live-query/propagate-locally.ts", "../../dexie/src/live-query/enable-broadcast.ts", "../../dexie/src/index.ts"], "sourcesContent": ["declare var global;\nexport const _global: any =\n    typeof globalThis !== 'undefined' ? globalThis :\n    typeof self !== 'undefined' ? self :\n    typeof window !== 'undefined' ? window :\n    global;\n", "﻿import { _global } from \"../globals/global\";\nexport const keys = Object.keys;\nexport const isArray = Array.isArray;\nif (typeof Promise !== 'undefined' && !_global.Promise){\n    // In jsdom, this it can be the case that Promise is not put on the global object.\n    // If so, we need to patch the global object for the rest of the code to work as expected.\n    // Other dexie code expects Promise to be on the global object (like normal browser environments)\n    _global.Promise = Promise;\n}\nexport { _global }\n\nexport function extend<T extends object,X extends object>(obj: T, extension: X): T & X  {\n    if (typeof extension !== 'object') return obj as T & X;\n    keys(extension).forEach(function (key) {\n        obj[key] = extension[key];\n    });\n    return obj as T & X;\n}\n\nexport const getProto = Object.getPrototypeOf;\nexport const _hasOwn = {}.hasOwnProperty;\nexport function hasOwn(obj, prop) {\n    return _hasOwn.call(obj, prop);\n}\n\nexport function props (proto, extension) {\n    if (typeof extension === 'function') extension = extension(getProto(proto));\n    (typeof Reflect === \"undefined\" ? keys : Reflect.ownKeys)(extension).forEach(key => {\n        setProp(proto, key, extension[key]);\n    });\n}\n\nexport const defineProperty = Object.defineProperty;\n\nexport function setProp(obj, prop, functionOrGetSet, options?) {\n    defineProperty(obj, prop, extend(functionOrGetSet && hasOwn(functionOrGetSet, \"get\") && typeof functionOrGetSet.get === 'function' ?\n        {get: functionOrGetSet.get, set: functionOrGetSet.set, configurable: true} :\n        {value: functionOrGetSet, configurable: true, writable: true}, options));\n}\n\nexport function derive(Child) {\n    return {\n        from: function (Parent) {\n            Child.prototype = Object.create(Parent.prototype);\n            setProp(Child.prototype, \"constructor\", Child);\n            return {\n                extend: props.bind(null, Child.prototype)\n            };\n        }\n    };\n}\n\nexport const getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\nexport function getPropertyDescriptor(obj, prop) {\n    const pd = getOwnPropertyDescriptor(obj, prop);\n    let proto;\n    return pd || (proto = getProto(obj)) && getPropertyDescriptor (proto, prop);\n}\n\nconst _slice = [].slice;\nexport function slice(args, start?, end?) {\n    return _slice.call(args, start, end);\n}\n\nexport function override(origFunc, overridedFactory) {\n    return overridedFactory(origFunc);\n}\n\nexport function assert (b) {\n    if (!b) throw new Error(\"Assertion Failed\");\n}\n\nexport function asap(fn) {\n    // @ts-ignore\n    if (_global.setImmediate) setImmediate(fn); else setTimeout(fn, 0);\n}\n\nexport function getUniqueArray(a) {\n    return a.filter((value, index, self) => self.indexOf(value) === index);\n}\n\n/** Generate an object (hash map) based on given array.\n * @param extractor Function taking an array item and its index and returning an array of 2 items ([key, value]) to\n *        instert on the resulting object for each item in the array. If this function returns a falsy value, the\n *        current item wont affect the resulting object.\n */\nexport function arrayToObject<T,R> (array: T[], extractor: (x:T, idx: number)=>[string, R]): {[name: string]: R} {\n    return array.reduce((result, item, i) => {\n        var nameAndValue = extractor(item, i);\n        if (nameAndValue) result[nameAndValue[0]] = nameAndValue[1];\n        return result;\n    }, {});\n}\n\nexport function trycatcher(fn, reject) {\n    return function () {\n        try {\n            fn.apply(this, arguments);\n        } catch (e) {\n            reject(e);\n        }\n    };\n}\n\nexport function tryCatch(fn: (...args: any[])=>void, onerror, args?) : void {\n    try {\n        fn.apply(null, args);\n    } catch (ex) {\n        onerror && onerror(ex);\n    }\n}\n\nexport function getByKeyPath(obj, keyPath) {\n    // http://www.w3.org/TR/IndexedDB/#steps-for-extracting-a-key-from-a-value-using-a-key-path\n    if (typeof keyPath === 'string' && hasOwn(obj, keyPath)) return obj[keyPath]; // This line is moved from last to first for optimization purpose.\n    if (!keyPath) return obj;\n    if (typeof keyPath !== 'string') {\n        var rv = [];\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            var val = getByKeyPath(obj, keyPath[i]);\n            rv.push(val);\n        }\n        return rv;\n    }\n    var period = keyPath.indexOf('.');\n    if (period !== -1) {\n        var innerObj = obj[keyPath.substr(0, period)];\n        return innerObj == null ? undefined : getByKeyPath(innerObj, keyPath.substr(period + 1));\n    }\n    return undefined;\n}\n\nexport function setByKeyPath(obj, keyPath, value) {\n    if (!obj || keyPath === undefined) return;\n    if ('isFrozen' in Object && Object.isFrozen(obj)) return;\n    if (typeof keyPath !== 'string' && 'length' in keyPath) {\n        assert(typeof value !== 'string' && 'length' in value);\n        for (var i = 0, l = keyPath.length; i < l; ++i) {\n            setByKeyPath(obj, keyPath[i], value[i]);\n        }\n    } else {\n        var period = keyPath.indexOf('.');\n        if (period !== -1) {\n            var currentKeyPath = keyPath.substr(0, period);\n            var remainingKeyPath = keyPath.substr(period + 1);\n            if (remainingKeyPath === \"\")\n                if (value === undefined) {\n                    if (isArray(obj) && !isNaN(parseInt(currentKeyPath))) obj.splice(currentKeyPath, 1);\n                    else delete obj[currentKeyPath];\n                } else obj[currentKeyPath] = value;\n            else {\n                var innerObj = obj[currentKeyPath];\n                if (!innerObj || !hasOwn(obj, currentKeyPath)) innerObj = (obj[currentKeyPath] = {});\n                setByKeyPath(innerObj, remainingKeyPath, value);\n            }\n        } else {\n            if (value === undefined) {\n                if (isArray(obj) && !isNaN(parseInt(keyPath))) obj.splice(keyPath, 1);\n                else delete obj[keyPath];\n            } else obj[keyPath] = value;\n        }\n    }\n}\n\nexport function delByKeyPath(obj, keyPath) {\n    if (typeof keyPath === 'string')\n        setByKeyPath(obj, keyPath, undefined);\n    else if ('length' in keyPath)\n        [].map.call(keyPath, function(kp) {\n            setByKeyPath(obj, kp, undefined);\n        });\n}\n\nexport function shallowClone(obj) {\n    var rv = {};\n    for (var m in obj) {\n        if (hasOwn(obj, m)) rv[m] = obj[m];\n    }\n    return rv;\n}\n\nconst concat = [].concat;\nexport function flatten<T> (a: (T | T[])[]) : T[] {\n    return concat.apply([], a);\n}\n\n//https://developer.mozilla.org/en-US/docs/Web/API/Web_Workers_API/Structured_clone_algorithm\nconst intrinsicTypeNames =\n    \"BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey\"\n    .split(',').concat(\n        flatten([8,16,32,64].map(num=>[\"Int\",\"Uint\",\"Float\"].map(t=>t+num+\"Array\")))\n    ).filter(t=>_global[t]);\nconst intrinsicTypes = intrinsicTypeNames.map(t=>_global[t]);\nexport const intrinsicTypeNameSet = arrayToObject(intrinsicTypeNames, x=>[x,true]);\n\nlet circularRefs: null | WeakMap<any,any> = null;\nexport function deepClone<T>(any: T): T {\n    circularRefs = typeof WeakMap !== 'undefined' && new WeakMap();\n    const rv = innerDeepClone(any);\n    circularRefs = null;\n    return rv;\n}\n\nfunction innerDeepClone<T>(any: T): T {\n    if (!any || typeof any !== 'object') return any;\n    let rv = circularRefs && circularRefs.get(any); // Resolve circular references\n    if (rv) return rv;\n    if (isArray(any)) {\n        rv = [];\n        circularRefs && circularRefs.set(any, rv);\n        for (var i = 0, l = any.length; i < l; ++i) {\n            rv.push(innerDeepClone(any[i]));\n        }\n    } else if (intrinsicTypes.indexOf(any.constructor) >= 0) {\n        rv = any;\n    } else {\n        const proto = getProto(any);\n        rv = proto === Object.prototype ? {} : Object.create(proto);\n        circularRefs && circularRefs.set(any, rv);\n        for (var prop in any) {\n            if (hasOwn(any, prop)) {\n                rv[prop] = innerDeepClone(any[prop]);\n            }\n        }\n    }\n    return rv;\n}\n\nconst {toString} = {};\nexport function toStringTag(o: Object) {\n    return toString.call(o).slice(8, -1);\n}\n\n// If first argument is iterable or array-like, return it as an array\nexport const iteratorSymbol = typeof Symbol !== 'undefined' ?\n    Symbol.iterator :\n    '@@iterator';\nexport const getIteratorOf = typeof iteratorSymbol === \"symbol\" ? function(x) {\n    var i;\n    return x != null && (i = x[iteratorSymbol]) && i.apply(x);\n} : function () { return null; };\nexport const asyncIteratorSymbol = typeof Symbol !== 'undefined'\n    ? Symbol.asyncIterator || Symbol.for(\"Symbol.asyncIterator\")\n    : '@asyncIterator';\n\nexport const NO_CHAR_ARRAY = {};\n// Takes one or several arguments and returns an array based on the following criteras:\n// * If several arguments provided, return arguments converted to an array in a way that\n//   still allows javascript engine to optimize the code.\n// * If single argument is an array, return a clone of it.\n// * If this-pointer equals NO_CHAR_ARRAY, don't accept strings as valid iterables as a special\n//   case to the two bullets below.\n// * If single argument is an iterable, convert it to an array and return the resulting array.\n// * If single argument is array-like (has length of type number), convert it to an array.\nexport function getArrayOf (arrayLike) {\n    var i, a, x, it;\n    if (arguments.length === 1) {\n        if (isArray(arrayLike)) return arrayLike.slice();\n        if (this === NO_CHAR_ARRAY && typeof arrayLike === 'string') return [arrayLike];\n        if ((it = getIteratorOf(arrayLike))) {\n            a = [];\n            while ((x = it.next()), !x.done) a.push(x.value);\n            return a;\n        }\n        if (arrayLike == null) return [arrayLike];\n        i = arrayLike.length;\n        if (typeof i === 'number') {\n            a = new Array(i);\n            while (i--) a[i] = arrayLike[i];\n            return a;\n        }\n        return [arrayLike];\n    }\n    i = arguments.length;\n    a = new Array(i);\n    while (i--) a[i] = arguments[i];\n    return a;\n}\nexport const isAsyncFunction = typeof Symbol !== 'undefined'\n    ? (fn: Function) => fn[Symbol.toStringTag] === 'AsyncFunction'\n    : ()=>false;\n", "// By default, debug will be true only if platform is a web platform and its page is served from localhost.\n// When debug = true, error's stacks will contain asyncronic long stacks.\nexport var debug = typeof location !== 'undefined' &&\n        // By default, use debug mode if served from localhost.\n        /^(http|https):\\/\\/(localhost|127\\.0\\.0\\.1)/.test(location.href);\n\nexport function setDebug(value, filter) {\n    debug = value;\n    libraryFilter = filter;\n}\n\nexport var libraryFilter = () => true;\n\nexport const NEEDS_THROW_FOR_STACK = !new Error(\"\").stack;\n\nexport function getErrorWithStack() {\n    \"use strict\";\n    if (NEEDS_THROW_FOR_STACK) try {\n        // Doing something naughty in strict mode here to trigger a specific error\n        // that can be explicitely ignored in debugger's exception settings.\n        // If we'd just throw new Error() here, IE's debugger's exception settings\n        // will just consider it as \"exception thrown by javascript code\" which is\n        // something you wouldn't want it to ignore.\n        getErrorWithStack.arguments;\n        throw new Error(); // Fallback if above line don't throw.\n    } catch(e) {\n        return e;\n    }\n    return new Error();\n}\n\nexport function prettyStack(exception, numIgnoredFrames) {\n    var stack = exception.stack;\n    if (!stack) return \"\";\n    numIgnoredFrames = (numIgnoredFrames || 0);\n    if (stack.indexOf(exception.name) === 0)\n        numIgnoredFrames += (exception.name + exception.message).split('\\n').length;\n    return stack.split('\\n')\n        .slice(numIgnoredFrames)\n        .filter(libraryFilter)\n        .map(frame => \"\\n\" + frame)\n        .join('');\n}\n\n// TODO: Replace this in favor of a decorator instead.\nexport function deprecated<T> (what: string, fn: (...args)=>T) {\n    return function () {\n        console.warn(`${what} is deprecated. See https://dexie.org/docs/Deprecations. ${prettyStack(getErrorWithStack(), 1)}`);\n        return fn.apply(this, arguments);\n    } as (...args)=>T\n}\n", "import { derive, setProp } from '../functions/utils';\nimport { getErrorWithStack, prettyStack } from '../helpers/debug';\n\nvar dexieErrorNames = [\n    'Modify',\n    'Bulk',\n    'OpenFailed',\n    'VersionChange',\n    'Schema',\n    'Upgrade',\n    'InvalidTable',\n    'MissingAPI',\n    'NoSuchDatabase',\n    'InvalidArgument',\n    'SubTransaction',\n    'Unsupported',\n    'Internal',\n    'DatabaseClosed',\n    'PrematureCommit',\n    'ForeignAwait'\n];\n\nvar idbDomErrorNames = [\n    'Unknown',\n    'Constraint',\n    'Data',\n    'TransactionInactive',\n    'ReadOnly',\n    'Version',\n    'NotFound',\n    'InvalidState',\n    'InvalidAccess',\n    'Abort',\n    'Timeout',\n    'QuotaExceeded',\n    'Syntax',\n    'DataClone'\n];\n\nvar errorList = dexieErrorNames.concat(idbDomErrorNames);\n\nvar defaultTexts = {\n    VersionChanged: \"Database version changed by other database connection\",\n    DatabaseClosed: \"Database has been closed\",\n    Abort: \"Transaction aborted\",\n    TransactionInactive: \"Transaction has already completed or failed\",\n    MissingAPI: \"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb\"\n};\n\n//\n// DexieError - base class of all out exceptions.\n//\nexport function DexieError (name, msg) {\n    // Reason we don't use ES6 classes is because:\n    // 1. It bloats transpiled code and increases size of minified code.\n    // 2. It doesn't give us much in this case.\n    // 3. It would require sub classes to call super(), which\n    //    is not needed when deriving from Error.\n    this._e = getErrorWithStack();\n    this.name = name;\n    this.message = msg;\n}\n\nderive(DexieError).from(Error).extend({\n    stack: {\n        get: function() {\n            return this._stack ||\n                (this._stack = this.name + \": \" + this.message + prettyStack(this._e, 2));\n        }\n    },\n    toString: function(){ return this.name + \": \" + this.message; }\n});\n\nfunction getMultiErrorMessage (msg, failures) {\n    return msg + \". Errors: \" + Object.keys(failures)\n        .map(key=>failures[key].toString())\n        .filter((v,i,s)=>s.indexOf(v) === i) // Only unique error strings\n        .join('\\n');\n}\n\n//\n// ModifyError - thrown in Collection.modify()\n// Specific constructor because it contains members failures and failedKeys.\n//\nexport function ModifyError (msg, failures, successCount, failedKeys) {\n    this._e = getErrorWithStack();\n    this.failures = failures;\n    this.failedKeys = failedKeys;\n    this.successCount = successCount;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(ModifyError).from(DexieError);\n\nexport function BulkError (msg, failures) {\n    this._e = getErrorWithStack();\n    this.name = \"BulkError\";\n    this.failures = Object.keys(failures).map(pos => failures[pos]);\n    this.failuresByPos = failures;\n    this.message = getMultiErrorMessage(msg, failures);\n}\nderive(BulkError).from(DexieError);\n\n//\n//\n// Dynamically generate error names and exception classes based\n// on the names in errorList.\n//\n//\n\n// Map of {ErrorName -> ErrorName + \"Error\"}\nexport var errnames = errorList.reduce((obj,name)=>(obj[name]=name+\"Error\",obj),{});\n\n// Need an alias for DexieError because we're gonna create subclasses with the same name.\nconst BaseException = DexieError;\n// Map of {ErrorName -> exception constructor}\nexport var exceptions = errorList.reduce((obj,name)=>{\n    // Let the name be \"DexieError\" because this name may\n    // be shown in call stack and when debugging. DexieError is\n    // the most true name because it derives from DexieError,\n    // and we cannot change Function.name programatically without\n    // dynamically create a Function object, which would be considered\n    // 'eval-evil'.\n    var fullName = name + \"Error\";\n    function DexieError (msgOrInner, inner){\n        this._e = getErrorWithStack();\n        this.name = fullName;\n        if (!msgOrInner) {\n            this.message = defaultTexts[name] || fullName;\n            this.inner = null;\n        } else if (typeof msgOrInner === 'string') {\n            this.message = `${msgOrInner}${!inner ? '' : '\\n ' + inner}`;\n            this.inner = inner || null;\n        } else if (typeof msgOrInner === 'object') {\n            this.message = `${msgOrInner.name} ${msgOrInner.message}`;\n            this.inner = msgOrInner;\n        }\n    }\n    derive(DexieError).from(BaseException);\n    obj[name]=DexieError;\n    return obj;\n},{});\n\n// Use ECMASCRIPT standard exceptions where applicable:\nexceptions.Syntax = SyntaxError;\nexceptions.Type = TypeError;\nexceptions.Range = RangeError;\n\nexport var exceptionMap = idbDomErrorNames.reduce((obj, name)=>{\n    obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\n\nexport function mapError (domError, message) {\n    if (!domError || domError instanceof DexieError || domError instanceof TypeError || domError instanceof SyntaxError || !domError.name || !exceptionMap[domError.name])\n        return domError;\n    var rv = new exceptionMap[domError.name](message || domError.message, domError);\n    if (\"stack\" in domError) {\n        // Derive stack from inner exception if it has a stack\n        setProp(rv, \"stack\", {get: function(){\n            return this.inner.stack;\n        }});\n    }\n    return rv;\n}\n\nexport var fullNameExceptions = errorList.reduce((obj, name)=>{\n    if ([\"Syntax\",\"Type\",\"Range\"].indexOf(name) === -1)\n        obj[name + \"Error\"] = exceptions[name];\n    return obj;\n}, {});\n\nfullNameExceptions.ModifyError = ModifyError;\nfullNameExceptions.DexieError = DexieError;\nfullNameExceptions.BulkError = BulkError;\n", "import {extend} from './utils';\n\nexport function nop() { }\nexport function mirror(val) { return val; }\nexport function pureFunctionChain(f1, f2) {\n    // Enables chained events that takes ONE argument and returns it to the next function in chain.\n    // This pattern is used in the hook(\"reading\") event.\n    if (f1 == null || f1 === mirror) return f2;\n    return function (val) {\n        return f2(f1(val));\n    };\n}\n\nexport function callBoth(on1, on2) {\n    return function () {\n        on1.apply(this, arguments);\n        on2.apply(this, arguments);\n    };\n}\n\nexport function hookCreatingChain(f1, f2) {\n    // Enables chained events that takes several arguments and may modify first argument by making a modification and then returning the same instance.\n    // This pattern is used in the hook(\"creating\") event.\n    if (f1 === nop) return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res !== undefined) arguments[0] = res;\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res2 !== undefined ? res2 : res;\n    };\n}\n\nexport function hookDeletingChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        f1.apply(this, arguments);\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = this.onerror = null;\n        f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n    };\n}\n\nexport function hookUpdatingChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function (modifications) {\n        var res = f1.apply(this, arguments);\n        extend(modifications, res); // If f1 returns new modifications, extend caller's modifications with the result before calling next in chain.\n        var onsuccess = this.onsuccess, // In case event listener has set this.onsuccess\n            onerror = this.onerror;     // In case event listener has set this.onerror\n        this.onsuccess = null;\n        this.onerror = null;\n        var res2 = f2.apply(this, arguments);\n        if (onsuccess) this.onsuccess = this.onsuccess ? callBoth(onsuccess, this.onsuccess) : onsuccess;\n        if (onerror) this.onerror = this.onerror ? callBoth(onerror, this.onerror) : onerror;\n        return res === undefined ?\n            (res2 === undefined ? undefined : res2) :\n            (extend(res, res2));\n    };\n}\n\nexport function reverseStoppableEventChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        if (f2.apply(this, arguments) === false) return false;\n        return f1.apply(this, arguments);\n    };\n}\n\nexport function nonStoppableEventChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        f1.apply(this, arguments);\n        f2.apply(this, arguments);\n    };\n}\n\nexport function promisableChain(f1, f2) {\n    if (f1 === nop) return f2;\n    return function () {\n        var res = f1.apply(this, arguments);\n        if (res && typeof res.then === 'function') {\n            var thiz = this,\n                i = arguments.length,\n                args = new Array(i);\n            while (i--) args[i] = arguments[i];\n            return res.then(function () {\n                return f2.apply(thiz, args);\n            });\n        }\n        return f2.apply(this, arguments);\n    };\n}\n", "/*\n * Copyright (c) 2014-2017 <PERSON>\n * Apache License Version 2.0, January 2004, http://www.apache.org/licenses/LICENSE-2.0\n */\nimport { _global } from '../globals/global';\nimport {tryCatch, props, setProp, _global,\n    getPropertyDescriptor, getArrayOf, extend, getProto} from '../functions/utils';\nimport {nop, callBoth, mirror} from '../functions/chaining-functions';\nimport {debug, prettyStack, getErrorWithStack} from './debug';\nimport {exceptions} from '../errors';\n\n//\n// Promise and Zone (PSD) for Dexie library\n//\n// I started out writing this Promise class by copying promise-light (https://github.com/taylorhakes/promise-light) by\n// https://github.com/taylorhakes - an A+ and ECMASCRIPT 6 compliant Promise implementation.\n//\n// In previous versions this was fixed by not calling setTimeout when knowing that the resolve() or reject() came from another\n// tick. In Dexie v1.4.0, I've rewritten the Promise class entirely. Just some fragments of promise-light is left. I use\n// another strategy now that simplifies everything a lot: to always execute callbacks in a new micro-task, but have an own micro-task\n// engine that is indexedDB compliant across all browsers.\n// Promise class has also been optimized a lot with inspiration from bluebird - to avoid closures as much as possible.\n// Also with inspiration from bluebird, asyncronic stacks in debug mode.\n//\n// Specific non-standard features of this Promise class:\n// * Custom zone support (a.k.a. PSD) with ability to keep zones also when using native promises as well as\n//   native async / await.\n// * Promise.follow() method built upon the custom zone engine, that allows user to track all promises created from current stack frame\n//   and below + all promises that those promises creates or awaits.\n// * Detect any unhandled promise in a PSD-scope (PSD.onunhandled). \n//\n// David Fahlander, https://github.com/dfahlander\n//\n\n// Just a pointer that only this module knows about.\n// Used in Promise constructor to emulate a private constructor.\nvar INTERNAL = {};\n\n// Async stacks (long stacks) must not grow infinitely.\nconst\n    LONG_STACKS_CLIP_LIMIT = 100,\n    // When calling error.stack or promise.stack, limit the number of asyncronic stacks to print out. \n    MAX_LONG_STACKS = 20,\n    ZONE_ECHO_LIMIT = 100,\n    [resolvedNativePromise, nativePromiseProto, resolvedGlobalPromise] = typeof Promise === 'undefined' ?\n        [] :\n        (()=>{\n            let globalP = Promise.resolve();\n            if (typeof crypto === 'undefined' || !crypto.subtle)\n                return [globalP, getProto(globalP), globalP];\n            // Generate a native promise (as window.Promise may have been patched)\n            const nativeP = crypto.subtle.digest(\"SHA-512\", new Uint8Array([0]));\n            return [\n                nativeP,\n                getProto(nativeP),\n                globalP\n            ];\n        })(),\n    nativePromiseThen = nativePromiseProto && nativePromiseProto.then;\n\nexport const NativePromise = resolvedNativePromise && resolvedNativePromise.constructor;\nconst patchGlobalPromise = !!resolvedGlobalPromise;\n\nvar stack_being_generated = false;\n\n/* The default function used only for the very first promise in a promise chain.\n   As soon as then promise is resolved or rejected, all next tasks will be executed in micro ticks\n   emulated in this module. For indexedDB compatibility, this means that every method needs to \n   execute at least one promise before doing an indexedDB operation. Dexie will always call \n   db.ready().then() for every operation to make sure the indexedDB event is started in an\n   indexedDB-compatible emulated micro task loop.\n*/\nvar schedulePhysicalTick = resolvedGlobalPromise ?\n    () => {resolvedGlobalPromise.then(physicalTick);}\n    :\n    _global.setImmediate ? \n        // setImmediate supported. Those modern platforms also supports Function.bind().\n        setImmediate.bind(null, physicalTick) :\n        _global.MutationObserver ?\n            // MutationObserver supported\n            () => {\n                var hiddenDiv = document.createElement(\"div\");\n                (new MutationObserver(() => {\n                    physicalTick();\n                    hiddenDiv = null;\n                })).observe(hiddenDiv, { attributes: true });\n                hiddenDiv.setAttribute('i', '1');\n            } :\n            // No support for setImmediate or MutationObserver. No worry, setTimeout is only called\n            // once time. Every tick that follows will be our emulated micro tick.\n            // Could have uses setTimeout.bind(null, 0, physicalTick) if it wasnt for that FF13 and below has a bug \n            ()=>{setTimeout(physicalTick,0);};\n\n// Configurable through Promise.scheduler.\n// Don't export because it would be unsafe to let unknown\n// code call it unless they do try..catch within their callback.\n// This function can be retrieved through getter of Promise.scheduler though,\n// but users must not do Promise.scheduler = myFuncThatThrowsException\nvar asap = function (callback, args) {\n    microtickQueue.push([callback, args]);\n    if (needsNewPhysicalTick) {\n        schedulePhysicalTick();\n        needsNewPhysicalTick = false;\n    }\n};\n\nvar isOutsideMicroTick = true, // True when NOT in a virtual microTick.\n    needsNewPhysicalTick = true, // True when a push to microtickQueue must also schedulePhysicalTick()\n    unhandledErrors = [], // Rejected promises that has occured. Used for triggering 'unhandledrejection'.\n    rejectingErrors = [], // Tracks if errors are being re-rejected during onRejected callback.\n    currentFulfiller = null,\n    rejectionMapper = mirror; // Remove in next major when removing error mapping of DOMErrors and DOMExceptions\n    \nexport var globalPSD = {\n    id: 'global',\n    global: true,\n    ref: 0,\n    unhandleds: [],\n    onunhandled: globalError,\n    pgp: false,\n    env: {},\n    finalize: function () {\n        this.unhandleds.forEach(uh => {\n            try {\n                globalError(uh[0], uh[1]);\n            } catch (e) {}\n        });\n    }\n};\n\nexport var PSD = globalPSD;\n\nexport var microtickQueue = []; // Callbacks to call in this or next physical tick.\nexport var numScheduledCalls = 0; // Number of listener-calls left to do in this physical tick.\nexport var tickFinalizers = []; // Finalizers to call when there are no more async calls scheduled within current physical tick.\n\nexport default function DexiePromise(fn) {\n    if (typeof this !== 'object') throw new TypeError('Promises must be constructed via new');    \n    this._listeners = [];\n    this.onuncatched = nop; // Deprecate in next major. Not needed. Better to use global error handler.\n    \n    // A library may set `promise._lib = true;` after promise is created to make resolve() or reject()\n    // execute the microtask engine implicitely within the call to resolve() or reject().\n    // To remain A+ compliant, a library must only set `_lib=true` if it can guarantee that the stack\n    // only contains library code when calling resolve() or reject().\n    // RULE OF THUMB: ONLY set _lib = true for promises explicitely resolving/rejecting directly from\n    // global scope (event handler, timer etc)!\n    this._lib = false;\n    // Current async scope\n    var psd = (this._PSD = PSD);\n\n    if (debug) {\n        this._stackHolder = getErrorWithStack();\n        this._prev = null;\n        this._numPrev = 0; // Number of previous promises (for long stacks)\n    }\n    \n    if (typeof fn !== 'function') {\n        if (fn !== INTERNAL) throw new TypeError('Not a function');\n        // Private constructor (INTERNAL, state, value).\n        // Used internally by Promise.resolve() and Promise.reject().\n        this._state = arguments[1];\n        this._value = arguments[2];\n        if (this._state === false)\n            handleRejection(this, this._value); // Map error, set stack and addPossiblyUnhandledError().\n        return;\n    }\n    \n    this._state = null; // null (=pending), false (=rejected) or true (=resolved)\n    this._value = null; // error or result\n    ++psd.ref; // Refcounting current scope\n    executePromiseTask(this, fn);\n}\n\n// Prepare a property descriptor to put onto Promise.prototype.then\nconst thenProp = {\n    get: function() {\n        var psd = PSD, microTaskId = totalEchoes;\n\n        function then (onFulfilled, onRejected) {\n            var possibleAwait = !psd.global && (psd !== PSD || microTaskId !== totalEchoes);\n            const cleanup = possibleAwait && !decrementExpectedAwaits();\n            var rv = new DexiePromise((resolve, reject) => {\n                propagateToListener(this, new Listener(\n                    nativeAwaitCompatibleWrap(onFulfilled, psd, possibleAwait, cleanup),\n                    nativeAwaitCompatibleWrap(onRejected, psd, possibleAwait, cleanup),\n                    resolve,\n                    reject,\n                    psd));\n            });\n            debug && linkToPreviousPromise(rv, this);\n            return rv;\n        }\n\n        then.prototype = INTERNAL; // For idempotense, see setter below.\n\n        return then;\n    },\n    // Be idempotent and allow another framework (such as zone.js or another instance of a Dexie.Promise module) to replace Promise.prototype.then\n    // and when that framework wants to restore the original property, we must identify that and restore the original property descriptor.\n    set: function (value) {\n        setProp (this, 'then', value && value.prototype === INTERNAL ?\n            thenProp : // Restore to original property descriptor.\n            {\n                get: function(){\n                    return value; // Getter returning provided value (behaves like value is just changed)\n                },\n                set: thenProp.set // Keep a setter that is prepared to restore original.\n            }\n        );\n    }\n};\n\nprops(DexiePromise.prototype, {\n    then: thenProp, // Defined above.\n    _then: function (onFulfilled, onRejected) {\n        // A little tinier version of then() that don't have to create a resulting promise.\n        propagateToListener(this, new Listener(null, null, onFulfilled, onRejected, PSD));        \n    },\n\n    catch: function (onRejected) {\n        if (arguments.length === 1) return this.then(null, onRejected);\n        // First argument is the Error type to catch\n        var type = arguments[0],\n            handler = arguments[1];\n        return typeof type === 'function' ? this.then(null, err =>\n            // Catching errors by its constructor type (similar to java / c++ / c#)\n            // Sample: promise.catch(TypeError, function (e) { ... });\n            err instanceof type ? handler(err) : PromiseReject(err))\n        : this.then(null, err =>\n            // Catching errors by the error.name property. Makes sense for indexedDB where error type\n            // is always DOMError but where e.name tells the actual error type.\n            // Sample: promise.catch('ConstraintError', function (e) { ... });\n            err && err.name === type ? handler(err) : PromiseReject(err));\n    },\n\n    finally: function (onFinally) {\n        return this.then(value => {\n            onFinally();\n            return value;\n        }, err => {\n            onFinally();\n            return PromiseReject(err);\n        });\n    },\n    \n    stack: {\n        get: function() {\n            if (this._stack) return this._stack;\n            try {\n                stack_being_generated = true;\n                var stacks = getStack (this, [], MAX_LONG_STACKS);\n                var stack = stacks.join(\"\\nFrom previous: \");\n                if (this._state !== null) this._stack = stack; // Stack may be updated on reject.\n                return stack;\n            } finally {\n                stack_being_generated = false;\n            }\n        }\n    },\n\n    timeout: function (ms, msg) {\n        return ms < Infinity ?\n            new DexiePromise((resolve, reject) => {\n                var handle = setTimeout(() => reject(new exceptions.Timeout(msg)), ms);\n                this.then(resolve, reject).finally(clearTimeout.bind(null, handle));\n            }) : this;\n    }\n});\n\nif (typeof Symbol !== 'undefined' && Symbol.toStringTag)\n    setProp(DexiePromise.prototype, Symbol.toStringTag, 'Dexie.Promise');\n\n// Now that Promise.prototype is defined, we have all it takes to set globalPSD.env.\n// Environment globals snapshotted on leaving global zone\nglobalPSD.env = snapShot();\n\nfunction Listener(onFulfilled, onRejected, resolve, reject, zone) {\n    this.onFulfilled = typeof onFulfilled === 'function' ? onFulfilled : null;\n    this.onRejected = typeof onRejected === 'function' ? onRejected : null;\n    this.resolve = resolve;\n    this.reject = reject;\n    this.psd = zone;\n}\n\n// Promise Static Properties\nprops (DexiePromise, {\n    all: function () {\n        var values = getArrayOf.apply(null, arguments) // Supports iterables, implicit arguments and array-like.\n            .map(onPossibleParallellAsync); // Handle parallell async/awaits \n        return new DexiePromise(function (resolve, reject) {\n            if (values.length === 0) resolve([]);\n            var remaining = values.length;\n            values.forEach((a,i) => DexiePromise.resolve(a).then(x => {\n                values[i] = x;\n                if (!--remaining) resolve(values);\n            }, reject));\n        });\n    },\n    \n    resolve: value => {\n        if (value instanceof DexiePromise) return value;\n        if (value && typeof value.then === 'function') return new DexiePromise((resolve, reject)=>{\n            value.then(resolve, reject);\n        });\n        var rv = new DexiePromise(INTERNAL, true, value);\n        linkToPreviousPromise(rv, currentFulfiller);\n        return rv;\n    },\n    \n    reject: PromiseReject,\n    \n    race: function () {\n        var values = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise((resolve, reject) => {\n            values.map(value => DexiePromise.resolve(value).then(resolve, reject));\n        });\n    },\n\n    PSD: {\n        get: ()=>PSD,\n        set: value => PSD = value\n    },\n\n    totalEchoes: {get: ()=>totalEchoes},\n\n    //task: {get: ()=>task},\n    \n    newPSD: newScope,\n    \n    usePSD: usePSD,\n    \n    scheduler: {\n        get: () => asap,\n        set: value => {asap = value}\n    },\n    \n    rejectionMapper: {\n        get: () => rejectionMapper,\n        set: value => {rejectionMapper = value;} // Map reject failures\n    },\n            \n    follow: (fn, zoneProps) => {\n        return new DexiePromise((resolve, reject) => {\n            return newScope((resolve, reject) => {\n                var psd = PSD;\n                psd.unhandleds = []; // For unhandled standard- or 3rd party Promises. Checked at psd.finalize()\n                psd.onunhandled = reject; // Triggered directly on unhandled promises of this library.\n                psd.finalize = callBoth(function () {\n                    // Unhandled standard or 3rd part promises are put in PSD.unhandleds and\n                    // examined upon scope completion while unhandled rejections in this Promise\n                    // will trigger directly through psd.onunhandled\n                    run_at_end_of_this_or_next_physical_tick(()=>{\n                        this.unhandleds.length === 0 ? resolve() : reject(this.unhandleds[0]);\n                    });\n                }, psd.finalize);\n                fn();\n            }, zoneProps, resolve, reject);\n        });\n    }\n});\n\nif (NativePromise) {\n    if (NativePromise.allSettled) setProp (DexiePromise, \"allSettled\", function() {\n        const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise(resolve => {\n            if (possiblePromises.length === 0) resolve([]);\n            let remaining = possiblePromises.length;\n            const results = new Array(remaining);\n            possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(\n                value => results[i] = {status: \"fulfilled\", value},\n                reason => results[i] = {status: \"rejected\", reason})\n                .then(()=>--remaining || resolve(results)));\n        });\n    });\n    if (NativePromise.any && typeof AggregateError !== 'undefined') setProp(DexiePromise, \"any\", function() {\n        const possiblePromises = getArrayOf.apply(null, arguments).map(onPossibleParallellAsync);\n        return new DexiePromise((resolve, reject) => {\n            if (possiblePromises.length === 0) reject(new AggregateError([]));\n            let remaining = possiblePromises.length;\n            const failures = new Array(remaining);\n            possiblePromises.forEach((p, i) => DexiePromise.resolve(p).then(\n                value => resolve(value),\n                failure => {\n                    failures[i] = failure;\n                    if (!--remaining) reject(new AggregateError(failures));\n                }));\n        });\n    });\n}\n\n/**\n* Take a potentially misbehaving resolver function and make sure\n* onFulfilled and onRejected are only called once.\n*\n* Makes no guarantees about asynchrony.\n*/\nfunction executePromiseTask (promise, fn) {\n    // Promise Resolution Procedure:\n    // https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    try {\n        fn(value => {\n            if (promise._state !== null) return; // Already settled\n            if (value === promise) throw new TypeError('A promise cannot be resolved with itself.');\n            var shouldExecuteTick = promise._lib && beginMicroTickScope();\n            if (value && typeof value.then === 'function') {\n                executePromiseTask(promise, (resolve, reject) => {\n                    value instanceof DexiePromise ?\n                        value._then(resolve, reject) :\n                        value.then(resolve, reject);\n                });\n            } else {\n                promise._state = true;\n                promise._value = value;\n                propagateAllListeners(promise);\n            }\n            if (shouldExecuteTick) endMicroTickScope();\n        }, handleRejection.bind(null, promise)); // If Function.bind is not supported. Exception is handled in catch below\n    } catch (ex) {\n        handleRejection(promise, ex);\n    }\n}\n\nfunction handleRejection (promise, reason) {\n    rejectingErrors.push(reason);\n    if (promise._state !== null) return;\n    var shouldExecuteTick = promise._lib && beginMicroTickScope();\n    reason = rejectionMapper(reason);\n    promise._state = false;\n    promise._value = reason;\n    debug && reason !== null && typeof reason === 'object' && !reason._promise && tryCatch(()=>{\n        var origProp = getPropertyDescriptor(reason, \"stack\");        \n        reason._promise = promise;    \n        setProp(reason, \"stack\", {\n            get: () =>\n                stack_being_generated ?\n                    origProp && (origProp.get ?\n                                origProp.get.apply(reason) :\n                                origProp.value) :\n                    promise.stack\n        });\n    });\n    // Add the failure to a list of possibly uncaught errors\n    addPossiblyUnhandledError(promise);\n    propagateAllListeners(promise);\n    if (shouldExecuteTick) endMicroTickScope();\n}\n\nfunction propagateAllListeners (promise) {\n    //debug && linkToPreviousPromise(promise);\n    var listeners = promise._listeners;\n    promise._listeners = [];\n    for (var i = 0, len = listeners.length; i < len; ++i) {\n        propagateToListener(promise, listeners[i]);\n    }\n    var psd = promise._PSD;\n    --psd.ref || psd.finalize(); // if psd.ref reaches zero, call psd.finalize();\n    if (numScheduledCalls === 0) {\n        // If numScheduledCalls is 0, it means that our stack is not in a callback of a scheduled call,\n        // and that no deferreds where listening to this rejection or success.\n        // Since there is a risk that our stack can contain application code that may\n        // do stuff after this code is finished that may generate new calls, we cannot\n        // call finalizers here.\n        ++numScheduledCalls;\n        asap(()=>{\n            if (--numScheduledCalls === 0) finalizePhysicalTick(); // Will detect unhandled errors\n        }, []);\n    }\n}\n\nfunction propagateToListener(promise, listener) {\n    if (promise._state === null) {\n        promise._listeners.push(listener);\n        return;\n    }\n\n    var cb = promise._state ? listener.onFulfilled : listener.onRejected;\n    if (cb === null) {\n        // This Listener doesnt have a listener for the event being triggered (onFulfilled or onReject) so lets forward the event to any eventual listeners on the Promise instance returned by then() or catch()\n        return (promise._state ? listener.resolve : listener.reject) (promise._value);\n    }\n    ++listener.psd.ref;\n    ++numScheduledCalls;\n    asap (callListener, [cb, promise, listener]);\n}\n\nfunction callListener (cb, promise, listener) {\n    try {\n        // Set static variable currentFulfiller to the promise that is being fullfilled,\n        // so that we connect the chain of promises (for long stacks support)\n        currentFulfiller = promise;\n        \n        // Call callback and resolve our listener with it's return value.\n        var ret, value = promise._value;\n            \n        if (promise._state) {\n            // cb is onResolved\n            ret = cb (value);\n        } else {\n            // cb is onRejected\n            if (rejectingErrors.length) rejectingErrors = [];\n            ret = cb(value);\n            if (rejectingErrors.indexOf(value) === -1)\n                markErrorAsHandled(promise); // Callback didnt do Promise.reject(err) nor reject(err) onto another promise.\n        }\n        listener.resolve(ret);\n    } catch (e) {\n        // Exception thrown in callback. Reject our listener.\n        listener.reject(e);\n    } finally {\n        // Restore env and currentFulfiller.\n        currentFulfiller = null;\n        if (--numScheduledCalls === 0) finalizePhysicalTick();\n        --listener.psd.ref || listener.psd.finalize();\n    }\n}\n\nfunction getStack (promise, stacks, limit) {\n    if (stacks.length === limit) return stacks;\n    var stack = \"\";\n    if (promise._state === false) {\n        var failure = promise._value,\n            errorName,\n            message;\n        \n        if (failure != null) {\n            errorName = failure.name || \"Error\";\n            message = failure.message || failure;\n            stack = prettyStack(failure, 0);\n        } else {\n            errorName = failure; // If error is undefined or null, show that.\n            message = \"\";\n        }\n        stacks.push(errorName + (message ? \": \" + message : \"\") + stack);\n    }\n    if (debug) {\n        stack = prettyStack(promise._stackHolder, 2);\n        if (stack && stacks.indexOf(stack) === -1) stacks.push(stack);\n        if (promise._prev) getStack(promise._prev, stacks, limit);\n    }\n    return stacks;\n}\n\nfunction linkToPreviousPromise(promise, prev) {\n    // Support long stacks by linking to previous completed promise.\n    var numPrev = prev ? prev._numPrev + 1 : 0;\n    if (numPrev < LONG_STACKS_CLIP_LIMIT) { // Prohibit infinite Promise loops to get an infinite long memory consuming \"tail\".\n        promise._prev = prev;\n        promise._numPrev = numPrev;\n    }\n}\n\n/* The callback to schedule with setImmediate() or setTimeout().\n   It runs a virtual microtick and executes any callback registered in microtickQueue.\n */\nfunction physicalTick() {\n    beginMicroTickScope() && endMicroTickScope();\n}\n\nexport function beginMicroTickScope() {\n    var wasRootExec = isOutsideMicroTick;\n    isOutsideMicroTick = false;\n    needsNewPhysicalTick = false;\n    return wasRootExec;\n}\n\n/* Executes micro-ticks without doing try..catch.\n   This can be possible because we only use this internally and\n   the registered functions are exception-safe (they do try..catch\n   internally before calling any external method). If registering\n   functions in the microtickQueue that are not exception-safe, this\n   would destroy the framework and make it instable. So we don't export\n   our asap method.\n*/\nexport function endMicroTickScope() {\n    var callbacks, i, l;\n    do {\n        while (microtickQueue.length > 0) {\n            callbacks = microtickQueue;\n            microtickQueue = [];\n            l = callbacks.length;\n            for (i = 0; i < l; ++i) {\n                var item = callbacks[i];\n                item[0].apply(null, item[1]);\n            }\n        }\n    } while (microtickQueue.length > 0);\n    isOutsideMicroTick = true;\n    needsNewPhysicalTick = true;\n}\n\nfunction finalizePhysicalTick() {\n    var unhandledErrs = unhandledErrors;\n    unhandledErrors = [];\n    unhandledErrs.forEach(p => {\n        p._PSD.onunhandled.call(null, p._value, p);\n    });\n    var finalizers = tickFinalizers.slice(0); // Clone first because finalizer may remove itself from list.\n    var i = finalizers.length;\n    while (i) finalizers[--i]();    \n}\n\nfunction run_at_end_of_this_or_next_physical_tick (fn) {\n    function finalizer() {\n        fn();\n        tickFinalizers.splice(tickFinalizers.indexOf(finalizer), 1);\n    }\n    tickFinalizers.push(finalizer);\n    ++numScheduledCalls;\n    asap(()=>{\n        if (--numScheduledCalls === 0) finalizePhysicalTick();\n    }, []);\n}\n\nfunction addPossiblyUnhandledError(promise) {\n    // Only add to unhandledErrors if not already there. The first one to add to this list\n    // will be upon the first rejection so that the root cause (first promise in the\n    // rejection chain) is the one listed.\n    if (!unhandledErrors.some(p => p._value === promise._value))\n        unhandledErrors.push(promise);\n}\n\nfunction markErrorAsHandled(promise) {\n    // Called when a reject handled is actually being called.\n    // Search in unhandledErrors for any promise whos _value is this promise_value (list\n    // contains only rejected promises, and only one item per error)\n    var i = unhandledErrors.length;\n    while (i) if (unhandledErrors[--i]._value === promise._value) {\n        // Found a promise that failed with this same error object pointer,\n        // Remove that since there is a listener that actually takes care of it.\n        unhandledErrors.splice(i, 1);\n        return;\n    }\n}\n\nfunction PromiseReject (reason) {\n    return new DexiePromise(INTERNAL, false, reason);\n}\n\nexport function wrap (fn, errorCatcher) {\n    var psd = PSD;\n    return function() {\n        var wasRootExec = beginMicroTickScope(),\n            outerScope = PSD;\n\n        try {\n            switchToZone(psd, true);\n            return fn.apply(this, arguments);\n        } catch (e) {\n            errorCatcher && errorCatcher(e);\n        } finally {\n            switchToZone(outerScope, false);\n            if (wasRootExec) endMicroTickScope();\n        }\n    };\n}\n\n\n//\n// variables used for native await support\n//\nconst task = { awaits: 0, echoes: 0, id: 0}; // The ongoing macro-task when using zone-echoing.\nvar taskCounter = 0; // ID counter for macro tasks.\nvar zoneStack = []; // Stack of left zones to restore asynchronically.\nvar zoneEchoes = 0; // zoneEchoes is a must in order to persist zones between native await expressions.\nvar totalEchoes = 0; // ID counter for micro-tasks. Used to detect possible native await in our Promise.prototype.then.\n\n\nvar zone_id_counter = 0;\nexport function newScope (fn, props, a1, a2) {\n    var parent = PSD,\n        psd = Object.create(parent);\n    psd.parent = parent;\n    psd.ref = 0;\n    psd.global = false;\n    psd.id = ++zone_id_counter;\n    // Prepare for promise patching (done in usePSD):\n    var globalEnv = globalPSD.env;\n    psd.env = patchGlobalPromise ? {\n        Promise: DexiePromise, // Changing window.Promise could be omitted for Chrome and Edge, where IDB+Promise plays well!\n        PromiseProp: {value: DexiePromise, configurable: true, writable: true},\n        all: DexiePromise.all,\n        race: DexiePromise.race,\n        allSettled: DexiePromise.allSettled,\n        any: DexiePromise.any,\n        resolve: DexiePromise.resolve,\n        reject: DexiePromise.reject,\n        nthen: getPatchedPromiseThen (globalEnv.nthen, psd), // native then\n        gthen: getPatchedPromiseThen (globalEnv.gthen, psd) // global then\n    } : {};\n    if (props) extend(psd, props);\n    \n    // unhandleds and onunhandled should not be specifically set here.\n    // Leave them on parent prototype.\n    // unhandleds.push(err) will push to parent's prototype\n    // onunhandled() will call parents onunhandled (with this scope's this-pointer though!)\n    ++parent.ref;\n    psd.finalize = function () {\n        --this.parent.ref || this.parent.finalize();\n    }\n    var rv = usePSD (psd, fn, a1, a2);\n    if (psd.ref === 0) psd.finalize();\n    return rv;\n}\n\n// Function to call if scopeFunc returns NativePromise\n// Also for each NativePromise in the arguments to Promise.all()\nexport function incrementExpectedAwaits() {\n    if (!task.id) task.id = ++taskCounter;\n    ++task.awaits;\n    task.echoes += ZONE_ECHO_LIMIT;\n    return task.id;\n}\n\n// Function to call when 'then' calls back on a native promise where onAwaitExpected() had been called.\n// Also call this when a native await calls then method on a promise. In that case, don't supply\n// sourceTaskId because we already know it refers to current task.\nexport function decrementExpectedAwaits() {\n    if (!task.awaits) return false;\n    if (--task.awaits === 0) task.id = 0;\n    task.echoes = task.awaits * ZONE_ECHO_LIMIT; // Will reset echoes to 0 if awaits is 0.\n    return true;\n}\n\nif ((''+nativePromiseThen).indexOf('[native code]') === -1) {\n    // If the native promise' prototype is patched, we cannot rely on zone echoing.\n    // Disable that here:\n    incrementExpectedAwaits = decrementExpectedAwaits = nop;\n}\n\n// Call from Promise.all() and Promise.race()\nexport function onPossibleParallellAsync (possiblePromise) {\n    if (task.echoes && possiblePromise && possiblePromise.constructor === NativePromise) {\n        incrementExpectedAwaits(); \n        return possiblePromise.then(x => {\n            decrementExpectedAwaits();\n            return x;\n        }, e => {\n            decrementExpectedAwaits();\n            return rejection(e);\n        });\n    }\n    return possiblePromise;\n}\n\nfunction zoneEnterEcho(targetZone) {\n    ++totalEchoes;\n    //console.log(\"Total echoes \", totalEchoes);\n    if (!task.echoes || --task.echoes === 0) {\n        task.echoes = task.id = 0; // Cancel zone echoing.\n    }\n\n    zoneStack.push(PSD);\n    switchToZone(targetZone, true);\n}\n\nfunction zoneLeaveEcho() {\n    var zone = zoneStack[zoneStack.length-1];\n    zoneStack.pop();\n    switchToZone(zone, false);\n}\n\nfunction switchToZone (targetZone, bEnteringZone) {\n    var currentZone = PSD;\n    if (bEnteringZone ? task.echoes && (!zoneEchoes++ || targetZone !== PSD) : zoneEchoes && (!--zoneEchoes || targetZone !== PSD)) {\n        // Enter or leave zone asynchronically as well, so that tasks initiated during current tick\n        // will be surrounded by the zone when they are invoked.\n        enqueueNativeMicroTask(bEnteringZone ? zoneEnterEcho.bind(null, targetZone) : zoneLeaveEcho);\n    }\n    if (targetZone === PSD) return;\n\n    PSD = targetZone; // The actual zone switch occurs at this line.\n\n    // Snapshot on every leave from global zone.\n    if (currentZone === globalPSD) globalPSD.env = snapShot();\n\n    if (patchGlobalPromise) {\n        // Let's patch the global and native Promises (may be same or may be different)\n        var GlobalPromise = globalPSD.env.Promise;\n        // Swich environments (may be PSD-zone or the global zone. Both apply.)\n        var targetEnv = targetZone.env;\n\n        // Change Promise.prototype.then for native and global Promise (they MAY differ on polyfilled environments, but both can be accessed)\n        // Must be done on each zone change because the patched method contains targetZone in its closure.\n        nativePromiseProto.then = targetEnv.nthen;\n        GlobalPromise.prototype.then = targetEnv.gthen;\n\n        if (currentZone.global || targetZone.global) {\n            // Leaving or entering global zone. It's time to patch / restore global Promise.\n\n            // Set this Promise to window.Promise so that transiled async functions will work on Firefox, Safari and IE, as well as with Zonejs and angular.\n            Object.defineProperty(_global, 'Promise', targetEnv.PromiseProp);\n\n            // Support Promise.all() etc to work indexedDB-safe also when people are including es6-promise as a module (they might\n            // not be accessing global.Promise but a local reference to it)\n            GlobalPromise.all = targetEnv.all;\n            GlobalPromise.race = targetEnv.race;\n            GlobalPromise.resolve = targetEnv.resolve;\n            GlobalPromise.reject = targetEnv.reject;\n            if (targetEnv.allSettled) GlobalPromise.allSettled = targetEnv.allSettled;\n            if (targetEnv.any) GlobalPromise.any = targetEnv.any;\n        }\n    }\n}\n\nfunction snapShot () {\n    var GlobalPromise = _global.Promise;\n    return patchGlobalPromise ? {\n        Promise: GlobalPromise,\n        PromiseProp: Object.getOwnPropertyDescriptor(_global, \"Promise\"),\n        all: GlobalPromise.all,\n        race: GlobalPromise.race,\n        allSettled: GlobalPromise.allSettled,\n        any: GlobalPromise.any,\n        resolve: GlobalPromise.resolve,\n        reject: GlobalPromise.reject,\n        nthen: nativePromiseProto.then,\n        gthen: GlobalPromise.prototype.then\n    } : {};\n}\n\nexport function usePSD (psd, fn, a1, a2, a3) {\n    var outerScope = PSD;\n    try {\n        switchToZone(psd, true);\n        return fn(a1, a2, a3);\n    } finally {\n        switchToZone(outerScope, false);\n    }\n}\n\nfunction enqueueNativeMicroTask (job) {\n    //\n    // Precondition: nativePromiseThen !== undefined\n    //\n    nativePromiseThen.call(resolvedNativePromise, job);\n}\n\nfunction nativeAwaitCompatibleWrap(fn, zone, possibleAwait, cleanup) {\n    return typeof fn !== 'function' ? fn : function () {\n        var outerZone = PSD;\n        if (possibleAwait) incrementExpectedAwaits();\n        switchToZone(zone, true);\n        try {\n            return fn.apply(this, arguments);\n        } finally {\n            switchToZone(outerZone, false);\n            if (cleanup) enqueueNativeMicroTask(decrementExpectedAwaits);\n        }\n    };\n}\n\nfunction getPatchedPromiseThen (origThen, zone) {\n    return function (onResolved, onRejected) {\n        return origThen.call(this,\n            nativeAwaitCompatibleWrap(onResolved, zone),\n            nativeAwaitCompatibleWrap(onRejected, zone));\n    };\n}\n\nconst UNHANDLEDREJECTION = \"unhandledrejection\";\n\nfunction globalError(err, promise) {\n    var rv;\n    try {\n        rv = promise.onuncatched(err);\n    } catch (e) {}\n    if (rv !== false) try {\n        var event, eventData = {promise: promise, reason: err};\n        if (_global.document && document.createEvent) {\n            event = document.createEvent('Event');\n            event.initEvent(UNHANDLEDREJECTION, true, true);\n            extend(event, eventData);\n        } else if (_global.CustomEvent) {\n            event = new CustomEvent(UNHANDLEDREJECTION, {detail: eventData});\n            extend(event, eventData);\n        }\n        if (event && _global.dispatchEvent) {\n            dispatchEvent(event);\n            if (!_global.PromiseRejectionEvent && _global.onunhandledrejection)\n                // No native support for PromiseRejectionEvent but user has set window.onunhandledrejection. Manually call it.\n                try {_global.onunhandledrejection(event);} catch (_) {}\n        }\n        if (debug && event && !event.defaultPrevented) {\n            console.warn(`Unhandled rejection: ${err.stack || err}`);\n        }\n    } catch (e) {}\n}\n\nexport var rejection = DexiePromise.reject;\n\nexport {DexiePromise};\n", "import { PSD, rejection, newScope } from \"../helpers/promise\";\nimport { DexieOptions } from \"../public/types/dexie-constructor\";\nimport { errnames, exceptions } from \"../errors\";\nimport { nop } from \"./chaining-functions\";\nimport { Transaction } from \"../classes/transaction\";\nimport { <PERSON>ie } from '../classes/dexie';\n\n/* Generate a temporary transaction when db operations are done outside a transaction scope.\n*/\nexport function tempTransaction (\n  db: Dexie,\n  mode: IDBTransactionMode,\n  storeNames: string[],\n  fn: (resolve, reject, trans: Transaction) => any)\n  // Last argument is \"writeLocked\". But this doesnt apply to oneshot direct db operations, so we ignore it.\n{\n  if (!db.idbdb || (!db._state.openComplete && (!PSD.letThrough && !db._vip))) {\n    if (db._state.openComplete) {\n      // db.idbdb is falsy but openComplete is true. Must have been an exception durin open.\n      // Don't wait for openComplete as it would lead to infinite loop.\n      return rejection(new exceptions.DatabaseClosed(db._state.dbOpenError));\n    }\n    if (!db._state.isBeingOpened) {\n      if (!db._options.autoOpen)\n        return rejection(new exceptions.DatabaseClosed());\n      db.open().catch(nop); // Open in background. If if fails, it will be catched by the final promise anyway.\n    }\n    return db._state.dbReadyPromise.then(() => tempTransaction(db, mode, storeNames, fn));\n  } else {\n    var trans = db._createTransaction(mode, storeNames, db._dbSchema);\n    try {\n      trans.create();\n      db._state.PR1398_maxLoop = 3;\n    } catch (ex) {\n      if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n        console.warn('Dexie: Need to reopen db');\n        db._close();\n        return db.open().then(()=>tempTransaction(db, mode, storeNames, fn));\n      }\n      return rejection(ex);\n    }\n    return trans._promise(mode, (resolve, reject) => {\n      return newScope(() => { // OPTIMIZATION POSSIBLE? newScope() not needed because it's already done in _promise.\n        PSD.trans = trans;\n        return fn(resolve, reject, trans);\n      });\n    }).then(result => {\n      // Instead of resolving value directly, wait with resolving it until transaction has completed.\n      // Otherwise the data would not be in the DB if requesting it in the then() operation.\n      // Specifically, to ensure that the following expression will work:\n      //\n      //   db.friends.put({name: \"Arne\"}).then(function () {\n      //       db.friends.where(\"name\").equals(\"Arne\").count(function(count) {\n      //           assert (count === 1);\n      //       });\n      //   });\n      //\n      return trans._completion.then(() => result);\n    });/*.catch(err => { // Don't do this as of now. If would affect bulk- and modify methods in a way that could be more intuitive. But wait! Maybe change in next major.\n          trans._reject(err);\n          return rejection(err);\n      });*/\n  }\n}\n", "import { <PERSON><PERSON> } from \"../classes/dexie\";\n\nexport const DEXIE_VERSION = '{version}'; // Replaced by build-script.\nexport const maxString = String.fromCharCode(65535);\nexport const minKey = -Infinity; // minKey can be constant. maxKey must be a prop of <PERSON><PERSON> (_maxKey)\nexport const INVALID_KEY_ARGUMENT =\n  \"Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.\";\nexport const STRING_EXPECTED = \"String expected.\";\nexport const connections: Dexie[] = [];\nexport const isIEOrEdge =\n  typeof navigator !== 'undefined' && /(MSIE|Trident|Edge)/.test(navigator.userAgent);\nexport const hasIEDeleteObjectStoreBug = isIEOrEdge;\nexport const hangsOnDeleteLargeKeyRange = isIEOrEdge;\nexport const dexieStackFrameFilter = frame => !/(dexie\\.js|dexie\\.min\\.js)/.test(frame);\nexport const DBNAMES_DB = '__dbnames';\nexport const READONLY = 'readonly';\nexport const READWRITE = 'readwrite';\n", "export function combine(filter1, filter2) {\n  return filter1 ?\n      filter2 ?\n          function () { return filter1.apply(this, arguments) && filter2.apply(this, arguments); } :\n          filter1 :\n      filter2;\n}\n", "import { DBCoreKeyRange, DBCoreRangeType } from '../public/types/dbcore';\n\nexport const AnyRange: DBCoreKeyRange = {\n  type: DBCoreRangeType.Any,\n  lower: -Infinity,\n  lowerOpen: false,\n  upper: [[]],\n  upperOpen: false\n}\n\nexport const NeverRange: DBCoreKeyRange = {\n  type: DBCoreRangeType.Never,\n  lower: -Infinity,\n  lowerOpen: true,\n  upper: -Infinity,\n  upperOpen: true\n}\n", "import { deep<PERSON>lone, delBy<PERSON>eyPath, getByKeyPath } from './utils';\n\n// This workaround is needed since obj could be a custom-class instance with an\n// uninitialized keyPath. See the following comment for more context:\n// https://github.com/dfahlander/Dexie.js/issues/1280#issuecomment-823557881\nexport function workaroundForUndefinedPrimKey(keyPath: string | ArrayLike<string>) {\n  // Workaround only needed for plain non-dotted keyPaths\n  return typeof keyPath === \"string\" && !/\\./.test(keyPath) \n  ? (obj: object) => {\n    if (obj[keyPath] === undefined && (keyPath in obj)) {\n      // property exists but is undefined. This will not be liked by Indexeddb.\n      // Need to remove the property before adding it but we need to clone it before\n      // doing that to not be intrusive.\n      obj = deepClone(obj);\n      delete obj[keyPath];\n    }\n    return obj;\n  }\n  : (obj: object) => obj;\n}", "import { BulkError, exceptions } from '../../errors';\nimport { Table as ITable } from '../../public/types/table';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { TableHooks } from '../../public/types/table-hooks';\nimport { DexiePromise as Promise, PSD, newScope, wrap, rejection, beginMicroTickScope, endMicroTickScope } from '../../helpers/promise';\nimport { Transaction } from '../transaction';\nimport { Dexie } from '../dexie';\nimport { tempTransaction } from '../../functions/temp-transaction';\nimport { Collection } from '../collection';\nimport { isArray, keys, getByKeyPath, hasOwn, setByKeyPath, deepClone, tryCatch, arrayToObject, extend } from '../../functions/utils';\nimport { maxString } from '../../globals/constants';\nimport { combine } from '../../functions/combine';\nimport { PromiseExtended } from \"../../public/types/promise-extended\";\nimport { IndexableType } from '../../public/types/indexable-type';\nimport { debug } from '../../helpers/debug';\nimport { DBCoreTable } from '../../public/types/dbcore';\nimport { AnyRange } from '../../dbcore/keyrange';\nimport { workaroundForUndefinedPrimKey } from '../../functions/workaround-undefined-primkey';\n\n/** class Table\n * \n * https://dexie.org/docs/Table/Table\n */\nexport class Table implements ITable<any, IndexableType> {\n  db: Dexie;\n  _tx?: Transaction;\n  name: string;\n  schema: TableSchema;\n  hook: TableHooks;\n  core: DBCoreTable;\n\n  _trans(\n    mode: IDBTransactionMode,\n    fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<any> | void,\n    writeLocked?: boolean | string) : PromiseExtended<any>\n  {\n    const trans: Transaction = this._tx || PSD.trans;\n    const tableName = this.name;\n    \n    function checkTableInTransaction(resolve, reject, trans: Transaction) {\n      if (!trans.schema[tableName])\n        throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");\n      return fn(trans.idbtrans, trans);\n    }\n    // Surround all in a microtick scope.\n    // Reason: Browsers (modern Safari + older others)\n    // still as of 2018-10-10 has problems keeping a transaction\n    // alive between micro ticks. Safari because if transaction\n    // is created but not used in same microtick, it will go\n    // away. That specific issue could be solved in DBCore\n    // by opening the transaction just before using it instead.\n    // But older Firefoxes and IE11 (with Promise polyfills)\n    // will still have probs.\n    // The beginMicrotickScope()/endMicrotickScope() works\n    // in cooperation with Dexie.Promise to orchestrate\n    // the micro-ticks in endMicrotickScope() rather than\n    // in native engine.\n    const wasRootExec = beginMicroTickScope();\n    try {\n      return trans && trans.db === this.db ?\n        trans === PSD.trans ?\n          trans._promise(mode, checkTableInTransaction, writeLocked) :\n          newScope(() => trans._promise(mode, checkTableInTransaction, writeLocked), { trans: trans, transless: PSD.transless || PSD }) :\n        tempTransaction(this.db, mode, [this.name], checkTableInTransaction);\n    } finally {\n      if (wasRootExec) endMicroTickScope();\n    }\n  }\n\n  /** Table.get()\n   * \n   * https://dexie.org/docs/Table/Table.get()\n   * \n   **/\n  get(keyOrCrit, cb?) {\n    if (keyOrCrit && keyOrCrit.constructor === Object)\n      return this.where(keyOrCrit as { [key: string]: IndexableType }).first(cb);\n\n    return this._trans('readonly', (trans) => {\n      return this.core.get({trans, key: keyOrCrit})\n        .then(res => this.hook.reading.fire(res));\n    }).then(cb);\n  }\n\n  /** Table.where()\n   * \n   * https://dexie.org/docs/Table/Table.where()\n   * \n   **/\n  where(indexOrCrit: string | string[] | { [key: string]: IndexableType }) {\n    if (typeof indexOrCrit === 'string')\n      return new this.db.WhereClause(this, indexOrCrit);\n    if (isArray(indexOrCrit))\n      return new this.db.WhereClause(this, `[${indexOrCrit.join('+')}]`);\n    // indexOrCrit is an object map of {[keyPath]:value} \n    const keyPaths = keys(indexOrCrit);\n    if (keyPaths.length === 1)\n      // Only one critera. This was the easy case:\n      return this\n        .where(keyPaths[0])\n        .equals(indexOrCrit[keyPaths[0]]);\n\n    // Multiple criterias.\n    // Let's try finding a compound index that matches all keyPaths in\n    // arbritary order:\n    const compoundIndex = this.schema.indexes.concat(this.schema.primKey).filter(ix => {\n      if (\n        ix.compound &&\n        keyPaths.every(keyPath => ix.keyPath.indexOf(keyPath) >= 0)) {\n          for (let i=0; i<keyPaths.length; ++i) {\n            if (keyPaths.indexOf(ix.keyPath[i]) === -1) return false;\n          }\n          return true;\n        }\n        return false;\n      }).sort((a,b) => a.keyPath.length - b.keyPath.length)[0];\n            \n    if (compoundIndex && this.db._maxKey !== maxString) {\n      // Cool! We found such compound index\n      // and this browser supports compound indexes (maxKey !== maxString)!\n      const keyPathsInValidOrder = (compoundIndex.keyPath as string[]).slice(0, keyPaths.length);\n      return this\n        .where(keyPathsInValidOrder)\n        .equals(keyPathsInValidOrder.map(kp => indexOrCrit[kp]));\n    }\n\n    if (!compoundIndex && debug) console.warn(\n      `The query ${JSON.stringify(indexOrCrit)} on ${this.name} would benefit of a ` +\n      `compound index [${keyPaths.join('+')}]`);\n\n    // Ok, now let's fallback to finding at least one matching index\n    // and filter the rest.\n    const { idxByName } = this.schema;\n    const idb = this.db._deps.indexedDB;\n\n    function equals (a, b) {\n      try {\n        return idb.cmp(a,b) === 0; // Works with all indexable types including binary keys.\n      } catch (e) {\n        return false;\n      }\n    }\n\n    const [idx, filterFunction] = keyPaths.reduce(([prevIndex, prevFilterFn], keyPath) => {\n      const index = idxByName[keyPath];\n      const value = indexOrCrit[keyPath];\n      return [\n        prevIndex || index, // idx::=Pick index of first matching keypath\n        prevIndex || !index ? // filter::=null if not needed, otherwise combine function filter\n          combine(\n            prevFilterFn,\n            index && index.multi ?\n              x => {\n                const prop = getByKeyPath(x, keyPath);\n                return isArray(prop) && prop.some(item => equals(value, item));\n              } : x => equals(value, getByKeyPath(x, keyPath)))\n          : prevFilterFn\n      ];\n    }, [null, null]);\n\n    return idx ?\n      this.where(idx.name).equals(indexOrCrit[idx.keyPath])\n        .filter(filterFunction) :\n      compoundIndex ?\n        this.filter(filterFunction) : // Has compound but browser bad. Allow filter.\n        this.where(keyPaths).equals(''); // No index at all. Fail lazily with \"[a+b+c] is not indexed\"\n  }\n\n  /** Table.filter()\n   * \n   * https://dexie.org/docs/Table/Table.filter()\n   * \n   **/\n  filter(filterFunction: (obj: any) => boolean) {\n    return this.toCollection().and(filterFunction);\n  }\n\n  /** Table.count()\n   * \n   * https://dexie.org/docs/Table/Table.count()\n   * \n   **/\n  count(thenShortcut?: any) {\n    return this.toCollection().count(thenShortcut);\n  }\n\n  /** Table.offset()\n   * \n   * https://dexie.org/docs/Table/Table.offset()\n   * \n   **/\n  offset(offset: number) {\n    return this.toCollection().offset(offset);\n  }\n\n  /** Table.limit()\n   * \n   * https://dexie.org/docs/Table/Table.limit()\n   * \n   **/\n  limit(numRows: number) {\n    return this.toCollection().limit(numRows);\n  }\n\n  /** Table.each()\n   * \n   * https://dexie.org/docs/Table/Table.each()\n   * \n   **/\n  each(callback: (obj: any, cursor: { key: IndexableType, primaryKey: IndexableType }) => any) {\n    return this.toCollection().each(callback);\n  }\n\n  /** Table.toArray()\n   * \n   * https://dexie.org/docs/Table/Table.toArray()\n   * \n   **/\n  toArray(thenShortcut?: any) {\n    return this.toCollection().toArray(thenShortcut);\n  }\n\n  /** Table.toCollection()\n   * \n   * https://dexie.org/docs/Table/Table.toCollection()\n   * \n   **/\n  toCollection() {\n    return new this.db.Collection(new this.db.WhereClause(this));\n  }\n\n  /** Table.orderBy()\n   * \n   * https://dexie.org/docs/Table/Table.orderBy()\n   * \n   **/\n  orderBy(index: string | string[]) {\n    return new this.db.Collection(\n      new this.db.WhereClause(this, isArray(index) ?\n        `[${index.join('+')}]` :\n        index));\n  }\n\n  /** Table.reverse()\n   * \n   * https://dexie.org/docs/Table/Table.reverse()\n   * \n   **/\n  reverse(): Collection {\n    return this.toCollection().reverse();\n  }\n\n  /** Table.mapToClass()\n   * \n   * https://dexie.org/docs/Table/Table.mapToClass()\n   * \n   **/\n  mapToClass(constructor: Function) {\n    this.schema.mappedClass = constructor;\n    // Now, subscribe to the when(\"reading\") event to make all objects that come out from this table inherit from given class\n    // no matter which method to use for reading (Table.get() or Table.where(...)... )\n    const readHook = obj => {\n      if (!obj) return obj; // No valid object. (Value is null). Return as is.\n      // Create a new object that derives from constructor:\n      const res = Object.create(constructor.prototype);\n      // Clone members:\n      for (var m in obj) if (hasOwn(obj, m)) try { res[m] = obj[m]; } catch (_) { }\n      return res;\n    };\n\n    if (this.schema.readHook) {\n      this.hook.reading.unsubscribe(this.schema.readHook);\n    }\n    this.schema.readHook = readHook;\n    this.hook(\"reading\", readHook);\n    return constructor;\n  }\n\n  /** @deprecated */\n  defineClass() {\n    function Class (content){\n      extend(this, content);\n    };\n    return this.mapToClass(Class);\n  }\n\n  /** Table.add()\n   * \n   * https://dexie.org/docs/Table/Table.add()\n   * \n   **/\n  add(obj, key?: IndexableType): PromiseExtended<IndexableType> {\n    const {auto, keyPath} = this.schema.primKey;\n    let objToAdd = obj;\n    if (keyPath && auto) {\n      objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n    }\n    return this._trans('readwrite', trans => {\n      return this.core.mutate({trans, type: 'add', keys: key != null ? [key] : null, values: [objToAdd]});\n    }).then(res => res.numFailures ? Promise.reject(res.failures[0]) : res.lastResult)\n    .then(lastResult => {\n      if (keyPath) {\n        // This part should be here for backward compatibility.\n        // If ever feeling too bad about this, please wait to a new major before removing it,\n        // and document the change thoroughly.\n        try{setByKeyPath(obj, keyPath, lastResult);}catch(_){};\n      }\n      return lastResult;\n    });\n  }\n\n  /** Table.update()\n   * \n   * https://dexie.org/docs/Table/Table.update()\n   * \n   **/\n  update(keyOrObject, modifications: { [keyPath: string]: any; } | ((obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean)): PromiseExtended<number> {\n    if (typeof keyOrObject === 'object' && !isArray(keyOrObject)) {\n      const key = getByKeyPath(keyOrObject, this.schema.primKey.keyPath);\n      if (key === undefined) return rejection(new exceptions.InvalidArgument(\n        \"Given object does not contain its primary key\"));\n      // object to modify. Also modify given object with the modifications:\n      // This part should be here for backward compatibility.\n      // If ever feeling too bad about mutating given object, please wait to a new major before removing it,\n      // and document the change thoroughly.\n      try {\n        if (typeof modifications !== \"function\") {\n          keys(modifications).forEach(keyPath => {\n            setByKeyPath(keyOrObject, keyPath, modifications[keyPath]);\n          });\n        } else {\n          // Now since we support function argument, we should have a similar behavior here as well\n          // (as long as we do this mutability stuff on the given object)\n          modifications(keyOrObject, {value: keyOrObject, primKey: key});\n        }\n      } catch {\n        // Maybe given object was frozen.\n        // This part is not essential. Just move on as nothing happened...\n      }\n      return this.where(\":id\").equals(key).modify(modifications);\n    } else {\n      // key to modify\n      return this.where(\":id\").equals(keyOrObject).modify(modifications);\n    }\n  }\n\n  /** Table.put()\n   * \n   * https://dexie.org/docs/Table/Table.put()\n   * \n   **/\n  put(obj, key?: IndexableType): PromiseExtended<IndexableType> {\n    const {auto, keyPath} = this.schema.primKey;\n    let objToAdd = obj;\n    if (keyPath && auto) {\n      objToAdd = workaroundForUndefinedPrimKey(keyPath)(obj);\n    }\n    return this._trans(\n      'readwrite',\n      trans => this.core.mutate({trans, type: 'put', values: [objToAdd], keys: key != null ? [key] : null}))\n    .then(res => res.numFailures ? Promise.reject(res.failures[0]) : res.lastResult)\n    .then(lastResult => {\n      if (keyPath) {\n        // This part should be here for backward compatibility.\n        // If ever feeling too bad about this, please wait to a new major before removing it,\n        // and document the change thoroughly.\n        try{setByKeyPath(obj, keyPath, lastResult);}catch(_){};\n      }\n      return lastResult;\n    });\n  }\n\n  /** Table.delete()\n   * \n   * https://dexie.org/docs/Table/Table.delete()\n   * \n   **/\n  delete(key: IndexableType): PromiseExtended<void> {\n    return this._trans('readwrite',\n      trans => this.core.mutate({trans, type: 'delete', keys: [key]}))\n    .then(res => res.numFailures ? Promise.reject(res.failures[0]) : undefined);\n  }\n\n  /** Table.clear()\n   * \n   * https://dexie.org/docs/Table/Table.clear()\n   * \n   **/\n  clear() {\n    return this._trans('readwrite',\n      trans => this.core.mutate({trans, type: 'deleteRange', range: AnyRange}))\n        .then(res => res.numFailures ? Promise.reject(res.failures[0]) : undefined);\n  }\n\n  /** Table.bulkGet()\n   * \n   * https://dexie.org/docs/Table/Table.bulkGet()\n   * \n   * @param keys \n   */\n  bulkGet(keys: IndexableType[]) {\n    return this._trans('readonly', trans => {\n      return this.core.getMany({\n        keys,\n        trans\n      }).then(result => result.map(res => this.hook.reading.fire(res)));\n    });\n  }\n\n  /** Table.bulkAdd()\n   * \n   * https://dexie.org/docs/Table/Table.bulkAdd()\n   * \n   **/\n  bulkAdd(\n    objects: any[],\n    keysOrOptions?: ReadonlyArray<IndexableType> | { allKeys?: boolean },\n    options?: { allKeys?: boolean }\n  ) {    \n    const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n    options = options || (keys ? undefined : keysOrOptions as { allKeys?: boolean });\n    const wantResults = options ? options.allKeys : undefined;\n\n    return this._trans('readwrite', trans => {\n      const {auto, keyPath} = this.schema.primKey;\n      if (keyPath && keys)\n        throw new exceptions.InvalidArgument(\"bulkAdd(): keys argument invalid on tables with inbound keys\");\n      if (keys && keys.length !== objects.length)\n        throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n\n      const numObjects = objects.length; // Pick length here to allow garbage collection of objects later\n      let objectsToAdd = keyPath && auto ?\n        objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n        objects;\n      return this.core.mutate(\n        {trans, type: 'add', keys: keys as IndexableType[], values: objectsToAdd, wantResults}\n      )\n        .then(({numFailures, results,lastResult, failures}) => {\n          const result = wantResults ? results : lastResult;\n          if (numFailures === 0) return result;\n          throw new BulkError(\n            `${this.name}.bulkAdd(): ${numFailures} of ${numObjects} operations failed`, failures);\n        });\n    });\n  }\n\n  /** Table.bulkPut()\n   * \n   * https://dexie.org/docs/Table/Table.bulkPut()\n   * \n   **/\n  bulkPut(\n    objects: any[],\n    keysOrOptions?: ReadonlyArray<IndexableType> | { allKeys?: boolean },\n    options?: { allKeys?: boolean }\n  ) {   \n    const keys = Array.isArray(keysOrOptions) ? keysOrOptions : undefined;\n    options = options || (keys ? undefined : keysOrOptions as { allKeys?: boolean });\n    const wantResults = options ? options.allKeys : undefined;\n\n    return this._trans('readwrite', trans => {\n      const {auto, keyPath} = this.schema.primKey;\n      if (keyPath && keys)\n        throw new exceptions.InvalidArgument(\"bulkPut(): keys argument invalid on tables with inbound keys\");\n      if (keys && keys.length !== objects.length)\n        throw new exceptions.InvalidArgument(\"Arguments objects and keys must have the same length\");\n\n      const numObjects = objects.length; // Pick length here to allow garbage collection of objects later\n      let objectsToPut = keyPath && auto ?\n        objects.map(workaroundForUndefinedPrimKey(keyPath)) :\n        objects;\n\n      return this.core.mutate(\n        {trans, type: 'put', keys: keys as IndexableType[], values: objectsToPut, wantResults}\n      )\n        .then(({numFailures, results, lastResult, failures}) => {\n          const result = wantResults ? results : lastResult;\n          if (numFailures === 0) return result;\n          throw new BulkError(\n            `${this.name}.bulkPut(): ${numFailures} of ${numObjects} operations failed`, failures);\n        });\n    });\n  }\n\n  /** Table.bulkDelete()\n   * \n   * https://dexie.org/docs/Table/Table.bulkDelete()\n   * \n   **/\n  bulkDelete(keys: ReadonlyArray<IndexableType>): PromiseExtended<void> {\n    const numKeys = keys.length;\n    return this._trans('readwrite', trans => {\n      return this.core.mutate({trans, type: 'delete', keys: keys as IndexableType[]});\n    }).then(({numFailures, lastResult, failures}) => {\n      if (numFailures === 0) return lastResult;\n      throw new BulkError(\n        `${this.name}.bulkDelete(): ${numFailures} of ${numKeys} operations failed`, failures);\n    });\n  }\n}\n", "import {keys, isArray, asap} from '../functions/utils';\nimport {nop, mirror, reverseStoppableEventChain} from '../functions/chaining-functions';\nimport {exceptions} from '../errors';\n\nexport default function Events(ctx) {\n    var evs = {};\n    var rv = function (eventName, subscriber) {\n        if (subscriber) {\n            // Subscribe. If additional arguments than just the subscriber was provided, forward them as well.\n            var i = arguments.length, args = new Array(i - 1);\n            while (--i) args[i - 1] = arguments[i];\n            evs[eventName].subscribe.apply(null, args);\n            return ctx;\n        } else if (typeof (eventName) === 'string') {\n            // Return interface allowing to fire or unsubscribe from event\n            return evs[eventName];\n        }\n    };\n    rv.addEventType = add;\n    \n    for (var i = 1, l = arguments.length; i < l; ++i) {\n        add(arguments[i]);\n    }\n    \n    return rv;\n\n    function add(eventName, chainFunction, defaultFunction) {\n        if (typeof eventName === 'object') return addConfiguredEvents(eventName);\n        if (!chainFunction) chainFunction = reverseStoppableEventChain;\n        if (!defaultFunction) defaultFunction = nop;\n\n        var context = {\n            subscribers: [],\n            fire: defaultFunction,\n            subscribe: function (cb) {\n                if (context.subscribers.indexOf(cb) === -1) {\n                    context.subscribers.push(cb);\n                    context.fire = chainFunction(context.fire, cb);\n                }\n            },\n            unsubscribe: function (cb) {\n                context.subscribers = context.subscribers.filter(function (fn) { return fn !== cb; });\n                context.fire = context.subscribers.reduce(chainFunction, defaultFunction);\n            }\n        };\n        evs[eventName] = rv[eventName] = context;\n        return context;\n    }\n\n    function addConfiguredEvents(cfg) {\n        // events(this, {reading: [functionChain, nop]});\n        keys(cfg).forEach(function (eventName) {\n            var args = cfg[eventName];\n            if (isArray(args)) {\n                add(eventName, cfg[eventName][0], cfg[eventName][1]);\n            } else if (args === 'asap') {\n                // Rather than approaching event subscription using a functional approach, we here do it in a for-loop where subscriber is executed in its own stack\n                // enabling that any exception that occur wont disturb the initiator and also not nescessary be catched and forgotten.\n                var context = add(eventName, mirror, function fire() {\n                    // Optimazation-safe cloning of arguments into args.\n                    var i = arguments.length, args = new Array(i);\n                    while (i--) args[i] = arguments[i];\n                    // All each subscriber:\n                    context.subscribers.forEach(function (fn) {\n                        asap(function fireEvent() {\n                            fn.apply(null, args);\n                        });\n                    });\n                });\n            } else throw new exceptions.InvalidArgument(\"Invalid event config\");\n        });\n    }\n}\n", "import { arrayToObject, derive } from './utils';\n\n\nexport function makeClassConstructor<TConstructor> (prototype: Object, constructor: Function) {\n  /*const propertyDescriptorMap = arrayToObject(\n    Object.getOwnPropertyNames(prototype),\n    propKey => [propKey, Object.getOwnPropertyDescriptor(prototype, propKey)]);\n\n  // Both derive and clone the prototype.\n  //   derive: So that x instanceof T returns true when T is the class template.\n  //   clone: Optimizes method access a bit (but actually not nescessary)\n  const derivedPrototypeClone = Object.create(prototype, propertyDescriptorMap);\n  derivedPrototypeClone.constructor = constructor;\n  constructor.prototype = derivedPrototypeClone;\n  return constructor as any as TConstructor;*/\n\n  // Keep the above code in case we want to clone AND derive the parent prototype.\n  // Reason would be optimization of property access.\n  // The code below will only create a prototypal inheritance from given constructor function\n  // to given prototype.\n  derive(constructor).from({prototype});\n  return constructor as any as TConstructor;  \n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { Transaction } from '../transaction/transaction';\nimport { hookCreating<PERSON>hain, pureFunction<PERSON>hain, nop, mirror, hookUpdating<PERSON>hain, hookDeleting<PERSON>hain } from '../../functions/chaining-functions';\nimport { TableHooks } from '../../public/types/table-hooks';\nimport { Table } from './table';\nimport Events from '../../helpers/Events';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\n\nexport interface TableConstructor {\n  new (name: string, tableSchema: TableSchema, optionalTrans?: Transaction) : Table;\n  prototype: Table;\n}\n\n/** Generates a Table constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createTableConstructor (db: Dexie) {\n  return makeClassConstructor<TableConstructor>(\n    Table.prototype,\n\n    function Table (this: Table, name: string, tableSchema: TableSchema, trans?: Transaction) {\n      this.db = db;\n      this._tx = trans;\n      this.name = name;\n      this.schema = tableSchema;\n      this.hook = db._allTables[name] ? db._allTables[name].hook : Events(null, {\n        \"creating\": [hookCreatingChain, nop],\n        \"reading\": [pureFunctionChain, mirror],\n        \"updating\": [hookUpdatingChain, nop],\n        \"deleting\": [hookDeletingChain, nop]\n      }) as TableHooks;\n    }\n\n  );\n}\n", "import { combine } from \"../../functions/combine\";\nimport { exceptions } from \"../../errors\";\nimport { hasOwn } from \"../../functions/utils\";\nimport { wrap } from \"../../helpers/promise\";\nimport { Collection } from './';\nimport { DBCoreCursor, DBCoreTable, DBCoreTransaction, DBCoreTableSchema, DBCoreRangeType } from '../../public/types/dbcore';\nimport { nop } from '../../functions/chaining-functions';\n\ntype CollectionContext = Collection[\"_ctx\"];\n\nexport function isPlainKeyRange (ctx: CollectionContext, ignoreLimitFilter?: boolean) {\n  return !(ctx.filter || ctx.algorithm || ctx.or) &&\n      (ignoreLimitFilter ? ctx.justLimit : !ctx.replayFilter);\n}    \n\nexport function addFilter(ctx: CollectionContext, fn: Function) {\n  ctx.filter = combine(ctx.filter, fn);\n}\n\nexport function addReplayFilter (ctx: CollectionContext, factory, isLimitFilter?) {\n  var curr = ctx.replayFilter;\n  ctx.replayFilter = curr ? ()=>combine(curr(), factory()) : factory;\n  ctx.justLimit = isLimitFilter && !curr;\n}\n\nexport function addMatchFilter(ctx: CollectionContext, fn) {\n  ctx.isMatch = combine(ctx.isMatch, fn);\n}\n\nexport function getIndexOrStore(ctx: CollectionContext, coreSchema: DBCoreTableSchema) {\n  // TODO: Rewrite this. No need to know ctx.isPrimKey. ctx.index should hold the keypath.\n  // Still, throw if not found!\n  if (ctx.isPrimKey) return coreSchema.primaryKey;\n  const index = coreSchema.getIndexByKeyPath(ctx.index);\n  if (!index) throw new exceptions.Schema(\"KeyPath \" + ctx.index + \" on object store \" + coreSchema.name + \" is not indexed\");\n  return index;\n}\n\nexport function openCursor(ctx: CollectionContext, coreTable: DBCoreTable, trans: DBCoreTransaction) {\n  const index = getIndexOrStore(ctx, coreTable.schema);\n  return coreTable.openCursor({\n    trans,\n    values: !ctx.keysOnly,\n    reverse: ctx.dir === 'prev',\n    unique: !!ctx.unique,\n    query: {\n      index, \n      range: ctx.range\n    }\n  });\n}\n\nexport function iter (\n  ctx: CollectionContext, \n  fn: (item, cursor: DBCoreCursor, advance: Function)=>void,\n  coreTrans: DBCoreTransaction,\n  coreTable: DBCoreTable): Promise<any>\n{\n  const filter = ctx.replayFilter ? combine(ctx.filter, ctx.replayFilter()) : ctx.filter;\n  if (!ctx.or) {\n      return iterate(\n        openCursor(ctx, coreTable, coreTrans),\n        combine(ctx.algorithm, filter), fn, !ctx.keysOnly && ctx.valueMapper);\n  } else {\n      const set = {};\n\n      const union = (item: any, cursor: DBCoreCursor, advance) => {\n          if (!filter || filter(cursor, advance, result=>cursor.stop(result), err => cursor.fail(err))) {\n              var primaryKey = cursor.primaryKey;\n              var key = '' + primaryKey;\n              if (key === '[object ArrayBuffer]') key = '' + new Uint8Array(primaryKey);\n              if (!hasOwn(set, key)) {\n                  set[key] = true;\n                  fn(item, cursor, advance);\n              }\n          }\n      }\n\n      return Promise.all([\n        ctx.or._iterate(union, coreTrans),\n        iterate(openCursor(ctx, coreTable, coreTrans), ctx.algorithm, union, !ctx.keysOnly && ctx.valueMapper)\n      ]);\n  }\n}\n\nfunction iterate(cursorPromise: Promise<DBCoreCursor>, filter, fn, valueMapper): Promise<any> {\n  \n  // Apply valueMapper (hook('reading') or mappped class)\n  var mappedFn = valueMapper ? (x,c,a) => fn(valueMapper(x),c,a) : fn;\n  // Wrap fn with PSD and microtick stuff from Promise.\n  var wrappedFn = wrap(mappedFn);\n  \n  return cursorPromise.then(cursor => {\n    if (cursor) {\n      return cursor.start(()=>{\n        var c = ()=>cursor.continue();\n        if (!filter || filter(cursor, advancer => c = advancer, val=>{cursor.stop(val);c=nop}, e => {cursor.fail(e);c = nop;}))\n          wrappedFn(cursor.value, cursor, advancer => c = advancer);\n        c();\n      });\n    }\n  });\n}\n", "// Implementation of https://www.w3.org/TR/IndexedDB-3/#compare-two-keys\n\nimport { toStringTag } from './utils';\n\n// ... with the adjustment to return NaN instead of throwing.\nexport function cmp(a: any, b: any): number {\n  try {\n    const ta = type(a);\n    const tb = type(b);\n    if (ta !== tb) {\n      if (ta === 'Array') return 1;\n      if (tb === 'Array') return -1;\n      if (ta === 'binary') return 1;\n      if (tb === 'binary') return -1;\n      if (ta === 'string') return 1;\n      if (tb === 'string') return -1;\n      if (ta === 'Date') return 1;\n      if (tb !== 'Date') return NaN;\n      return -1;\n    }\n    switch (ta) {\n      case 'number':\n      case 'Date':\n      case 'string':\n        return a > b ? 1 : a < b ? -1 : 0;\n      case 'binary': {\n        return compareUint8Arrays(getUint8Array(a), getUint8Array(b));\n      }\n      case 'Array':\n        return compareArrays(a, b);\n    }\n  } catch {}\n  return NaN; // Return value if any given args are valid keys.\n}\n\nexport function compareArrays(a: any[], b: any[]): number {\n  const al = a.length;\n  const bl = b.length;\n  const l = al < bl ? al : bl;\n  for (let i = 0; i < l; ++i) {\n    const res = cmp(a[i], b[i]);\n    if (res !== 0) return res;\n  }\n  return al === bl ? 0 : al < bl ? -1 : 1;\n}\n\nexport function compareUint8Arrays(\n  a: Uint8Array,\n  b: Uint8Array\n) {\n  const al = a.length;\n  const bl = b.length;\n  const l = al < bl ? al : bl;\n  for (let i = 0; i < l; ++i) {\n    if (a[i] !== b[i]) return a[i] < b[i] ? -1 : 1;\n  }\n  return al === bl ? 0 : al < bl ? -1 : 1;\n}\n\n// Implementation of https://www.w3.org/TR/IndexedDB-3/#key-type\nfunction type(x: any) {\n  const t = typeof x;\n  if (t !== 'object') return t;\n  if (ArrayBuffer.isView(x)) return 'binary';\n  const tsTag = toStringTag(x); // Cannot use instanceof in Safari\n  return tsTag === 'ArrayBuffer' ? 'binary' : (tsTag as 'Array' | 'Date');\n}\n\ntype BinaryType =\n  | ArrayBuffer\n  | DataView\n  | Uint8ClampedArray\n  | ArrayBufferView\n  | Uint8Array\n  | Int8Array\n  | Uint16Array\n  | Int16Array\n  | Uint32Array\n  | Int32Array\n  | Float32Array\n  | Float64Array;\n\nfunction getUint8Array(a: BinaryType): Uint8Array {\n  if (a instanceof Uint8Array) return a;\n  if (ArrayBuffer.isView(a))\n    // TypedArray or DataView\n    return new Uint8Array(a.buffer, a.byteOffset, a.byteLength);\n  return new Uint8Array(a); // ArrayBuffer\n}\n", "import { Collection as ICollection } from \"../../public/types/collection\";\nimport { <PERSON>ie } from \"../dexie\";\nimport { Table } from \"../table\";\nimport { IndexableType, IndexableTypeArrayReadonly } from \"../../public/types/indexable-type\";\nimport { PromiseExtended } from \"../../public/types/promise-extended\";\nimport { iter, isPlainKeyRange, getIndexOrStore, addReplayFilter, addFilter, addMatchFilter } from \"./collection-helpers\";\nimport { rejection } from \"../../helpers/promise\";\nimport { combine } from \"../../functions/combine\";\nimport { extend, hasOwn, deepClone, keys, setByKeyPath, getByKeyPath } from \"../../functions/utils\";\nimport { ModifyError } from \"../../errors\";\nimport { hangsOnDeleteLargeKeyRange } from \"../../globals/constants\";\nimport { ThenShortcut } from \"../../public/types/then-shortcut\";\nimport { Transaction } from '../transaction';\nimport { DBCoreCursor, DBCoreTransaction, DBCoreRangeType, DBCoreMutateResponse, DBCoreKeyRange } from '../../public/types/dbcore';\nimport { cmp } from \"../../functions/cmp\";\n\n/** class Collection\n * \n * https://dexie.org/docs/Collection/Collection\n */\nexport class Collection implements ICollection {\n  db: Dexie;\n  _ctx: {\n    table: Table;\n    index?: string | null;\n    isPrimKey?: boolean;\n    range: DBCoreKeyRange;\n    keysOnly: boolean;\n    dir: \"next\" | \"prev\";\n    unique: \"\" | \"unique\";\n    algorithm?: Function | null;\n    filter?: Function | null;\n    replayFilter: Function | null;\n    justLimit: boolean; // True if a replayFilter is just a filter that performs a \"limit\" operation (or none at all)\n    isMatch: Function | null;\n    offset: number,\n    limit: number,\n    error: any, // If set, any promise must be rejected with this error\n    or: Collection,\n    valueMapper: (any) => any\n  }\n  \n  _ondirectionchange?: Function;\n\n  _read<T>(fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<T>, cb?): PromiseExtended<T> {\n    var ctx = this._ctx;\n    return ctx.error ?\n      ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n      ctx.table._trans('readonly', fn).then(cb);\n  }\n\n  _write<T>(fn: (idbtrans: IDBTransaction, dxTrans: Transaction) => PromiseLike<T>): PromiseExtended<T> {\n    var ctx = this._ctx;\n    return ctx.error ?\n      ctx.table._trans(null, rejection.bind(null, ctx.error)) :\n      ctx.table._trans('readwrite', fn, \"locked\"); // When doing write operations on collections, always lock the operation so that upcoming operations gets queued.\n  }\n\n  _addAlgorithm(fn) {\n    var ctx = this._ctx;\n    ctx.algorithm = combine(ctx.algorithm, fn);\n  }\n\n  _iterate(\n    fn: (item, cursor: DBCoreCursor, advance: Function) => void,\n    coreTrans: DBCoreTransaction) : Promise<any>\n  {\n    return iter(this._ctx, fn, coreTrans, this._ctx.table.core);\n  }\n\n  /** Collection.clone()\n   * \n   * https://dexie.org/docs/Collection/Collection.clone()\n   * \n   **/\n  clone(props?) {\n    var rv = Object.create(this.constructor.prototype),\n      ctx = Object.create(this._ctx);\n    if (props) extend(ctx, props);\n    rv._ctx = ctx;\n    return rv;\n  }\n\n  /** Collection.raw()\n   * \n   * https://dexie.org/docs/Collection/Collection.raw()\n   * \n   **/\n  raw() {\n    this._ctx.valueMapper = null;\n    return this;\n  }\n\n  /** Collection.each()\n   * \n   * https://dexie.org/docs/Collection/Collection.each()\n   * \n   **/\n  each(fn: (obj, cursor: DBCoreCursor) => any): PromiseExtended<void> {\n    var ctx = this._ctx;\n\n    return this._read(trans => iter(ctx, fn, trans, ctx.table.core));\n  }\n\n  /** Collection.count()\n   * \n   * https://dexie.org/docs/Collection/Collection.count()\n   * \n   **/\n  count(cb?) {\n    return this._read(trans => {\n      const ctx = this._ctx;\n      const coreTable = ctx.table.core;\n      if (isPlainKeyRange(ctx, true)) {\n        // This is a plain key range. We can use the count() method if the index.\n        return coreTable.count({\n          trans,\n          query: {\n            index: getIndexOrStore(ctx, coreTable.schema),\n            range: ctx.range\n          }\n        }).then(count => Math.min(count, ctx.limit));\n      } else {\n        // Algorithms, filters or expressions are applied. Need to count manually.\n        var count = 0;\n        return iter(ctx, () => { ++count; return false; }, trans, coreTable)\n        .then(()=>count);\n      }\n    }).then(cb);\n  }\n\n  /** Collection.sortBy()\n   * \n   * https://dexie.org/docs/Collection/Collection.sortBy()\n   * \n   **/\n  sortBy(keyPath: string): PromiseExtended<any[]>;\n  sortBy<R>(keyPath: string, thenShortcut: ThenShortcut<any[], R>) : PromiseExtended<R>;\n  sortBy(keyPath: string, cb?: ThenShortcut<any[], any>) {\n    const parts = keyPath.split('.').reverse(),\n      lastPart = parts[0],\n      lastIndex = parts.length - 1;\n    function getval(obj, i) {\n      if (i) return getval(obj[parts[i]], i - 1);\n      return obj[lastPart];\n    }\n    var order = this._ctx.dir === \"next\" ? 1 : -1;\n\n    function sorter(a, b) {\n      var aVal = getval(a, lastIndex),\n        bVal = getval(b, lastIndex);\n      return aVal < bVal ? -order : aVal > bVal ? order : 0;\n    }\n    return this.toArray(function (a) {\n      return a.sort(sorter);\n    }).then(cb);\n  }\n\n  /** Collection.toArray()\n   * \n   * https://dexie.org/docs/Collection/Collection.toArray()\n   * \n   **/\n  toArray(cb?): PromiseExtended<any[]> {\n    return this._read(trans => {\n      var ctx = this._ctx;\n      if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n        // Special optimation if we could use IDBObjectStore.getAll() or\n        // IDBKeyRange.getAll():\n        const {valueMapper} = ctx;\n        const index = getIndexOrStore(ctx, ctx.table.core.schema);\n        return ctx.table.core.query({\n          trans,\n          limit: ctx.limit,\n          values: true,\n          query: {\n            index,\n            range: ctx.range\n          }\n        }).then(({result}) => valueMapper ? result.map(valueMapper) : result);\n      } else {\n        // Getting array through a cursor.\n        const a = [];\n        return iter(ctx, item => a.push(item), trans, ctx.table.core).then(()=>a);\n      }\n    }, cb);\n  }\n\n  /** Collection.offset()\n   * \n   * https://dexie.org/docs/Collection/Collection.offset()\n   * \n   **/\n  offset(offset: number) : Collection{\n    var ctx = this._ctx;\n    if (offset <= 0) return this;\n    ctx.offset += offset; // For count()\n    if (isPlainKeyRange(ctx)) {\n      addReplayFilter(ctx, () => {\n        var offsetLeft = offset;\n        return (cursor, advance) => {\n          if (offsetLeft === 0) return true;\n          if (offsetLeft === 1) { --offsetLeft; return false; }\n          advance(() => {\n            cursor.advance(offsetLeft);\n            offsetLeft = 0;\n          });\n          return false;\n        };\n      });\n    } else {\n      addReplayFilter(ctx, () => {\n        var offsetLeft = offset;\n        return () => (--offsetLeft < 0);\n      });\n    }\n    return this;\n  }\n\n  /** Collection.limit()\n   * \n   * https://dexie.org/docs/Collection/Collection.limit()\n   * \n   **/\n  limit(numRows: number) : Collection {\n    this._ctx.limit = Math.min(this._ctx.limit, numRows); // For count()\n    addReplayFilter(this._ctx, () => {\n      var rowsLeft = numRows;\n      return function (cursor, advance, resolve) {\n        if (--rowsLeft <= 0) advance(resolve); // Stop after this item has been included\n        return rowsLeft >= 0; // If numRows is already below 0, return false because then 0 was passed to numRows initially. Otherwise we wouldnt come here.\n      };\n    }, true);\n    return this;\n  }\n\n  /** Collection.until()\n   * \n   * https://dexie.org/docs/Collection/Collection.until()\n   * \n   **/\n  until(filterFunction: (x) => boolean, bIncludeStopEntry?) {\n    addFilter(this._ctx, function (cursor, advance, resolve) {\n      if (filterFunction(cursor.value)) {\n        advance(resolve);\n        return bIncludeStopEntry;\n      } else {\n        return true;\n      }\n    });\n    return this;\n  }\n\n  /** Collection.first()\n   * \n   * https://dexie.org/docs/Collection/Collection.first()\n   * \n   **/\n  first(cb?) {\n    return this.limit(1).toArray(function (a) { return a[0]; }).then(cb);\n  }\n\n  /** Collection.last()\n   * \n   * https://dexie.org/docs/Collection/Collection.last()\n   * \n   **/\n  last(cb?) {\n    return this.reverse().first(cb);\n  }\n\n  /** Collection.filter()\n   * \n   * https://dexie.org/docs/Collection/Collection.filter()\n   * \n   **/\n  filter(filterFunction: (x) => boolean): Collection {\n    /// <param name=\"jsFunctionFilter\" type=\"Function\">function(val){return true/false}</param>\n    addFilter(this._ctx, function (cursor) {\n      return filterFunction(cursor.value);\n    });\n    // match filters not used in Dexie.js but can be used by 3rd part libraries to test a\n    // collection for a match without querying DB. Used by Dexie.Observable.\n    addMatchFilter(this._ctx, filterFunction);\n    return this;\n  }\n\n  /** Collection.and()\n   * \n   * https://dexie.org/docs/Collection/Collection.and()\n   * \n   **/\n  and(filter: (x) => boolean) {\n    return this.filter(filter);\n  }\n\n  /** Collection.or()\n   * \n   * https://dexie.org/docs/Collection/Collection.or()\n   * \n   **/\n  or(indexName: string) {\n    return new this.db.WhereClause(this._ctx.table, indexName, this);\n  }\n\n  /** Collection.reverse()\n   * \n   * https://dexie.org/docs/Collection/Collection.reverse()\n   * \n   **/\n  reverse() {\n    this._ctx.dir = (this._ctx.dir === \"prev\" ? \"next\" : \"prev\");\n    if (this._ondirectionchange) this._ondirectionchange(this._ctx.dir);\n    return this;\n  }\n\n  /** Collection.desc()\n   * \n   * https://dexie.org/docs/Collection/Collection.desc()\n   * \n   **/\n  desc() {\n    return this.reverse();\n  }\n\n  /** Collection.eachKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachKey()\n   * \n   **/\n  eachKey(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    return this.each(function (val, cursor) { cb(cursor.key, cursor); });\n  }\n\n  /** Collection.eachUniqueKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachUniqueKey()\n   * \n   **/\n  eachUniqueKey(cb?) {\n    this._ctx.unique = \"unique\";\n    return this.eachKey(cb);\n  }\n\n  /** Collection.eachPrimaryKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.eachPrimaryKey()\n   * \n   **/\n  eachPrimaryKey(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    return this.each(function (val, cursor) { cb(cursor.primaryKey, cursor); });\n  }\n\n  /** Collection.keys()\n   * \n   * https://dexie.org/docs/Collection/Collection.keys()\n   * \n   **/\n  keys(cb?) {\n    var ctx = this._ctx;\n    ctx.keysOnly = !ctx.isMatch;\n    var a = [];\n    return this.each(function (item, cursor) {\n      a.push(cursor.key);\n    }).then(function () {\n      return a;\n    }).then(cb);\n  }\n\n  /** Collection.primaryKeys()\n   * \n   * https://dexie.org/docs/Collection/Collection.primaryKeys()\n   * \n   **/\n  primaryKeys(cb?) : PromiseExtended<IndexableType[]> {\n    var ctx = this._ctx;\n    if (ctx.dir === 'next' && isPlainKeyRange(ctx, true) && ctx.limit > 0) {\n      // Special optimation if we could use IDBObjectStore.getAllKeys() or\n      // IDBKeyRange.getAllKeys():\n      return this._read(trans => {\n        var index = getIndexOrStore(ctx, ctx.table.core.schema);\n        return ctx.table.core.query({\n          trans,\n          values: false,\n          limit: ctx.limit,\n          query: {\n            index,\n            range: ctx.range\n          }});\n      }).then(({result})=>result).then(cb);\n    }\n    ctx.keysOnly = !ctx.isMatch;\n    var a = [];\n    return this.each(function (item, cursor) {\n      a.push(cursor.primaryKey);\n    }).then(function () {\n      return a;\n    }).then(cb);\n  }\n\n  /** Collection.uniqueKeys()\n   * \n   * https://dexie.org/docs/Collection/Collection.uniqueKeys()\n   * \n   **/\n  uniqueKeys(cb?) {\n    this._ctx.unique = \"unique\";\n    return this.keys(cb);\n  }\n\n  /** Collection.firstKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.firstKey()\n   * \n   **/\n  firstKey(cb?) {\n    return this.limit(1).keys(function (a) { return a[0]; }).then(cb);\n  }\n\n  /** Collection.lastKey()\n   * \n   * https://dexie.org/docs/Collection/Collection.lastKey()\n   * \n   **/\n  lastKey(cb?) {\n    return this.reverse().firstKey(cb);\n  }\n\n  /** Collection.distinct()\n   * \n   * https://dexie.org/docs/Collection/Collection.distinct()\n   * \n   **/\n  distinct() {\n    var ctx = this._ctx,\n      idx = ctx.index && ctx.table.schema.idxByName[ctx.index];\n    if (!idx || !idx.multi) return this; // distinct() only makes differencies on multiEntry indexes.\n    var set = {};\n    addFilter(this._ctx, function (cursor: DBCoreCursor) {\n      var strKey = cursor.primaryKey.toString(); // Converts any Date to String, String to String, Number to String and Array to comma-separated string\n      var found = hasOwn(set, strKey);\n      set[strKey] = true;\n      return !found;\n    });\n    return this;\n  }\n\n  //\n  // Methods that mutate storage\n  //\n\n  /** Collection.modify()\n   * \n   * https://dexie.org/docs/Collection/Collection.modify()\n   * \n   **/\n  modify(changes: { [keyPath: string]: any }) : PromiseExtended<number>\n  modify(changes: (obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean): PromiseExtended<number> {\n    var ctx = this._ctx;\n    return this._write(trans => {\n      var modifyer: (obj: any, ctx:{value: any, primKey: IndexableType}) => void | boolean\n      if (typeof changes === 'function') {\n        // Changes is a function that may update, add or delete propterties or even require a deletion the object itself (delete this.item)\n        modifyer = changes;\n      } else {\n        // changes is a set of {keyPath: value} and no one is listening to the updating hook.\n        var keyPaths = keys(changes);\n        var numKeys = keyPaths.length;\n        modifyer = function (item) {\n          var anythingModified = false;\n          for (var i = 0; i < numKeys; ++i) {\n            var keyPath = keyPaths[i], val = changes[keyPath];\n            if (getByKeyPath(item, keyPath) !== val) {\n              setByKeyPath(item, keyPath, val); // Adding {keyPath: undefined} means that the keyPath should be deleted. Handled by setByKeyPath\n              anythingModified = true;\n            }\n          }\n          return anythingModified;\n        };\n      }\n\n      const coreTable = ctx.table.core;\n      const {outbound, extractKey} = coreTable.schema.primaryKey;\n      const limit = this.db._options.modifyChunkSize || 200;\n      const totalFailures = [];\n      let successCount = 0;\n      const failedKeys: IndexableType[] = [];\n      const applyMutateResult = (expectedCount: number, res: DBCoreMutateResponse) => {\n        const {failures, numFailures} = res;\n        successCount += expectedCount - numFailures;\n        for (let pos of keys(failures)) {\n          totalFailures.push(failures[pos]);\n        }\n      }\n      return this.clone().primaryKeys().then(keys => {\n\n        const nextChunk = (offset: number) => {\n          const count = Math.min(limit, keys.length - offset);\n          return coreTable.getMany({\n            trans,\n            keys: keys.slice(offset, offset + count),\n            cache: \"immutable\" // Optimize for 2 things:\n            // 1) observability-middleware can track changes better.\n            // 2) hooks middleware don't have to query the existing values again when tracking changes.\n            // We can use \"immutable\" because we promise to not touch the values we retrieve here!\n          }).then(values => {\n            const addValues = [];\n            const putValues = [];\n            const putKeys = outbound ? [] : null;\n            const deleteKeys = [];\n            for (let i=0; i<count; ++i) {\n              const origValue = values[i];\n              const ctx = {\n                value: deepClone(origValue),\n                primKey: keys[offset+i]\n              };\n              if (modifyer.call(ctx, ctx.value, ctx) !== false) {\n                if (ctx.value == null) {\n                  // Deleted\n                  deleteKeys.push(keys[offset+i]);\n                } else if (!outbound && cmp(extractKey(origValue), extractKey(ctx.value)) !== 0) {\n                  // Changed primary key of inbound\n                  deleteKeys.push(keys[offset+i]);\n                  addValues.push(ctx.value)\n                } else {\n                  // Changed value\n                  putValues.push(ctx.value);\n                  if (outbound) putKeys.push(keys[offset+i]);\n                }\n              }\n            }\n            const criteria = isPlainKeyRange(ctx) &&\n              ctx.limit === Infinity &&\n              (typeof changes !== 'function' || changes === deleteCallback) && {\n                index: ctx.index,\n                range: ctx.range\n              };\n\n            return Promise.resolve(addValues.length > 0 &&\n              coreTable.mutate({trans, type: 'add', values: addValues})\n                .then(res => {\n                  for (let pos in res.failures) {\n                    // Remove from deleteKeys the key of the object that failed to change its primary key\n                    deleteKeys.splice(parseInt(pos), 1);\n                  }\n                  applyMutateResult(addValues.length, res);\n                })\n            ).then(()=>(putValues.length > 0 || (criteria && typeof changes === 'object')) &&\n                coreTable.mutate({\n                  trans,\n                  type: 'put',\n                  keys: putKeys,\n                  values: putValues,\n                  criteria,\n                  changeSpec: typeof changes !== 'function'\n                    && changes\n                }).then(res=>applyMutateResult(putValues.length, res))\n            ).then(()=>(deleteKeys.length > 0 || (criteria && changes === deleteCallback)) &&\n                coreTable.mutate({\n                  trans,\n                  type: 'delete',\n                  keys: deleteKeys,\n                  criteria\n                }).then(res=>applyMutateResult(deleteKeys.length, res))\n            ).then(()=>{\n              return keys.length > offset + count && nextChunk(offset + limit);\n            });\n          });\n        }\n\n        return nextChunk(0).then(()=>{\n          if (totalFailures.length > 0)\n            throw new ModifyError(\"Error modifying one or more objects\", totalFailures, successCount, failedKeys as IndexableTypeArrayReadonly);\n\n          return keys.length;\n        });\n      });\n\n    });\n  }\n\n  /** Collection.delete()\n   * \n   * https://dexie.org/docs/Collection/Collection.delete()\n   * \n   **/\n  delete() : PromiseExtended<number> {\n    var ctx = this._ctx,\n      range = ctx.range;\n      //deletingHook = ctx.table.hook.deleting.fire,\n      //hasDeleteHook = deletingHook !== nop;\n    if (isPlainKeyRange(ctx) &&\n      ((ctx.isPrimKey && !hangsOnDeleteLargeKeyRange) || range.type === DBCoreRangeType.Any)) // if no range, we'll use clear().\n    {\n      // May use IDBObjectStore.delete(IDBKeyRange) in this case (Issue #208)\n      // For chromium, this is the way most optimized version.\n      // For IE/Edge, this could hang the indexedDB engine and make operating system instable\n      // (https://gist.github.com/dfahlander/5a39328f029de18222cf2125d56c38f7)\n      return this._write(trans => {\n        // Our API contract is to return a count of deleted items, so we have to count() before delete().\n        const {primaryKey} = ctx.table.core.schema;\n        const coreRange = range;\n        return ctx.table.core.count({trans, query: {index: primaryKey, range: coreRange}}).then(count => {\n          return ctx.table.core.mutate({trans, type: 'deleteRange', range: coreRange})\n          .then(({failures, lastResult, results, numFailures}) => {\n            if (numFailures) throw new ModifyError(\"Could not delete some values\",\n              Object.keys(failures).map(pos => failures[pos]),\n              count - numFailures);\n            return count - numFailures;\n          });\n        });\n      });\n    }\n\n    return this.modify(deleteCallback);\n  }\n}\n\nconst deleteCallback = (value, ctx) => ctx.value = null;\n", "import { <PERSON><PERSON> } from '../../classes/dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Collection } from './collection';\nimport { WhereClause } from '../where-clause/where-clause';\nimport { AnyRange } from '../../dbcore/keyrange';\nimport { DBCoreKeyRange } from '../../public/types/dbcore';\nimport { mirror } from '../../functions/chaining-functions';\n\n/** Constructs a Collection instance. */\nexport interface CollectionConstructor {\n  new(whereClause?: WhereClause | null, keyRangeGenerator?: () => DBCoreKeyRange): Collection;\n  prototype: Collection;\n}\n\n/** Generates a Collection constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createCollectionConstructor(db: Dexie) {\n  return makeClassConstructor<CollectionConstructor>(\n    Collection.prototype,\n\n    function Collection(\n      this: Collection,\n      whereClause?: WhereClause | null,\n      keyRangeGenerator?: () => DBCoreKeyRange)\n    {\n      this.db = db;\n      let keyRange = AnyRange, error = null;\n      if (keyRangeGenerator) try {\n        keyRange = keyRangeGenerator();\n      } catch (ex) {\n        error = ex;\n      }\n\n      const whereCtx = whereClause._ctx;\n      const table = whereCtx.table;\n      const readingHook = table.hook.reading.fire;\n      this._ctx = {\n        table: table,\n        index: whereCtx.index,\n        isPrimKey: (!whereCtx.index || (table.schema.primKey.keyPath && whereCtx.index === table.schema.primKey.name)),\n        range: keyRange,\n        keysOnly: false,\n        dir: \"next\",\n        unique: \"\",\n        algorithm: null,\n        filter: null,\n        replayFilter: null,\n        justLimit: true, // True if a replayFilter is just a filter that performs a \"limit\" operation (or none at all)\n        isMatch: null,\n        offset: 0,\n        limit: Infinity,\n        error: error, // If set, any promise must be rejected with this error\n        or: whereCtx.or,\n        valueMapper: readingHook !== mirror ? readingHook : null\n      };\n    }\n  );\n}\n", "import { IndexableType } from '../public/types/indexable-type';\n\nexport function simpleCompare(a, b) {\n  return a < b ? -1 : a === b ? 0 : 1;\n}\n\nexport function simpleCompareReverse(a, b) {\n  return a > b ? -1 : a === b ? 0 : 1;\n}\n", "import { WhereClause } from './where-clause';\nimport { Collection } from '../collection';\nimport { STRING_EXPECTED } from '../../globals/constants';\nimport { simpleCompare, simpleCompareReverse } from '../../functions/compare-functions';\nimport { IndexableType } from '../../public';\nimport { DBCoreKeyRange, DBCoreRangeType } from '../../public/types/dbcore';\n\nexport function fail(collectionOrWhereClause: Collection | WhereClause, err, T?) {\n  var collection = collectionOrWhereClause instanceof WhereClause ?\n      new collectionOrWhereClause.Collection (collectionOrWhereClause) :\n      collectionOrWhereClause;\n      \n  collection._ctx.error = T ? new T(err) : new TypeError(err);\n  return collection;\n}\n\nexport function emptyCollection(whereClause: WhereClause) {\n  return new whereClause.Collection (whereClause, () => rangeEqual(\"\")).limit(0);\n}\n\nexport function upperFactory(dir: 'next' | 'prev') {\n  return dir === \"next\" ?\n    (s: string) => s.toUpperCase() :\n    (s: string) => s.toLowerCase();\n}\n\nexport function lowerFactory(dir: 'next' | 'prev') {\n  return dir === \"next\" ?\n    (s: string) => s.toLowerCase() :\n    (s: string) => s.toUpperCase();\n}\n\nexport function nextCasing(key, lowerKey, upperNeedle, lowerNeedle, cmp, dir) {\n  var length = Math.min(key.length, lowerNeedle.length);\n  var llp = -1;\n  for (var i = 0; i < length; ++i) {\n      var lwrKeyChar = lowerKey[i];\n      if (lwrKeyChar !== lowerNeedle[i]) {\n          if (cmp(key[i], upperNeedle[i]) < 0) return key.substr(0, i) + upperNeedle[i] + upperNeedle.substr(i + 1);\n          if (cmp(key[i], lowerNeedle[i]) < 0) return key.substr(0, i) + lowerNeedle[i] + upperNeedle.substr(i + 1);\n          if (llp >= 0) return key.substr(0, llp) + lowerKey[llp] + upperNeedle.substr(llp + 1);\n          return null;\n      }\n      if (cmp(key[i], lwrKeyChar) < 0) llp = i;\n  }\n  if (length < lowerNeedle.length && dir === \"next\") return key + upperNeedle.substr(key.length);\n  if (length < key.length && dir === \"prev\") return key.substr(0, upperNeedle.length);\n  return (llp < 0 ? null : key.substr(0, llp) + lowerNeedle[llp] + upperNeedle.substr(llp + 1));\n}\n\nexport function addIgnoreCaseAlgorithm(whereClause: WhereClause, match, needles, suffix) {\n  /// <param name=\"needles\" type=\"Array\" elementType=\"String\"></param>\n  var upper, lower, compare, upperNeedles, lowerNeedles, direction, nextKeySuffix,\n      needlesLen = needles.length;\n  if (!needles.every(s => typeof s === 'string')) {\n      return fail(whereClause, STRING_EXPECTED);\n  }\n  function initDirection(dir) {\n      upper = upperFactory(dir);\n      lower = lowerFactory(dir);\n      compare = (dir === \"next\" ? simpleCompare : simpleCompareReverse);\n      var needleBounds = needles.map(function (needle){\n          return {lower: lower(needle), upper: upper(needle)};\n      }).sort(function(a,b) {\n          return compare(a.lower, b.lower);\n      });\n      upperNeedles = needleBounds.map(function (nb){ return nb.upper; });\n      lowerNeedles = needleBounds.map(function (nb){ return nb.lower; });\n      direction = dir;\n      nextKeySuffix = (dir === \"next\" ? \"\" : suffix);\n  }\n  initDirection(\"next\");\n\n  var c = new whereClause.Collection (\n      whereClause,\n      ()=>createRange(upperNeedles[0], lowerNeedles[needlesLen-1] + suffix)\n  );\n\n  c._ondirectionchange = function (direction) {\n      // This event onlys occur before filter is called the first time.\n      initDirection(direction);\n  };\n\n  var firstPossibleNeedle = 0;\n\n  c._addAlgorithm(function (cursor, advance, resolve) {\n      /// <param name=\"cursor\" type=\"IDBCursor\"></param>\n      /// <param name=\"advance\" type=\"Function\"></param>\n      /// <param name=\"resolve\" type=\"Function\"></param>\n      var key = cursor.key;\n      if (typeof key !== 'string') return false;\n      var lowerKey = lower(key);\n      if (match(lowerKey, lowerNeedles, firstPossibleNeedle)) {\n          return true;\n      } else {\n          var lowestPossibleCasing = null;\n          for (var i=firstPossibleNeedle; i<needlesLen; ++i) {\n              var casing = nextCasing(key, lowerKey, upperNeedles[i], lowerNeedles[i], compare, direction);\n              if (casing === null && lowestPossibleCasing === null)\n                  firstPossibleNeedle = i + 1;\n              else if (lowestPossibleCasing === null || compare(lowestPossibleCasing, casing) > 0) {\n                  lowestPossibleCasing = casing;\n              }\n          }\n          if (lowestPossibleCasing !== null) {\n              advance(function () { cursor.continue(lowestPossibleCasing + nextKeySuffix); });\n          } else {\n              advance(resolve);\n          }\n          return false;\n      }\n  });\n  return c;\n}\n\nexport function createRange (lower: IndexableType, upper: IndexableType, lowerOpen?: boolean, upperOpen?: boolean): DBCoreKeyRange {\n    return {\n        type: DBCoreRangeType.Range,\n        lower,\n        upper,\n        lowerOpen,\n        upperOpen\n    };\n}\n\nexport function rangeEqual (value: IndexableType) : DBCoreKeyRange {\n    return {\n        type: DBCoreRangeType.Equal,\n        lower: value,\n        upper: value\n    };\n}\n", "import { WhereClause as IWhere<PERSON>lause } from \"../../public/types/where-clause\";\nimport { Collection } from \"../collection\";\nimport { Table } from \"../table\";\nimport { IndexableType } from \"../../public/types/indexable-type\";\nimport { emptyCollection, fail, addIgnoreCaseAlgorithm, createRange, rangeEqual } from './where-clause-helpers';\nimport { INVALID_KEY_ARGUMENT, STRING_EXPECTED, maxString, minKey } from '../../globals/constants';\nimport { getArrayOf, NO_CHAR_ARRAY } from '../../functions/utils';\nimport { exceptions } from '../../errors';\nimport { Dexie } from '../dexie';\nimport { Collection as ICollection} from \"../../public/types/collection\";\n\n/** class WhereClause\n * \n * https://dexie.org/docs/WhereClause/WhereClause\n */\nexport class Where<PERSON><PERSON><PERSON> implements IWhereClause {\n  db: Dexie;\n  _IDBKeyRange: typeof IDBKeyRange;\n  _ctx: {\n    table: Table;\n    index: string;\n    or: Collection;\n  }\n  _cmp: (a: IndexableType, b: IndexableType) => number;\n  _ascending: (a: IndexableType, b: IndexableType) => number;\n  _descending: (a: IndexableType, b: IndexableType) => number;\n  _min: (a: IndexableType, b: IndexableType) => IndexableType;\n  _max: (a: IndexableType, b: IndexableType) => IndexableType;\n\n  get Collection() {\n    return this._ctx.table.db.Collection;\n  }\n\n  /** WhereClause.between()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.between()\n   * \n   **/\n  between(lower: IndexableType, upper: IndexableType, includeLower?: boolean, includeUpper?: boolean) {\n    includeLower = includeLower !== false;   // Default to true\n    includeUpper = includeUpper === true;    // Default to false\n    try {\n      if ((this._cmp(lower, upper) > 0) ||\n        (this._cmp(lower, upper) === 0 && (includeLower || includeUpper) && !(includeLower && includeUpper)))\n        return emptyCollection(this); // Workaround for idiotic W3C Specification that DataError must be thrown if lower > upper. The natural result would be to return an empty collection.\n      return new this.Collection(this, ()=>createRange(lower, upper, !includeLower, !includeUpper));\n    } catch (e) {\n      return fail(this, INVALID_KEY_ARGUMENT);\n    }\n  }\n\n  /** WhereClause.equals()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.equals()\n   * \n   **/\n  equals(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => rangeEqual(value)) as ICollection;\n  }\n\n  /** WhereClause.above()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.above()\n   * \n   **/\n  above(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(value, undefined, true));\n  }\n\n  /** WhereClause.aboveOrEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.aboveOrEqual()\n   * \n   **/\n  aboveOrEqual(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(value, undefined, false));\n  }\n\n  /** WhereClause.below()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.below()\n   * \n   **/\n  below(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(undefined, value, false, true));\n  }\n\n  /** WhereClause.belowOrEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.belowOrEqual()\n   * \n   **/\n  belowOrEqual(value: IndexableType) {\n    if (value == null) return fail(this, INVALID_KEY_ARGUMENT);\n    return new this.Collection(this, () => createRange(undefined, value));\n  }\n\n  /** WhereClause.startsWith()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWith()\n   * \n   **/\n  startsWith(str: string) {\n    if (typeof str !== 'string') return fail(this, STRING_EXPECTED);\n    return this.between(str, str + maxString, true, true);\n  }\n\n  /** WhereClause.startsWithIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithIgnoreCase()\n   * \n   **/\n  startsWithIgnoreCase(str: string) {\n    if (str === \"\") return this.startsWith(str);\n    return addIgnoreCaseAlgorithm(this, (x, a) => x.indexOf(a[0]) === 0, [str], maxString);\n  }\n\n  /** WhereClause.equalsIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.equalsIgnoreCase()\n   * \n   **/\n  equalsIgnoreCase(str: string) {\n    return addIgnoreCaseAlgorithm(this, (x, a) => x === a[0], [str], \"\");\n  }\n\n  /** WhereClause.anyOfIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.anyOfIgnoreCase()\n   * \n   **/\n  anyOfIgnoreCase(...values: string[]): Collection;\n  anyOfIgnoreCase(values: string[]): Collection;\n  anyOfIgnoreCase() {\n    var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return emptyCollection(this);\n    return addIgnoreCaseAlgorithm(this, (x, a) => a.indexOf(x) !== -1, set, \"\");\n  }\n\n  /** WhereClause.startsWithAnyOfIgnoreCase()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithAnyOfIgnoreCase()\n   * \n   **/\n  startsWithAnyOfIgnoreCase(...values: string[]): Collection;\n  startsWithAnyOfIgnoreCase(values: string[]): Collection;\n  startsWithAnyOfIgnoreCase() {\n    var set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return emptyCollection(this);\n    return addIgnoreCaseAlgorithm(this, (x, a) => a.some(n => x.indexOf(n) === 0), set, maxString);\n  }\n\n  /** WhereClause.anyOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.anyOf()\n   * \n   **/\n  anyOf(...values: string[]): Collection;\n  anyOf(values: string[]): Collection;\n  anyOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    let compare = this._cmp;\n    try { set.sort(compare); } catch (e) { return fail(this, INVALID_KEY_ARGUMENT); }\n    if (set.length === 0) return emptyCollection(this);\n    const c = new this.Collection(this, () => createRange(set[0], set[set.length - 1]));\n\n    c._ondirectionchange = direction => {\n      compare = (direction === \"next\" ?\n        this._ascending :\n        this._descending);\n      set.sort(compare);\n    };\n\n    let i = 0;\n    c._addAlgorithm((cursor, advance, resolve) => {\n      const key = cursor.key;\n      while (compare(key, set[i]) > 0) {\n        // The cursor has passed beyond this key. Check next.\n        ++i;\n        if (i === set.length) {\n          // There is no next. Stop searching.\n          advance(resolve);\n          return false;\n        }\n      }\n      if (compare(key, set[i]) === 0) {\n        // The current cursor value should be included and we should continue a single step in case next item has the same key or possibly our next key in set.\n        return true;\n      } else {\n        // cursor.key not yet at set[i]. Forward cursor to the next key to hunt for.\n        advance(() => { cursor.continue(set[i]); });\n        return false;\n      }\n    });\n    return c;\n  }\n\n  /** WhereClause.notEqual()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.notEqual()\n   * \n   **/\n  notEqual(value: IndexableType) {\n    return this.inAnyRange([[minKey, value], [value, this.db._maxKey]], { includeLowers: false, includeUppers: false });\n  }\n\n  /** WhereClause.noneOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.noneOf()\n   * \n   **/\n  noneOf(...values: string[]): Collection;\n  noneOf(values: string[]): Collection;\n  noneOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n    if (set.length === 0) return new this.Collection(this); // Return entire collection.\n    try { set.sort(this._ascending); } catch (e) { return fail(this, INVALID_KEY_ARGUMENT); }\n    // Transform [\"a\",\"b\",\"c\"] to a set of ranges for between/above/below: [[minKey,\"a\"], [\"a\",\"b\"], [\"b\",\"c\"], [\"c\",maxKey]]\n    const ranges = set.reduce(\n      (res, val) => res ?\n        res.concat([[res[res.length - 1][1], val]]) :\n        [[minKey, val]],\n      null);\n    ranges.push([set[set.length - 1], this.db._maxKey]);\n    return this.inAnyRange(ranges, { includeLowers: false, includeUppers: false });\n  }\n\n  /** WhereClause.inAnyRange()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.inAnyRange()\n   * \n   **/\n  inAnyRange(\n    ranges: ReadonlyArray<{ 0: IndexableType, 1: IndexableType }>,\n    options?: { includeLowers?: boolean, includeUppers?: boolean })\n  {\n    const cmp = this._cmp,\n          ascending = this._ascending,\n          descending = this._descending,\n          min = this._min,\n          max = this._max;\n\n    if (ranges.length === 0) return emptyCollection(this);\n    if (!ranges.every(range =>\n      range[0] !== undefined &&\n      range[1] !== undefined &&\n      ascending(range[0], range[1]) <= 0)) {\n      return fail(\n        this,\n        \"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower\",\n        exceptions.InvalidArgument);\n    }\n    const includeLowers = !options || options.includeLowers !== false;   // Default to true\n    const includeUppers = options && options.includeUppers === true;    // Default to false\n\n    function addRange(ranges, newRange) {\n      let i = 0, l = ranges.length;\n      for (; i < l; ++i) {\n        const range = ranges[i];\n        if (cmp(newRange[0], range[1]) < 0 && cmp(newRange[1], range[0]) > 0) {\n          range[0] = min(range[0], newRange[0]);\n          range[1] = max(range[1], newRange[1]);\n          break;\n        }\n      }\n      if (i === l)\n        ranges.push(newRange);\n      return ranges;\n    }\n\n    let sortDirection = ascending;\n    function rangeSorter(a, b) { return sortDirection(a[0], b[0]); }\n\n    // Join overlapping ranges\n    let set;\n    try {\n      set = ranges.reduce(addRange, []);\n      set.sort(rangeSorter);\n    } catch (ex) {\n      return fail(this, INVALID_KEY_ARGUMENT);\n    }\n\n    let rangePos = 0;\n    const keyIsBeyondCurrentEntry = includeUppers ?\n      key => ascending(key, set[rangePos][1]) > 0 :\n      key => ascending(key, set[rangePos][1]) >= 0;\n\n    const keyIsBeforeCurrentEntry = includeLowers ?\n      key => descending(key, set[rangePos][0]) > 0 :\n      key => descending(key, set[rangePos][0]) >= 0;\n\n    function keyWithinCurrentRange(key) {\n      return !keyIsBeyondCurrentEntry(key) && !keyIsBeforeCurrentEntry(key);\n    }\n\n    let checkKey = keyIsBeyondCurrentEntry;\n\n    const c = new this.Collection(\n      this,\n      () => createRange(set[0][0], set[set.length - 1][1], !includeLowers, !includeUppers));\n\n    c._ondirectionchange = direction => {\n      if (direction === \"next\") {\n        checkKey = keyIsBeyondCurrentEntry;\n        sortDirection = ascending;\n      } else {\n        checkKey = keyIsBeforeCurrentEntry;\n        sortDirection = descending;\n      }\n      set.sort(rangeSorter);\n    };\n\n    c._addAlgorithm((cursor, advance, resolve) => {\n      var key = cursor.key;\n      while (checkKey(key)) {\n        // The cursor has passed beyond this key. Check next.\n        ++rangePos;\n        if (rangePos === set.length) {\n          // There is no next. Stop searching.\n          advance(resolve);\n          return false;\n        }\n      }\n      if (keyWithinCurrentRange(key)) {\n        // The current cursor value should be included and we should continue a single step in case next item has the same key or possibly our next key in set.\n        return true;\n      } else if (this._cmp(key, set[rangePos][1]) === 0 || this._cmp(key, set[rangePos][0]) === 0) {\n        // includeUpper or includeLower is false so keyWithinCurrentRange() returns false even though we are at range border.\n        // Continue to next key but don't include this one.\n        return false;\n      } else {\n        // cursor.key not yet at set[i]. Forward cursor to the next key to hunt for.\n        advance(() => {\n          if (sortDirection === ascending) cursor.continue(set[rangePos][0]);\n          else cursor.continue(set[rangePos][1]);\n        });\n        return false;\n      }\n    });\n    return c;\n  }\n\n  /** WhereClause.startsWithAnyOf()\n   * \n   * https://dexie.org/docs/WhereClause/WhereClause.startsWithAnyOf()\n   * \n   **/\n  startsWithAnyOf(...prefixes: string[]): Collection;\n  startsWithAnyOf(prefixes: string[]): Collection;\n  startsWithAnyOf() {\n    const set = getArrayOf.apply(NO_CHAR_ARRAY, arguments);\n\n    if (!set.every(s => typeof s === 'string')) {\n        return fail(this, \"startsWithAnyOf() only works with strings\");\n    }\n    if (set.length === 0) return emptyCollection(this);\n\n    return this.inAnyRange(set.map((str: string) => [str, str + maxString]));\n  }\n\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { WhereClause } from './where-clause';\nimport { Table } from '../table';\nimport { Collection } from '../collection';\nimport { exceptions } from '../../errors';\n\nexport interface WhereClauseConstructor {\n  new(table: Table, index?: string, orCollection?: Collection): WhereClause;\n  prototype: WhereClause;\n}\n\n/** Generates a WhereClause constructor.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createWhereClauseConstructor(db: Dexie) {\n  return makeClassConstructor<WhereClauseConstructor>(\n    WhereClause.prototype,\n\n    function WhereClause(this: WhereClause, table: Table, index?: string, orCollection?: Collection) {\n      this.db = db;\n      this._ctx = {\n        table: table,\n        index: index === \":id\" ? null : index,\n        or: orCollection\n      };\n      const indexedDB = db._deps.indexedDB;\n      if (!indexedDB) throw new exceptions.MissingAPI();\n      this._cmp = this._ascending = indexedDB.cmp.bind(indexedDB);\n      this._descending = (a, b) => indexedDB.cmp(b, a);\n      this._max = (a, b) => indexedDB.cmp(a,b) > 0 ? a : b;\n      this._min = (a, b) => indexedDB.cmp(a,b) < 0 ? a : b;\n      this._IDBKeyRange = db._deps.IDBKeyRange;\n    }\n  );\n}\n", "import { wrap } from \"../helpers/promise\";\n\nexport function eventReject<PERSON><PERSON>ler(reject) {\n  return wrap(function (event) {\n      preventDefault(event);\n      reject (event.target.error);\n      return false;\n  });\n}\n\nexport function eventSuccessHandler (resolve) {\n  return wrap(function (event){\n      resolve(event.target.result);\n  });\n}\n\nexport function hookedEventRejectHandler (reject) {\n  return wrap(function (event) {\n      // See comment on hookedEventSuccessHandler() why wrap() is needed only when supporting hooks.\n      \n      var req = event.target,\n          err = req.error,\n          ctx = req._hookCtx,// Contains the hook error handler. Put here instead of closure to boost performance.\n          hookErrorHandler = ctx && ctx.onerror;\n      hookErrorHandler && hookErrorHandler(err);\n      preventDefault(event);\n      reject (err);\n      return false;\n  });\n}\n\nexport function hookedEventSuccessHandler(resolve) {\n  // wrap() is needed when calling hooks because the rare scenario of:\n  //  * hook does a db operation that fails immediately (<PERSON><PERSON> throws exception)\n  //    For calling db operations on correct transaction, wrap makes sure to set PSD correctly.\n  //    wrap() will also execute in a virtual tick.\n  //  * If not wrapped in a virtual tick, direct exception will launch a new physical tick.\n  //  * If this was the last event in the bulk, the promise will resolve after a physical tick\n  //    and the transaction will have committed already.\n  // If no hook, the virtual tick will be executed in the reject()/resolve of the final promise,\n  // because it is always marked with _lib = true when created using Transaction._promise().\n  return wrap(function(event) {\n      var req = event.target,\n          ctx = req._hookCtx,// Contains the hook error handler. Put here instead of closure to boost performance.\n          result = ctx.value || req.result, // Pass the object value on updates. The result from IDB is the primary key.\n          hookSuccessHandler = ctx && ctx.onsuccess;\n      hookSuccessHandler && hookSuccessHandler(result);\n      resolve && resolve(result);\n  }, resolve);\n}\n\n\nexport function preventDefault(event) {\n  if (event.stopPropagation) // IndexedDBShim doesnt support this on Safari 8 and below.\n      event.stopPropagation();\n  if (event.preventDefault) // IndexedDBShim doesnt support this on Safari 8 and below.\n      event.preventDefault();\n}\n\nexport function BulkErrorHandlerCatchAll(errorList, done?, supportHooks?) {\n  return (supportHooks ? hookedEventRejectHandler : eventRejectHandler)(e => {\n      errorList.push(e);\n      done && done();\n  });\n}\n\n", "import Events from '../helpers/Events';\nimport { GlobalDexieEvents } from '../public/types/db-events';\n\nexport const DEXIE_STORAGE_MUTATED_EVENT_NAME = 'storagemutated' as 'storagemutated';\n\n// Name of the global event fired using DOM dispatchEvent (if not in node).\n// Reason for propagating this as a DOM event is for getting reactivity across\n// multiple versions of Dexie within the same app (as long as they are\n// compatible with regards to the event data).\n// If the ObservabilitySet protocol change in a way that would not be backward\n// compatible, make sure also update the event name to a new number at the end\n// so that two Dexie instances of different versions continue to work together\n//  - maybe not able to communicate but won't fail due to unexpected data in\n// the detail property of the CustomEvent. If so, also make sure to udpate\n// docs and explain at which Dexie version the new name and format of the event\n// is being used.\nexport const STORAGE_MUTATED_DOM_EVENT_NAME = 'x-storagemutated-1';\n\nexport const globalEvents = Events(null, DEXIE_STORAGE_MUTATED_EVENT_NAME) as GlobalDexieEvents;\n", "import { Transaction as ITransaction } from '../../public/types/transaction';\nimport { DexiePromise, wrap, rejection } from \"../../helpers/promise\";\nimport { DbSchema } from '../../public/types/db-schema';\nimport { assert, hasOwn } from '../../functions/utils';\nimport { PSD, usePSD } from '../../helpers/promise';\nimport { Dexie } from '../dexie';\nimport { exceptions } from '../../errors';\nimport { safariMultiStoreFix } from '../../functions/quirks';\nimport { preventDefault } from '../../functions/event-wrappers';\nimport { newScope } from '../../helpers/promise';\nimport * as Debug from '../../helpers/debug';\nimport { Table } from '../table';\nimport { globalEvents } from '../../globals/global-events';\n\n/** Transaction\n * \n * https://dexie.org/docs/Transaction/Transaction\n * \n **/\nexport class Transaction implements ITransaction {\n  db: <PERSON>ie;\n  active: boolean;\n  mode: IDBTransactionMode;\n  chromeTransactionDurability: ChromeTransactionDurability;\n  idbtrans: IDBTransaction;\n  storeNames: string[];\n  on: any;\n  parent?: Transaction;\n  schema: DbSchema;\n  _memoizedTables: {[tableName: string]: Table};\n\n  _reculock: number;\n  _blockedFuncs: { 0: () => any, 1: any }[];\n  _resolve: () => void;\n  _reject: (Error) => void;\n  _waitingFor: DexiePromise; // for waitFor()\n  _waitingQueue: Function[]; // for waitFor()\n  _spinCount: number; // Just for debugging waitFor()\n  _completion: DexiePromise;\n\n  //\n  // Transaction internal methods (not required by API users, but needed internally and eventually by dexie extensions)\n  //\n\n  /** Transaction._lock()\n   * \n   * Internal method.\n   */\n  _lock() {\n    assert(!PSD.global); // Locking and unlocking reuires to be within a PSD scope.\n    // Temporary set all requests into a pending queue if they are called before database is ready.\n    ++this._reculock; // Recursive read/write lock pattern using PSD (Promise Specific Data) instead of TLS (Thread Local Storage)\n    if (this._reculock === 1 && !PSD.global) PSD.lockOwnerFor = this;\n    return this;\n  }\n\n  /** Transaction._unlock()\n   * \n   * Internal method.\n   */\n  _unlock() {\n    assert(!PSD.global); // Locking and unlocking reuires to be within a PSD scope.\n    if (--this._reculock === 0) {\n      if (!PSD.global) PSD.lockOwnerFor = null;\n      while (this._blockedFuncs.length > 0 && !this._locked()) {\n        var fnAndPSD = this._blockedFuncs.shift();\n        try { usePSD(fnAndPSD[1], fnAndPSD[0]); } catch (e) { }\n      }\n    }\n    return this;\n  }\n\n  /** Transaction._lock()\n   * \n   * Internal method.\n   */\n  _locked() {\n    // Checks if any write-lock is applied on this transaction.\n    // To simplify the Dexie API for extension implementations, we support recursive locks.\n    // This is accomplished by using \"Promise Specific Data\" (PSD).\n    // PSD data is bound to a Promise and any child Promise emitted through then() or resolve( new Promise() ).\n    // PSD is local to code executing on top of the call stacks of any of any code executed by Promise():\n    //         * callback given to the Promise() constructor  (function (resolve, reject){...})\n    //         * callbacks given to then()/catch()/finally() methods (function (value){...})\n    // If creating a new independant Promise instance from within a Promise call stack, the new Promise will derive the PSD from the call stack of the parent Promise.\n    // Derivation is done so that the inner PSD __proto__ points to the outer PSD.\n    // PSD.lockOwnerFor will point to current transaction object if the currently executing PSD scope owns the lock.\n    return this._reculock && PSD.lockOwnerFor !== this;\n  }\n\n  /** Transaction.create()\n   * \n   * Internal method.\n   * \n   */\n  create(idbtrans?: IDBTransaction) {\n    if (!this.mode) return this;\n    const idbdb = this.db.idbdb;\n    const dbOpenError = this.db._state.dbOpenError;\n    assert(!this.idbtrans);\n    if (!idbtrans && !idbdb) {\n      switch (dbOpenError && dbOpenError.name) {\n        case \"DatabaseClosedError\":\n          // Errors where it is no difference whether it was caused by the user operation or an earlier call to db.open()\n          throw new exceptions.DatabaseClosed(dbOpenError);\n        case \"MissingAPIError\":\n          // Errors where it is no difference whether it was caused by the user operation or an earlier call to db.open()\n          throw new exceptions.MissingAPI(dbOpenError.message, dbOpenError);\n        default:\n          // Make it clear that the user operation was not what caused the error - the error had occurred earlier on db.open()!\n          throw new exceptions.OpenFailed(dbOpenError);\n      }\n    }\n    if (!this.active) throw new exceptions.TransactionInactive();\n    assert(this._completion._state === null); // Completion Promise must still be pending.\n\n    idbtrans = this.idbtrans = idbtrans ||\n      (this.db.core \n        ? this.db.core.transaction(this.storeNames, this.mode as 'readwrite' | 'readonly', { durability: this.chromeTransactionDurability })\n        : idbdb.transaction(this.storeNames, this.mode, { durability: this.chromeTransactionDurability })\n      ) as IDBTransaction;\n\n    idbtrans.onerror = wrap(ev => {\n      preventDefault(ev);// Prohibit default bubbling to window.error\n      this._reject(idbtrans.error);\n    });\n    idbtrans.onabort = wrap(ev => {\n      preventDefault(ev);\n      this.active && this._reject(new exceptions.Abort(idbtrans.error));\n      this.active = false;\n      this.on(\"abort\").fire(ev);\n    });\n    idbtrans.oncomplete = wrap(() => {\n      this.active = false;\n      this._resolve();\n      if ('mutatedParts' in idbtrans) {\n        globalEvents.storagemutated.fire(idbtrans[\"mutatedParts\"]);\n      }\n    });\n    return this;\n  }\n\n  /** Transaction._promise()\n   * \n   * Internal method.\n   */\n  _promise(\n    mode: IDBTransactionMode,\n    fn: (resolve, reject, trans: Transaction) => PromiseLike<any> | void,\n    bWriteLock?: string | boolean): DexiePromise\n  {\n    if (mode === 'readwrite' && this.mode !== 'readwrite')\n      return rejection(new exceptions.ReadOnly(\"Transaction is readonly\"));\n\n    if (!this.active)\n      return rejection(new exceptions.TransactionInactive());\n\n    if (this._locked()) {\n      return new DexiePromise((resolve, reject) => {\n        this._blockedFuncs.push([() => {\n          this._promise(mode, fn, bWriteLock).then(resolve, reject);\n        }, PSD]);\n      });\n\n    } else if (bWriteLock) {\n      return newScope(() => {\n        var p = new DexiePromise((resolve, reject) => {\n          this._lock();\n          const rv = fn(resolve, reject, this);\n          if (rv && rv.then) rv.then(resolve, reject);\n        });\n        p.finally(() => this._unlock());\n        p._lib = true;\n        return p;\n      });\n\n    } else {\n      var p = new DexiePromise((resolve, reject) => {\n        var rv = fn(resolve, reject, this);\n        if (rv && rv.then) rv.then(resolve, reject);\n      });\n      p._lib = true;\n      return p;\n    }\n  }\n\n  /** Transaction._root()\n   * \n   * Internal method. Retrieves the root transaction in the tree of sub transactions.\n   */\n  _root() {\n    return this.parent ? this.parent._root() : this;\n  }\n\n  /** Transaction.waitFor()\n   * \n   * Internal method. Can be accessed from the public API through\n   * Dexie.waitFor(): https://dexie.org/docs/Dexie/Dexie.waitFor()\n   * \n   **/\n  waitFor(promiseLike: PromiseLike<any>) {\n    // Always operate on the root transaction (in case this is a sub stransaction)\n    var root = this._root();\n    // For stability reasons, convert parameter to promise no matter what type is passed to waitFor().\n    // (We must be able to call .then() on it.)\n    const promise = DexiePromise.resolve(promiseLike);\n    if (root._waitingFor) {\n      // Already called waitFor(). Wait for both to complete.\n      root._waitingFor = root._waitingFor.then(() => promise);\n    } else {\n      // We're not in waiting state. Start waiting state.\n      root._waitingFor = promise;\n      root._waitingQueue = [];\n      // Start interacting with indexedDB until promise completes:\n      var store = root.idbtrans.objectStore(root.storeNames[0]);\n      (function spin() {\n        ++root._spinCount; // For debugging only\n        while (root._waitingQueue.length) (root._waitingQueue.shift())();\n        if (root._waitingFor) store.get(-Infinity).onsuccess = spin;\n      }());\n    }\n    var currentWaitPromise = root._waitingFor;\n    return new DexiePromise((resolve, reject) => {\n      promise.then(\n        res => root._waitingQueue.push(wrap(resolve.bind(null, res))),\n        err => root._waitingQueue.push(wrap(reject.bind(null, err)))\n      ).finally(() => {\n        if (root._waitingFor === currentWaitPromise) {\n          // No one added a wait after us. Safe to stop the spinning.\n          root._waitingFor = null;\n        }\n      });\n    });\n  }  \n\n  /** Transaction.abort()\n   * \n   * https://dexie.org/docs/Transaction/Transaction.abort()\n   */\n  abort() {\n    if (this.active) {\n      this.active = false;\n      if (this.idbtrans) this.idbtrans.abort();\n      this._reject(new exceptions.Abort());\n    }\n  }\n\n  /** Transaction.table()\n   * \n   * https://dexie.org/docs/Transaction/Transaction.table()\n   */\n  table(tableName: string) {\n    const memoizedTables = (this._memoizedTables || (this._memoizedTables = {}));\n    if (hasOwn(memoizedTables, tableName))\n      return memoizedTables[tableName];\n    const tableSchema = this.schema[tableName];\n    if (!tableSchema) {\n      throw new exceptions.NotFound(\"Table \" + tableName + \" not part of transaction\");        \n    }\n\n    const transactionBoundTable = new this.db.Table(tableName, tableSchema, this);\n    transactionBoundTable.core = this.db.core.table(tableName);\n    memoizedTables[tableName] = transactionBoundTable;\n    return transactionBoundTable;\n  }\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Transaction } from './transaction';\nimport { DbSchema } from '../../public/types/db-schema';\nimport Events from '../../helpers/Events';\nimport Promise, { rejection } from '../../helpers/promise';\n\nexport interface TransactionConstructor<T extends Transaction=Transaction> {\n  new (\n    mode: IDBTransactionMode,\n    storeNames: string[],\n    dbschema: DbSchema,\n    chromeTransactionDurability: ChromeTransactionDurability,\n    parent?: Transaction) : T;\n  prototype: T;\n}\n\n/** Generates a Transaction constructor bound to given Dexie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createTransactionConstructor(db: Dexie) {\n  return makeClassConstructor<TransactionConstructor<Transaction>>(\n    Transaction.prototype,\n    function Transaction (\n      this: Transaction,\n      mode: IDBTransactionMode,\n      storeNames: string[],\n      dbschema: DbSchema,\n      chromeTransactionDurability: ChromeTransactionDurability,\n      parent?: Transaction)\n    {\n      this.db = db;\n      this.mode = mode;\n      this.storeNames = storeNames;\n      this.schema = dbschema;\n      this.chromeTransactionDurability = chromeTransactionDurability;\n      this.idbtrans = null;\n      this.on = Events(this, \"complete\", \"error\", \"abort\");\n      this.parent = parent || null;\n      this.active = true;\n      this._reculock = 0;\n      this._blockedFuncs = [];\n      this._resolve = null;\n      this._reject = null;\n      this._waitingFor = null;\n      this._waitingQueue = null;\n      this._spinCount = 0; // Just for debugging waitFor()\n      this._completion = new Promise ((resolve, reject) => {\n          this._resolve = resolve;\n          this._reject = reject;\n      });\n      \n      this._completion.then(\n          ()=> {\n              this.active = false;\n              this.on.complete.fire();\n          },\n          e => {\n              var wasActive = this.active;\n              this.active = false;\n              this.on.error.fire(e);\n              this.parent ?\n                  this.parent._reject(e) :\n                  wasActive && this.idbtrans && this.idbtrans.abort();\n              return rejection(e); // Indicate we actually DO NOT catch this error.\n          });\n    \n    });\n}\n", "import { IndexSpec } from '../public/types/index-spec';\n\nexport function createIndexSpec(\n  name: string,\n  keyPath: string | string[],\n  unique: boolean,\n  multi: boolean,\n  auto: boolean,\n  compound: boolean,\n  isPrimKey: boolean\n): IndexSpec {\n  return {\n    name,\n    keyPath,\n    unique,\n    multi,\n    auto,\n    compound,\n    src: (unique && !isPrimKey ? '&' : '') + (multi ? '*' : '') + (auto ? \"++\" : \"\") + nameFromKeyPath(keyPath)\n  }\n}\n\nexport function nameFromKeyPath (keyPath?: string | string[]): string {\n  return typeof keyPath === 'string' ?\n    keyPath :\n    keyPath ? ('[' + [].join.call(keyPath, '+') + ']') : \"\";\n}\n", "import { IndexSpec } from '../public/types/index-spec';\nimport { TableSchema } from '../public/types/table-schema';\nimport { createIndexSpec } from './index-spec';\nimport { arrayToObject } from '../functions/utils';\n\nexport function createTableSchema (\n  name: string,\n  primKey: IndexSpec,\n  indexes: IndexSpec[]\n): TableSchema {\n  return {\n    name,\n    primKey,\n    indexes,\n    mappedClass: null,\n    idxByName: arrayToObject(indexes, index => [index.name, index])\n  };\n}\n", "import { maxString } from '../globals/constants';\n\nexport function safariMultiStoreFix(storeNames: string[]) {\n  return storeNames.length === 1 ? storeNames[0] : storeNames;\n}\n\nexport function getNativeGetDatabaseNamesFn(indexedDB) {\n  var fn = indexedDB && (indexedDB.getDatabaseNames || indexedDB.webkitGetDatabaseNames);\n  return fn && fn.bind(indexedDB);\n}\n\nexport let getMaxKey = (IdbKeyRange: typeof IDBKeyRange) => {\n  try {\n    IdbKeyRange.only([[]]);\n    getMaxKey = () => [[]];\n    return [[]];\n  } catch (e) {\n    getMaxKey = () => maxString;\n    return maxString;\n  }\n}\n", "import { getByKeyPath } from '../functions/utils';\n\nexport function getKeyExtractor (keyPath: null | string | string[]) : (a: any) => any {\n  if (keyPath == null) {\n    return () => undefined;\n  } else if (typeof keyPath === 'string') {\n    return getSinglePathKeyExtractor(keyPath);\n  } else {\n    return obj => getByKeyPath(obj, keyPath);\n  }\n}\n\nexport function getSinglePathKeyExtractor(keyPath: string) {\n  const split = keyPath.split('.');\n  if (split.length === 1) {\n    return obj => obj[keyPath];\n  } else {\n    return obj => getByKeyPath(obj, keyPath);\n  }\n}\n", "import {\n  DBCore,\n  DBCoreCursor,\n  DBCoreOpenCursorRequest,\n  DBCoreQueryRequest,\n  DBCoreIndex,\n  DBCoreKeyRange,\n  DBCoreQueryResponse,\n  DBCoreRangeType,\n  DBCoreSchema,\n  DBCoreTableSchema,\n  DBCoreTable,\n  DBCoreMutateResponse,\n} from \"../public/types/dbcore\";\nimport { isArray } from '../functions/utils';\nimport { eventRejectHandler, preventDefault } from '../functions/event-wrappers';\nimport { wrap } from '../helpers/promise';\nimport { getMaxKey } from '../functions/quirks';\nimport { getKeyExtractor } from './get-key-extractor';\n\nexport function arrayify<T>(arrayLike: {length: number, [index: number]: T}): T[] {\n  return [].slice.call(arrayLike);\n}\nexport function pick<T,Prop extends keyof T>(obj: T, props: Prop[]): Pick<T, Prop> {\n  const result = {} as Pick<T, Prop>;\n  props.forEach(prop => result[prop] = obj[prop]);\n  return result;\n}\n\nlet _id_counter = 0;\n\nexport function getKeyPathAlias(keyPath: null | string | string[]) {\n  return keyPath == null ?\n    \":id\" :\n    typeof keyPath === 'string' ?\n      keyPath :\n      `[${keyPath.join('+')}]`;\n}\n\nexport function createDBCore (\n  db: IDBDatabase,\n  IdbKeyRange: typeof IDBKeyRange,\n  tmpTrans: IDBTransaction) : DBCore\n{\n  function extractSchema(db: IDBDatabase, trans: IDBTransaction) : {schema: DBCoreSchema, hasGetAll: boolean} {\n    const tables = arrayify(db.objectStoreNames);\n    return {\n      schema: {\n        name: db.name,\n        tables: tables.map(table => trans.objectStore(table)).map(store => {\n          const {keyPath, autoIncrement} = store;\n          const compound = isArray(keyPath);\n          const outbound = keyPath == null;\n          const indexByKeyPath: {[keyPathAlias: string]: DBCoreIndex} = {};\n          const result = {\n            name: store.name,\n            primaryKey: {\n              name: null,\n              isPrimaryKey: true,\n              outbound,\n              compound,\n              keyPath,\n              autoIncrement,\n              unique: true,\n              extractKey: getKeyExtractor(keyPath)\n            } as DBCoreIndex,\n            indexes: arrayify(store.indexNames).map(indexName => store.index(indexName))\n              .map(index => {\n                const {name, unique, multiEntry, keyPath} = index;\n                const compound = isArray(keyPath);\n                const result: DBCoreIndex = {\n                  name,\n                  compound,\n                  keyPath,\n                  unique,\n                  multiEntry,\n                  extractKey: getKeyExtractor(keyPath)\n                };\n                indexByKeyPath[getKeyPathAlias(keyPath)] = result;\n                return result;\n              }),\n            getIndexByKeyPath: (keyPath: null | string | string[]) => indexByKeyPath[getKeyPathAlias(keyPath)]\n          };\n          indexByKeyPath[\":id\"] = result.primaryKey;\n          if (keyPath != null) {\n            indexByKeyPath[getKeyPathAlias(keyPath)] = result.primaryKey;\n          }\n          return result;\n        })\n      },\n      hasGetAll: tables.length > 0 && ('getAll' in trans.objectStore(tables[0])) &&\n        !(typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n        !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n        [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604) // Bug with getAll() on Safari ver<604. See discussion following PR #579\n    };\n  }\n\n  function makeIDBKeyRange (range: DBCoreKeyRange) : IDBKeyRange | null {\n    if (range.type === DBCoreRangeType.Any) return null;\n    if (range.type === DBCoreRangeType.Never) throw new Error(\"Cannot convert never type to IDBKeyRange\");\n    const {lower, upper, lowerOpen, upperOpen} = range;\n    const idbRange = lower === undefined ?\n      upper === undefined ?\n        null : //IDBKeyRange.lowerBound(-Infinity, false) : // Any range (TODO: Should we return null instead?)\n        IdbKeyRange.upperBound(upper, !!upperOpen) : // below\n      upper === undefined ?\n        IdbKeyRange.lowerBound(lower, !!lowerOpen) : // above\n        IdbKeyRange.bound(lower, upper, !!lowerOpen, !!upperOpen);\n    return idbRange;\n  }\n\n  function createDbCoreTable(tableSchema: DBCoreTableSchema): DBCoreTable {\n    const tableName = tableSchema.name;\n\n    function mutate ({trans, type, keys, values, range}) {\n      return new Promise<DBCoreMutateResponse>((resolve, reject) => {\n        resolve = wrap(resolve);\n        const store = (trans as IDBTransaction).objectStore(tableName);\n        const outbound = store.keyPath == null;\n        const isAddOrPut = type === \"put\" || type === \"add\";\n        if (!isAddOrPut && type !== 'delete' && type !== 'deleteRange')\n          throw new Error (\"Invalid operation type: \" + type);\n\n        const {length} = keys || values || {length: 1}; // keys.length if keys. values.length if values. 1 if range.\n        if (keys && values && keys.length !== values.length) {\n          throw new Error(\"Given keys array must have same length as given values array.\");\n        }\n        if (length === 0)\n          // No items to write. Don't even bother!\n          return resolve({numFailures: 0, failures: {}, results: [], lastResult: undefined});\n\n        let req: IDBRequest;\n        const reqs: IDBRequest[] = [];\n          \n        const failures: {[operationNumber: number]: Error} = [];\n        let numFailures = 0;\n        const errorHandler = \n          event => {\n            ++numFailures;\n            preventDefault(event);\n          };\n  \n        if (type === 'deleteRange') {\n          // Here the argument is the range\n          if (range.type === DBCoreRangeType.Never)\n            return resolve({numFailures, failures, results: [], lastResult: undefined}); // Deleting the Never range shoulnt do anything.\n          if (range.type === DBCoreRangeType.Any)\n            reqs.push(req = store.clear()); // Deleting the Any range is equivalent to store.clear()\n          else\n            reqs.push(req = store.delete(makeIDBKeyRange(range)));\n        } else {\n          // No matter add, put or delete - find out arrays of first and second arguments to it.\n          const [args1, args2] = isAddOrPut ?\n            outbound ?\n              [values, keys] :\n              [values, null] :\n            [keys, null];\n\n          if (isAddOrPut) {\n            for (let i=0; i<length; ++i) {\n              reqs.push(req = (args2 && args2[i] !== undefined ?\n                store[type](args1[i], args2[i]) :\n                store[type](args1[i])) as IDBRequest);\n              req.onerror = errorHandler;\n            }\n          } else {\n            for (let i=0; i<length; ++i) {\n              reqs.push(req = store[type](args1[i]) as IDBRequest);\n              req.onerror = errorHandler;\n            }\n          }\n        }\n        const done = event => {\n          const lastResult = event.target.result;\n          reqs.forEach((req, i) => req.error != null && (failures[i] = req.error));\n          resolve({\n            numFailures,\n            failures,\n            results: type === \"delete\" ? keys : reqs.map(req => req.result),\n            lastResult\n          });\n        };\n  \n        req.onerror = event => { // wrap() not needed. All paths calling outside will wrap!\n          errorHandler(event);\n          done(event);\n        };\n  \n        req.onsuccess = done;\n      });\n    }\n    \n    function openCursor ({trans, values, query, reverse, unique}: DBCoreOpenCursorRequest): Promise<DBCoreCursor>\n    {\n      return new Promise((resolve, reject) => {\n        resolve = wrap(resolve);\n        const {index, range} = query;\n        const store = (trans as IDBTransaction).objectStore(tableName);\n        // source\n        const source = index.isPrimaryKey ?\n          store :\n          store.index(index.name);\n        // direction\n        const direction = reverse ?\n          unique ?\n            \"prevunique\" :\n            \"prev\" :\n          unique ?\n            \"nextunique\" :\n            \"next\";\n        // request\n        const req = values || !('openKeyCursor' in source) ?\n          source.openCursor(makeIDBKeyRange(range), direction) :\n          source.openKeyCursor(makeIDBKeyRange(range), direction);\n          \n        // iteration\n        req.onerror = eventRejectHandler(reject);\n        req.onsuccess = wrap(ev => {\n\n          const cursor = req.result as unknown as DBCoreCursor;\n          if (!cursor) {\n            resolve(null);\n            return;\n          }\n          (cursor as any).___id = ++_id_counter;\n          (cursor as any).done = false;\n          const _cursorContinue = cursor.continue.bind(cursor);\n          let _cursorContinuePrimaryKey = cursor.continuePrimaryKey;\n          if (_cursorContinuePrimaryKey) _cursorContinuePrimaryKey = _cursorContinuePrimaryKey.bind(cursor);\n          const _cursorAdvance = cursor.advance.bind(cursor);\n          const doThrowCursorIsNotStarted = ()=>{throw new Error(\"Cursor not started\");}\n          const doThrowCursorIsStopped = ()=>{throw new Error(\"Cursor not stopped\");}\n          (cursor as any).trans = trans;\n          cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsNotStarted;\n          cursor.fail = wrap(reject);\n          cursor.next = function (this: DBCoreCursor) {\n            // next() must work with \"this\" pointer in order to function correctly for ProxyCursors (derived objects)\n            // without having to re-define next() on each child.\n            let gotOne = 1;\n            return this.start(() => gotOne-- ? this.continue() : this.stop()).then(() => this);\n          };\n          cursor.start = (callback) => {\n            //console.log(\"Starting cursor\", (cursor as any).___id);\n            const iterationPromise = new Promise<void>((resolveIteration, rejectIteration) =>{\n              resolveIteration = wrap(resolveIteration);\n              req.onerror = eventRejectHandler(rejectIteration);\n              cursor.fail = rejectIteration;\n              cursor.stop = value => {\n                //console.log(\"Cursor stop\", cursor);\n                cursor.stop = cursor.continue = cursor.continuePrimaryKey = cursor.advance = doThrowCursorIsStopped;\n                resolveIteration(value);\n              };\n            });\n            // Now change req.onsuccess to a callback that doesn't call initCursor but just observer.next()\n            const guardedCallback = () => {\n              if (req.result) {\n                //console.log(\"Next result\", cursor);\n                try {\n                  callback();\n                } catch (err) {\n                  cursor.fail(err);\n                }\n              } else {\n                (cursor as any).done = true;\n                cursor.start = ()=>{throw new Error(\"Cursor behind last entry\");}\n                cursor.stop();\n              }\n            }\n            req.onsuccess = wrap(ev => {\n              //cursor.continue = _cursorContinue;\n              //cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n              //cursor.advance = _cursorAdvance;\n              req.onsuccess = guardedCallback;\n              guardedCallback();\n            });\n            cursor.continue = _cursorContinue;\n            cursor.continuePrimaryKey = _cursorContinuePrimaryKey;\n            cursor.advance = _cursorAdvance;\n            guardedCallback();\n            return iterationPromise;\n          };\n          resolve(cursor);\n        }, reject); \n      });\n    }\n  \n    function query (hasGetAll: boolean) {\n      return (request: DBCoreQueryRequest) => {\n        return new Promise<DBCoreQueryResponse>((resolve, reject) => {\n          resolve = wrap(resolve);\n          const {trans, values, limit, query} = request;\n          const nonInfinitLimit = limit === Infinity ? undefined : limit;\n          const {index, range} = query;\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const source = index.isPrimaryKey ? store : store.index(index.name);\n          const idbKeyRange = makeIDBKeyRange(range);\n          if (limit === 0) return resolve({result: []});\n          if (hasGetAll) {\n            const req = values ?\n                (source as any).getAll(idbKeyRange, nonInfinitLimit) :\n                (source as any).getAllKeys(idbKeyRange, nonInfinitLimit);\n            req.onsuccess = event => resolve({result: event.target.result});\n            req.onerror = eventRejectHandler(reject);\n          } else {\n            let count = 0;\n            const req = values || !('openKeyCursor' in source) ?\n              source.openCursor(idbKeyRange) :\n              source.openKeyCursor(idbKeyRange)\n            const result = [];\n            req.onsuccess = event => {\n              const cursor = req.result as IDBCursorWithValue;\n              if (!cursor) return resolve({result});\n              result.push(values ? cursor.value : cursor.primaryKey);\n              if (++count === limit) return resolve({result});\n              cursor.continue();\n            };\n            req.onerror = eventRejectHandler(reject);\n          }\n        });\n      };\n    }\n  \n    return {\n      name: tableName,\n      schema: tableSchema,\n      \n      mutate,\n\n      getMany ({trans, keys}) {\n        return new Promise<any[]>((resolve, reject) => {\n          resolve = wrap(resolve);\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const length = keys.length;\n          const result = new Array(length);\n          let keyCount = 0;\n          let callbackCount = 0;\n          let valueCount = 0;\n          let req: IDBRequest & {_pos?: number};\n    \n          const successHandler = event => {\n            const req = event.target;\n            if ((result[req._pos] = req.result) != null) ++valueCount;\n            if (++callbackCount === keyCount) resolve(result);\n          };\n          const errorHandler = eventRejectHandler(reject);\n    \n          for (let i=0; i<length; ++i) {\n            const key = keys[i];\n            if (key != null) {\n              req = store.get(keys[i]);\n              req._pos = i;\n              req.onsuccess = successHandler;\n              req.onerror = errorHandler;\n              ++keyCount;\n            }\n          }\n          if (keyCount === 0) resolve(result);\n        });\n      },\n\n      get ({trans, key}) {\n        return new Promise<any>((resolve, reject) => {\n          resolve = wrap (resolve);\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const req = store.get(key);\n          req.onsuccess = event => resolve((event.target as any).result);\n          req.onerror = eventRejectHandler(reject);\n        });\n      },\n\n      query: query(hasGetAll),\n      \n      openCursor,\n\n      count ({query, trans}) {\n        const {index, range} = query;\n        return new Promise<number>((resolve, reject) => {\n          const store = (trans as IDBTransaction).objectStore(tableName);\n          const source = index.isPrimaryKey ? store : store.index(index.name);\n          const idbKeyRange = makeIDBKeyRange(range);\n          const req = idbKeyRange ? source.count(idbKeyRange) : source.count();\n          req.onsuccess = wrap(ev => resolve((ev.target as IDBRequest).result));\n          req.onerror = eventRejectHandler(reject);\n        });\n      }\n    };\n  }\n\n  const {schema, hasGetAll} = extractSchema(db, tmpTrans);\n  const tables = schema.tables.map(tableSchema => createDbCoreTable(tableSchema));\n  const tableMap: {[name: string]: DBCoreTable} = {};\n  tables.forEach(table => tableMap[table.name] = table);\n  return {\n    stack: \"dbcore\",\n    \n    transaction: db.transaction.bind(db),\n\n    table(name: string) {\n      const result = tableMap[name];\n      if (!result) throw new Error(`Table '${name}' not found`);\n      return tableMap[name];\n    },\n\n    MIN_KEY: -Infinity,\n\n    MAX_KEY: getMaxKey(IdbKeyRange),\n\n    schema\n\n  };\n}\n", "import { <PERSON><PERSON> } from './';\nimport { createDBCore } from '../../dbcore/dbcore-indexeddb';\nimport { DBCore } from '../../public/types/dbcore';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\nimport { DexieStacks, Middleware } from '../../public/types/middleware';\nimport { exceptions } from '../../errors';\n\nfunction createMiddlewareStack<TStack extends {stack: string}>(\n  stackImpl: {stack: string},\n  middlewares: Middleware<{stack: string}>[]): TStack {\n  return middlewares.reduce((down, {create}) => ({...down, ...create(down)}), stackImpl) as TStack;\n} \n\nfunction createMiddlewareStacks(\n  middlewares: {[StackName in keyof DexieStacks]?: Middleware<DexieStacks[StackName]>[]},\n  idbdb: IDBDatabase,\n  {IDBKeyRange, indexedDB}: DexieDOMDependencies,\n  tmpTrans: IDBTransaction): {[StackName in keyof DexieStacks]?: DexieStacks[StackName]}\n{\n  const dbcore = createMiddlewareStack<DBCore>(\n    createDBCore(idbdb, IDBKeyRange, tmpTrans),\n    middlewares.dbcore);\n  \n  // TODO: Create other stacks the same way as above. They might be dependant on the result\n  // of creating dbcore stack.\n\n  return {\n    dbcore\n  };\n}\n\nexport function generateMiddlewareStacks({_novip: db}: Dexie, tmpTrans: IDBTransaction) {\n  const idbdb = tmpTrans.db;\n  const stacks = createMiddlewareStacks(db._middlewares, idbdb, db._deps, tmpTrans);\n  db.core = stacks.dbcore!;\n  db.tables.forEach(table => {\n    const tableName = table.name;\n    if (db.core.schema.tables.some(tbl => tbl.name === tableName)) {\n      table.core = db.core.table(tableName);\n      if (db[tableName] instanceof db.Table) {\n          db[tableName].core = table.core;\n      }\n    }\n  });\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { DbSchema } from '../../public/types/db-schema';\nimport { _global } from \"../../globals/global\";\nimport { setProp, keys, slice, isArray, shallowClone, isAsyncFunction, defineProperty, getPropertyDescriptor } from '../../functions/utils';\nimport { Transaction } from '../transaction';\nimport { Version } from './version';\nimport Promise, { PSD, newScope, NativePromise, decrementExpectedAwaits, incrementExpectedAwaits } from '../../helpers/promise';\nimport { exceptions } from '../../errors';\nimport { TableSchema } from '../../public/types/table-schema';\nimport { IndexSpec } from '../../public/types/index-spec';\nimport { hasIEDeleteObjectStoreBug, isIEOrEdge } from '../../globals/constants';\nimport { safariMultiStoreFix } from '../../functions/quirks';\nimport { createIndexSpec, nameFromKeyPath } from '../../helpers/index-spec';\nimport { createTableSchema } from '../../helpers/table-schema';\nimport { generateMiddlewareStacks } from '../dexie/generate-middleware-stacks';\n\nexport function setApiOnPlace({_novip: db}: Dexie, objs: Object[], tableNames: string[], dbschema: DbSchema) {\n  tableNames.forEach(tableName => {\n    const schema = dbschema[tableName];\n    objs.forEach(obj => {\n      const propDesc = getPropertyDescriptor(obj, tableName);\n      if (!propDesc || (\"value\" in propDesc && propDesc.value === undefined)) {\n        // Either the prop is not declared, or it is initialized to undefined.\n        if (obj === db.Transaction.prototype || obj instanceof db.Transaction) {\n          // obj is a Transaction prototype (or prototype of a subclass to Transaction)\n          // Make the API a getter that returns this.table(tableName)\n          setProp(obj, tableName, {\n            get(this: Transaction) { return this.table(tableName); },\n            set(value: any) {\n              // Issue #1039\n              // Let \"this.schema = dbschema;\" and other props in transaction constructor work even if there's a name collision with the table name.\n              defineProperty(this, tableName, {value, writable: true, configurable: true, enumerable: true});\n            }\n          });\n        } else {\n          // Table will not be bound to a transaction (will use Dexie.currentTransaction)\n          obj[tableName] = new db.Table(tableName, schema);\n        }\n      }\n    });\n  });\n}\n\nexport function removeTablesApi({_novip: db}: Dexie, objs: Object[]) {\n  objs.forEach(obj => {\n    for (let key in obj) {\n      if (obj[key] instanceof db.Table) delete obj[key];\n    }\n  });\n}\n\nexport function lowerVersionFirst(a: Version, b: Version) {\n  return a._cfg.version - b._cfg.version;\n}\n\nexport function runUpgraders(db: Dexie, oldVersion: number, idbUpgradeTrans: IDBTransaction, reject) {\n  const globalSchema = db._dbSchema;\n  const trans = db._createTransaction('readwrite', db._storeNames, globalSchema);\n  trans.create(idbUpgradeTrans);\n  trans._completion.catch(reject);\n  const rejectTransaction = trans._reject.bind(trans);\n  const transless = PSD.transless || PSD;\n  newScope(() => {\n    PSD.trans = trans;\n    PSD.transless = transless;\n    if (oldVersion === 0) {\n      // Create tables:\n      keys(globalSchema).forEach(tableName => {\n        createTable(idbUpgradeTrans, tableName, globalSchema[tableName].primKey, globalSchema[tableName].indexes);\n      });\n      generateMiddlewareStacks(db, idbUpgradeTrans);\n      Promise.follow(() => db.on.populate.fire(trans)).catch(rejectTransaction);\n    } else\n      updateTablesAndIndexes(db, oldVersion, trans, idbUpgradeTrans).catch(rejectTransaction);\n  });\n}\n\nexport type UpgradeQueueItem = (idbtrans: IDBTransaction) => PromiseLike<any> | void;\n\nexport function updateTablesAndIndexes(\n  {_novip: db}: Dexie,\n  oldVersion: number,\n  trans: Transaction,\n  idbUpgradeTrans: IDBTransaction)\n{\n  // Upgrade version to version, step-by-step from oldest to newest version.\n  // Each transaction object will contain the table set that was current in that version (but also not-yet-deleted tables from its previous version)\n  const queue: UpgradeQueueItem[] = [];\n  const versions = db._versions;\n  let globalSchema = db._dbSchema = buildGlobalSchema(db, db.idbdb, idbUpgradeTrans);\n  let anyContentUpgraderHasRun = false;\n\n  const versToRun = versions.filter(v => v._cfg.version >= oldVersion);\n  versToRun.forEach(version => {\n    queue.push(() => {\n      const oldSchema = globalSchema;\n      const newSchema = version._cfg.dbschema;\n      adjustToExistingIndexNames(db, oldSchema, idbUpgradeTrans);\n      adjustToExistingIndexNames(db, newSchema, idbUpgradeTrans);\n\n      globalSchema = db._dbSchema = newSchema;\n\n      const diff = getSchemaDiff(oldSchema, newSchema);\n      // Add tables           \n      diff.add.forEach(tuple => {\n        createTable(idbUpgradeTrans, tuple[0], tuple[1].primKey, tuple[1].indexes);\n      });\n      // Change tables\n      diff.change.forEach(change => {\n        if (change.recreate) {\n          throw new exceptions.Upgrade(\"Not yet support for changing primary key\");\n        } else {\n          const store = idbUpgradeTrans.objectStore(change.name);\n          // Add indexes\n          change.add.forEach(idx => addIndex(store, idx));\n          // Update indexes\n          change.change.forEach(idx => {\n            store.deleteIndex(idx.name);\n            addIndex(store, idx);\n          });\n          // Delete indexes\n          change.del.forEach(idxName => store.deleteIndex(idxName));\n        }\n      });\n\n      const contentUpgrade = version._cfg.contentUpgrade;\n\n      if (contentUpgrade && version._cfg.version > oldVersion) {\n        // Update db.core with new tables and indexes:\n        generateMiddlewareStacks(db, idbUpgradeTrans);\n        trans._memoizedTables = {}; // Invalidate memoization as transaction shape may change between versions.\n\n        anyContentUpgraderHasRun = true;\n\n        // Add to-be-deleted tables to contentUpgrade transaction\n        let upgradeSchema = shallowClone(newSchema);\n        diff.del.forEach(table => {\n          upgradeSchema[table] = oldSchema[table];\n        });\n\n        // Safe to affect Transaction.prototype globally in this moment,\n        // because when this code runs, there may not be any other code\n        // that can access any transaction instance, else than this particular\n        // upgrader function.\n        removeTablesApi(db, [db.Transaction.prototype]);\n        setApiOnPlace(db, [db.Transaction.prototype], keys(upgradeSchema), upgradeSchema);\n        trans.schema = upgradeSchema;\n\n        // Support for native async await.\n        const contentUpgradeIsAsync = isAsyncFunction(contentUpgrade);\n        if (contentUpgradeIsAsync) {\n          incrementExpectedAwaits();\n        }\n        \n        let returnValue: any;\n        const promiseFollowed = Promise.follow(() => {\n          // Finally, call the scope function with our table and transaction arguments.\n          returnValue = contentUpgrade(trans);\n          if (returnValue) {\n            if (contentUpgradeIsAsync) {\n              // contentUpgrade is a native async function - we know for sure returnValue is native promise.\n              var decrementor = decrementExpectedAwaits.bind(null, null);\n              returnValue.then(decrementor, decrementor);\n            }\n          }\n        });\n        return (returnValue && typeof returnValue.then === 'function' ?\n          Promise.resolve(returnValue) : promiseFollowed.then(()=>returnValue));\n      }\n    });\n    queue.push(idbtrans => {\n      if (!anyContentUpgraderHasRun || !hasIEDeleteObjectStoreBug) { // Dont delete old tables if ieBug is present and a content upgrader has run. Let tables be left in DB so far. This needs to be taken care of.\n        const newSchema = version._cfg.dbschema;\n        // Delete old tables\n        deleteRemovedTables(newSchema, idbtrans);\n      }\n      // Restore the final API\n      removeTablesApi(db, [db.Transaction.prototype]);\n      setApiOnPlace(db, [db.Transaction.prototype], db._storeNames, db._dbSchema);\n      trans.schema = db._dbSchema;\n    });\n  });\n\n  // Now, create a queue execution engine\n  function runQueue() {\n    return queue.length ? Promise.resolve(queue.shift()(trans.idbtrans)).then(runQueue) :\n      Promise.resolve();\n  }\n\n  return runQueue().then(() => {\n    createMissingTables(globalSchema, idbUpgradeTrans); // At last, make sure to create any missing tables. (Needed by addons that add stores to DB without specifying version)\n  });\n}\n\nexport interface SchemaDiff {\n  del: string[],\n  add: [string, TableSchema][];\n  change: TableSchemaDiff[];\n}\n\nexport interface TableSchemaDiff {\n  name: string,\n  recreate: boolean,\n  del: string[],\n  add: IndexSpec[],\n  change: IndexSpec[]\n}\n\nexport function getSchemaDiff(oldSchema: DbSchema, newSchema: DbSchema): SchemaDiff {\n  const diff: SchemaDiff = {\n    del: [], // Array of table names\n    add: [], // Array of [tableName, newDefinition]\n    change: [] // Array of {name: tableName, recreate: newDefinition, del: delIndexNames, add: newIndexDefs, change: changedIndexDefs}\n  };\n  let table: string;\n  for (table in oldSchema) {\n    if (!newSchema[table]) diff.del.push(table);\n  }\n  for (table in newSchema) {\n    const oldDef = oldSchema[table],\n      newDef = newSchema[table];\n    if (!oldDef) {\n      diff.add.push([table, newDef]);\n    } else {\n      const change = {\n        name: table,\n        def: newDef,\n        recreate: false,\n        del: [],\n        add: [],\n        change: []\n      };\n      if (\n          (\n             // compare keyPaths no matter if string or string[]\n             // compare falsy keypaths same no matter if they are null or empty string.\n            ''+(oldDef.primKey.keyPath||'')\n          ) !== (\n            ''+(newDef.primKey.keyPath||'')\n          ) ||\n            // Compare the autoIncrement flag also\n          (oldDef.primKey.auto !== newDef.primKey.auto && !isIEOrEdge)) // IE has bug reading autoIncrement prop.\n      {\n        // Primary key has changed. Remove and re-add table.\n        change.recreate = true;\n        diff.change.push(change);\n      } else {\n        // Same primary key. Just find out what differs:\n        const oldIndexes = oldDef.idxByName;\n        const newIndexes = newDef.idxByName;\n        let idxName: string;\n        for (idxName in oldIndexes) {\n          if (!newIndexes[idxName]) change.del.push(idxName);\n        }\n        for (idxName in newIndexes) {\n          const oldIdx = oldIndexes[idxName],\n            newIdx = newIndexes[idxName];\n          if (!oldIdx) change.add.push(newIdx);\n          else if (oldIdx.src !== newIdx.src) change.change.push(newIdx);\n        }\n        if (change.del.length > 0 || change.add.length > 0 || change.change.length > 0) {\n          diff.change.push(change);\n        }\n      }\n    }\n  }\n  return diff;\n}\n\nexport function createTable(\n  idbtrans: IDBTransaction,\n  tableName: string,\n  primKey: IndexSpec,\n  indexes: IndexSpec[]\n) {\n  const store = idbtrans.db.createObjectStore(\n    tableName,\n    primKey.keyPath ?\n      { keyPath: primKey.keyPath, autoIncrement: primKey.auto } :\n      { autoIncrement: primKey.auto }\n  );\n  indexes.forEach(idx => addIndex(store, idx));\n  return store;\n}\n\nexport function createMissingTables(newSchema: DbSchema, idbtrans: IDBTransaction) {\n  keys(newSchema).forEach(tableName => {\n    if (!idbtrans.db.objectStoreNames.contains(tableName)) {\n      createTable(idbtrans, tableName, newSchema[tableName].primKey, newSchema[tableName].indexes);\n    }\n  });\n}\n\nexport function deleteRemovedTables(newSchema: DbSchema, idbtrans: IDBTransaction) {\n  [].slice.call(idbtrans.db.objectStoreNames).forEach(storeName =>\n    newSchema[storeName] == null && idbtrans.db.deleteObjectStore(storeName));\n}\n\nexport function addIndex(store: IDBObjectStore, idx: IndexSpec) {\n  store.createIndex(idx.name, idx.keyPath, { unique: idx.unique, multiEntry: idx.multi });\n}\n\nfunction buildGlobalSchema(\n  db: Dexie,\n  idbdb: IDBDatabase,\n  tmpTrans: IDBTransaction\n) {\n  const globalSchema = {};\n  const dbStoreNames = slice(idbdb.objectStoreNames, 0);\n  dbStoreNames.forEach(storeName => {\n    const store = tmpTrans.objectStore(storeName);\n    let keyPath = store.keyPath;\n    const primKey = createIndexSpec(\n      nameFromKeyPath(keyPath),\n      keyPath || \"\",\n      false,\n      false,\n      !!store.autoIncrement,\n      keyPath && typeof keyPath !== \"string\",\n      true\n    );\n    const indexes: IndexSpec[] = [];\n    for (let j = 0; j < store.indexNames.length; ++j) {\n      const idbindex = store.index(store.indexNames[j]);\n      keyPath = idbindex.keyPath;\n      var index = createIndexSpec(\n        idbindex.name,\n        keyPath,\n        !!idbindex.unique,\n        !!idbindex.multiEntry,\n        false,\n        keyPath && typeof keyPath !== \"string\",\n        false\n      );\n      indexes.push(index);\n    }\n    globalSchema[storeName] = createTableSchema(storeName, primKey, indexes);\n  });\n  return globalSchema;\n}\n\nexport function readGlobalSchema({_novip: db}: Dexie, idbdb: IDBDatabase, tmpTrans: IDBTransaction) {\n  db.verno = idbdb.version / 10;\n  const globalSchema = db._dbSchema = buildGlobalSchema(db, idbdb, tmpTrans);\n  db._storeNames = slice(idbdb.objectStoreNames, 0);\n  setApiOnPlace(db, [db._allTables], keys(globalSchema), globalSchema);\n}\n\nexport function verifyInstalledSchema(db: Dexie, tmpTrans: IDBTransaction): boolean {\n  const installedSchema = buildGlobalSchema(db, db.idbdb, tmpTrans);\n  const diff = getSchemaDiff(installedSchema, db._dbSchema);\n  return !(diff.add.length || diff.change.some(ch => ch.add.length || ch.change.length));\n}\n\nexport function adjustToExistingIndexNames({_novip: db}: Dexie, schema: DbSchema, idbtrans: IDBTransaction) {\n  // Issue #30 Problem with existing db - adjust to existing index names when migrating from non-dexie db\n  const storeNames = idbtrans.db.objectStoreNames;\n\n  for (let i = 0; i < storeNames.length; ++i) {\n    const storeName = storeNames[i];\n    const store = idbtrans.objectStore(storeName);\n    db._hasGetAll = 'getAll' in store;\n\n    for (let j = 0; j < store.indexNames.length; ++j) {\n      const indexName = store.indexNames[j];\n      const keyPath = store.index(indexName).keyPath;\n      const dexieName = typeof keyPath === 'string' ? keyPath : \"[\" + slice(keyPath).join('+') + \"]\";\n      if (schema[storeName]) {\n        const indexSpec = schema[storeName].idxByName[dexieName];\n        if (indexSpec) {\n          indexSpec.name = indexName;\n          delete schema[storeName].idxByName[dexieName];\n          schema[storeName].idxByName[indexName] = indexSpec;\n        }\n      }\n    }\n  }\n\n  // Bug with getAll() on Safari ver<604 on Workers only, see discussion following PR #579\n  if (typeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) &&\n    !/(Chrome\\/|Edge\\/)/.test(navigator.userAgent) &&\n    _global.WorkerGlobalScope && _global instanceof _global.WorkerGlobalScope &&\n    [].concat(navigator.userAgent.match(/Safari\\/(\\d*)/))[1] < 604)\n  {\n    db._hasGetAll = false;\n  }\n}\n\nexport function parseIndexSyntax(primKeyAndIndexes: string): IndexSpec[] {\n  return primKeyAndIndexes.split(',').map((index, indexNum) => {\n    index = index.trim();\n    const name = index.replace(/([&*]|\\+\\+)/g, \"\"); // Remove \"&\", \"++\" and \"*\"\n    // Let keyPath of \"[a+b]\" be [\"a\",\"b\"]:\n    const keyPath = /^\\[/.test(name) ? name.match(/^\\[(.*)\\]$/)[1].split('+') : name;\n\n    return createIndexSpec(\n      name,\n      keyPath || null,\n      /\\&/.test(index),\n      /\\*/.test(index),\n      /\\+\\+/.test(index),\n      isArray(keyPath),\n      indexNum === 0\n    );\n  });\n}\n", "import { Version as IVersion } from '../../public/types/version';\nimport { DbSchema } from '../../public/types/db-schema';\nimport { extend, keys } from '../../functions/utils';\nimport { <PERSON>ie } from '../dexie';\nimport { Transaction } from '../transaction';\nimport { removeTables<PERSON><PERSON>, setApiOnPlace, parseIndexSyntax } from './schema-helpers';\nimport { exceptions } from '../../errors';\nimport { createTableSchema } from '../../helpers/table-schema';\nimport { nop, promisableChain } from '../../functions/chaining-functions';\n\n/** class Version\n *\n * https://dexie.org/docs/Version/Version\n */\nexport class Version implements IVersion {\n  db: Dexie;\n  _cfg: {\n    version: number,\n    storesSource: { [tableName: string]: string | null },\n    dbschema: DbSchema,\n    tables: {},\n    contentUpgrade: Function | null\n  }\n\n  _parseStoresSpec(stores: { [tableName: string]: string | null }, outSchema: DbSchema): any {\n    keys(stores).forEach(tableName => {\n      if (stores[tableName] !== null) {\n          var indexes = parseIndexSyntax(stores[tableName]);\n          var primKey = indexes.shift();\n          if (primKey.multi) throw new exceptions.Schema(\"Primary key cannot be multi-valued\");\n          indexes.forEach(idx => {\n              if (idx.auto) throw new exceptions.Schema(\"Only primary key can be marked as autoIncrement (++)\");\n              if (!idx.keyPath) throw new exceptions.Schema(\"Index must have a name and cannot be an empty string\");\n          });\n          outSchema[tableName] = createTableSchema(tableName, primKey, indexes);\n      }\n    });\n  }\n\n  stores(stores: { [key: string]: string | null; }): IVersion {\n    const db = this.db;\n    this._cfg.storesSource = this._cfg.storesSource ?\n      extend(this._cfg.storesSource, stores) :\n      stores;\n    const versions = db._versions;\n\n    // Derive stores from earlier versions if they are not explicitely specified as null or a new syntax.\n    const storesSpec: { [key: string]: string; } = {};\n    let dbschema = {};\n    versions.forEach(version => { // 'versions' is always sorted by lowest version first.\n      extend(storesSpec, version._cfg.storesSource);\n      dbschema = (version._cfg.dbschema = {});\n      version._parseStoresSpec(storesSpec, dbschema);\n    });\n    // Update the latest schema to this version\n    db._dbSchema = dbschema;\n    // Update APIs\n    removeTablesApi(db, [db._allTables, db, db.Transaction.prototype]);\n    setApiOnPlace(db, [db._allTables, db, db.Transaction.prototype, this._cfg.tables], keys(dbschema), dbschema);\n    db._storeNames = keys(dbschema);\n    return this;\n  }\n\n  upgrade(upgradeFunction: (trans: Transaction) => PromiseLike<any> | void): Version {\n    this._cfg.contentUpgrade = promisableChain(this._cfg.contentUpgrade || nop, upgradeFunction);\n    return this;\n  }\n}\n", "import { <PERSON><PERSON> } from '../dexie';\nimport { makeClassConstructor } from '../../functions/make-class-constructor';\nimport { Version } from './version';\n\nexport interface VersionConstructor {\n  new(versionNumber: number): Version;\n  prototype: Version;\n}\n\n/** Generates a Version constructor bound to given <PERSON>ie instance.\n * \n * The purpose of having dynamically created constructors, is to allow\n * addons to extend classes for a certain Dexie instance without affecting\n * other db instances.\n */\nexport function createVersionConstructor(db: Dexie) {\n  return makeClassConstructor<VersionConstructor>(\n    Version.prototype,\n\n    function Version(this: Version, versionNumber: number) {\n      this.db = db;\n      this._cfg = {\n        version: versionNumber,\n        storesSource: null,\n        dbschema: {},\n        tables: {},\n        contentUpgrade: null\n      };\n    });\n\n}\n", "import { <PERSON><PERSON> } from \"../classes/dexie/dexie\";\nimport { Table } from \"../public/types/table\";\nimport { DBNAMES_DB } from \"../globals/constants\";\nimport { DexieDOMDependencies } from \"../public/types/dexie-dom-dependencies\";\nimport { nop } from \"../functions/chaining-functions\";\n\ntype IDBKeyNamesVar = typeof IDBKeyRange;\n\nfunction getDbNamesTable(indexedDB: IDBFactory, IDBKeyRange: IDBKeyNamesVar) {\n  let dbNamesDB = indexedDB[\"_dbNamesDB\"];\n  if (!dbNamesDB) {\n    dbNamesDB = indexedDB[\"_dbNamesDB\"] = new Dexie(DBNAMES_DB, {\n      addons: [],\n      indexedDB,\n      IDBKeyRange,\n    });\n    dbNamesDB.version(1).stores({ dbnames: \"name\" });\n  }\n  return dbNamesDB.table(\"dbnames\") as Table<{ name: string }, string>;\n}\n\nfunction hasDatabasesNative(indexedDB: IDBFactory) {\n  return indexedDB && typeof indexedDB.databases === \"function\";\n}\n\nexport function getDatabaseNames({\n  indexedDB,\n  IDBKeyRange,\n}: DexieDOMDependencies) {\n  return hasDatabasesNative(indexedDB)\n    ? Promise.resolve(indexedDB.databases()).then((infos) =>\n        infos\n          // Select name prop of infos:\n          .map((info) => info.name)\n          // Filter out DBNAMES_DB as previous Dexie or browser version would not have included it in the result.\n          .filter((name) => name !== DBNAMES_DB)\n      )\n    : getDbNamesTable(indexedDB, IDBKeyRange).toCollection().primaryKeys();\n}\n\nexport function _onDatabaseCreated(\n  { indexedDB, IDBKeyRange }: DexieDOMDependencies,\n  name: string\n) {\n  !hasDatabasesNative(indexedDB) &&\n    name !== DBNAMES_DB &&\n    getDbNamesTable(indexedDB, IDBKeyRange).put({name}).catch(nop);\n}\n\nexport function _onDatabaseDeleted(\n  { indexedDB, IDBKeyRange }: DexieDOMDependencies,\n  name: string\n) {\n  !hasDatabasesNative(indexedDB) &&\n    name !== DBNAMES_DB &&\n    getDbNamesTable(indexedDB, IDBKeyRange).delete(name).catch(nop);\n}\n", "import { newScope } from '../../helpers/promise';\nimport { PSD } from '../../helpers/promise';\n\nexport function vip (fn) {\n  // To be used by subscribers to the on('ready') event.\n  // This will let caller through to access DB even when it is blocked while the db.ready() subscribers are firing.\n  // This would have worked automatically if we were certain that the Provider was using Dexie.Promise for all asyncronic operations. The promise PSD\n  // from the provider.connect() call would then be derived all the way to when provider would call localDatabase.applyChanges(). But since\n  // the provider more likely is using non-promise async APIs or other thenable implementations, we cannot assume that.\n  // Note that this method is only useful for on('ready') subscribers that is returning a Promise from the event. If not using vip()\n  // the database could deadlock since it wont open until the returned Promise is resolved, and any non-VIPed operation started by\n  // the caller will not resolve until database is opened.\n  return newScope(function () {\n    PSD.letThrough = true; // Make sure we are let through if still blocking db due to onready is firing.\n    return fn();\n  });\n}\n\n", "/**\n * Work around Safari 14 IndexedDB open bug.\n *\n * Safari has a horrible bug where IDB requests can hang while the browser is starting up. https://bugs.webkit.org/show_bug.cgi?id=226547\n * The only solution is to keep nudging it until it's awake.\n */\nfunction idbReady() {\n    var isSafari = !navigator.userAgentData &&\n        /Safari\\//.test(navigator.userAgent) &&\n        !/Chrom(e|ium)\\//.test(navigator.userAgent);\n    // No point putting other browsers or older versions of Safari through this mess.\n    if (!isSafari || !indexedDB.databases)\n        return Promise.resolve();\n    var intervalId;\n    return new Promise(function (resolve) {\n        var tryIdb = function () { return indexedDB.databases().finally(resolve); };\n        intervalId = setInterval(tryIdb, 100);\n        tryIdb();\n    }).finally(function () { return clearInterval(intervalId); });\n}\n\nexport default idbReady;\n", "import { <PERSON><PERSON> } from './dexie';\nimport * as Debug from '../../helpers/debug';\nimport { rejection } from '../../helpers/promise';\nimport { exceptions } from '../../errors';\nimport { eventRejectHandler, preventDefault } from '../../functions/event-wrappers';\nimport Promise, { wrap } from '../../helpers/promise';\nimport { connections } from '../../globals/constants';\nimport { runUpgraders, readGlobalSchema, adjustToExistingIndexNames, verifyInstalledSchema } from '../version/schema-helpers';\nimport { safariMultiStoreFix } from '../../functions/quirks';\nimport { _onDatabaseCreated } from '../../helpers/database-enumerator';\nimport { vip } from './vip';\nimport { promisableChain, nop } from '../../functions/chaining-functions';\nimport { generateMiddlewareStacks } from './generate-middleware-stacks';\nimport { slice } from '../../functions/utils';\nimport safari14Workaround from 'safari-14-idb-fix';\n\nexport function dexieOpen (db: Dexie) {\n  const state = db._state;\n  const {indexedDB} = db._deps;\n  if (state.isBeingOpened || db.idbdb)\n      return state.dbReadyPromise.then<Dexie>(() => state.dbOpenError ?\n        rejection (state.dbOpenError) :\n        db);\n  Debug.debug && (state.openCanceller._stackHolder = Debug.getErrorWithStack()); // Let stacks point to when open() was called rather than where new Dexie() was called.\n  state.isBeingOpened = true;\n  state.dbOpenError = null;\n  state.openComplete = false;\n  const openCanceller = state.openCanceller;\n\n  function throwIfCancelled() {\n    // If state.openCanceller object reference is replaced, it means db.close() has been called,\n    // meaning this open flow should be cancelled.\n    if (state.openCanceller !== openCanceller) throw new exceptions.DatabaseClosed('db.open() was cancelled');\n  }\n  \n  // Function pointers to call when the core opening process completes.\n  let resolveDbReady = state.dbReadyResolve,\n      // upgradeTransaction to abort on failure.\n      upgradeTransaction: (IDBTransaction | null) = null,\n      wasCreated = false;\n\n  const tryOpenDB = () => new Promise((resolve, reject) => {\n    // Multiply db.verno with 10 will be needed to workaround upgrading bug in IE:\n    // IE fails when deleting objectStore after reading from it.\n    // A future version of Dexie.js will stopover an intermediate version to workaround this.\n    // At that point, we want to be backward compatible. Could have been multiplied with 2, but by using 10, it is easier to map the number to the real version number.\n    \n    throwIfCancelled();\n    // If no API, throw!\n    if (!indexedDB) throw new exceptions.MissingAPI();\n    const dbName = db.name;\n    \n    const req = state.autoSchema ?\n      indexedDB.open(dbName) :\n      indexedDB.open(dbName, Math.round(db.verno * 10));\n    if (!req) throw new exceptions.MissingAPI(); // May happen in Safari private mode, see https://github.com/dfahlander/Dexie.js/issues/134\n    req.onerror = eventRejectHandler(reject);\n    req.onblocked = wrap(db._fireOnBlocked);\n    req.onupgradeneeded = wrap (e => {\n        upgradeTransaction = req.transaction;\n        if (state.autoSchema && !db._options.allowEmptyDB) { // Unless an addon has specified db._allowEmptyDB, lets make the call fail.\n            // Caller did not specify a version or schema. Doing that is only acceptable for opening alread existing databases.\n            // If onupgradeneeded is called it means database did not exist. Reject the open() promise and make sure that we\n            // do not create a new database by accident here.\n            req.onerror = preventDefault; // Prohibit onabort error from firing before we're done!\n            upgradeTransaction.abort(); // Abort transaction (would hope that this would make DB disappear but it doesnt.)\n            // Close database and delete it.\n            req.result.close();\n            const delreq = indexedDB.deleteDatabase(dbName); // The upgrade transaction is atomic, and javascript is single threaded - meaning that there is no risk that we delete someone elses database here!\n            delreq.onsuccess = delreq.onerror = wrap(() => {\n                reject (new exceptions.NoSuchDatabase(`Database ${dbName} doesnt exist`));\n            });\n        } else {\n            upgradeTransaction.onerror = eventRejectHandler(reject);\n            var oldVer = e.oldVersion > Math.pow(2, 62) ? 0 : e.oldVersion; // Safari 8 fix.\n            wasCreated = oldVer < 1;\n            db._novip.idbdb = req.result;// db._novip is because db can be an Object.create(origDb).\n            runUpgraders(db, oldVer / 10, upgradeTransaction, reject);\n        }\n    }, reject);\n    \n    req.onsuccess = wrap (() => {\n        // Core opening procedure complete. Now let's just record some stuff.\n        upgradeTransaction = null;\n        const idbdb = db._novip.idbdb = req.result; // db._novip is because db can be an Object.create(origDb).\n\n        const objectStoreNames = slice(idbdb.objectStoreNames);\n        if (objectStoreNames.length > 0) try {\n          const tmpTrans = idbdb.transaction(safariMultiStoreFix(objectStoreNames), 'readonly');\n          if (state.autoSchema) readGlobalSchema(db, idbdb, tmpTrans);\n          else {\n              adjustToExistingIndexNames(db, db._dbSchema, tmpTrans);\n              if (!verifyInstalledSchema(db, tmpTrans)) {\n                  console.warn(`Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Some queries may fail.`);\n              }\n          }\n          generateMiddlewareStacks(db, tmpTrans);\n        } catch (e) {\n          // Safari 8 may bail out if > 1 store names. However, this shouldnt be a showstopper. Issue #120.\n          // BUGBUG: It will bail out anyway as of Dexie 3.\n          // Should we support Safari 8 anymore? Believe all\n          // Dexie users use the shim for that platform anyway?!\n          // If removing Safari 8 support, go ahead and remove the safariMultiStoreFix() function\n          // as well as absurd upgrade version quirk for Safari.\n        }\n        \n        connections.push(db); // Used for emulating versionchange event on IE/Edge/Safari.\n        \n        idbdb.onversionchange = wrap(ev => {\n            state.vcFired = true; // detect implementations that not support versionchange (IE/Edge/Safari)\n            db.on(\"versionchange\").fire(ev);\n        });\n        \n        idbdb.onclose = wrap(ev => {\n            db.on(\"close\").fire(ev);\n        });\n\n        if (wasCreated) _onDatabaseCreated(db._deps, dbName);\n\n        resolve();\n\n    }, reject);\n  }).catch(err => {\n    if (err && err.name === 'UnknownError' && state.PR1398_maxLoop > 0) {\n      // Bug in Chrome after clearing site data\n      // https://github.com/dexie/Dexie.js/issues/543#issuecomment-1795736695\n      state.PR1398_maxLoop--;\n      console.warn('Dexie: Workaround for Chrome UnknownError on open()');\n      return tryOpenDB();\n    } else {\n      return Promise.reject(err);\n    }\n  });\n  \n  // safari14Workaround = Workaround by jakearchibald for new nasty bug in safari 14.\n  return Promise.race([\n    openCanceller,\n    (typeof navigator === 'undefined' ? Promise.resolve() : safari14Workaround()).then(tryOpenDB)\n  ]).then(() => {\n      // Before finally resolving the dbReadyPromise and this promise,\n      // call and await all on('ready') subscribers:\n      // Dexie.vip() makes subscribers able to use the database while being opened.\n      // This is a must since these subscribers take part of the opening procedure.\n      throwIfCancelled();\n      state.onReadyBeingFired = [];\n      return Promise.resolve(vip(()=>db.on.ready.fire(db.vip))).then(function fireRemainders() {\n          if (state.onReadyBeingFired.length > 0) {\n              // In case additional subscribers to db.on('ready') were added during the time db.on.ready.fire was executed.\n              let remainders = state.onReadyBeingFired.reduce(promisableChain, nop);\n              state.onReadyBeingFired = [];\n              return Promise.resolve(vip(()=>remainders(db.vip))).then(fireRemainders)\n          }\n      });\n  }).finally(()=>{\n      state.onReadyBeingFired = null;\n      state.isBeingOpened = false;\n  }).then(()=>{\n      // Resolve the db.open() with the db instance.\n      return db;\n  }).catch(err => {\n      state.dbOpenError = err; // Record the error. It will be used to reject further promises of db operations.\n      try {\n        // Did we fail within onupgradeneeded? Make sure to abort the upgrade transaction so it doesnt commit.\n        upgradeTransaction && upgradeTransaction.abort();\n      } catch { }\n      if (openCanceller === state.openCanceller) {\n        // Still in the same open flow - The error reason was not due to external call to db.close().\n        // Make sure to call db.close() to finalize resources.\n        db._close(); // Closes and resets idbdb, removes connections, resets dbReadyPromise and openCanceller so that a later db.open() is fresh.\n      }\n      return rejection (err);\n  }).finally(()=>{\n      state.openComplete = true;\n      resolveDbReady(); // dbReadyPromise is resolved no matter if open() rejects or resolved. It's just to wake up waiters.\n  });\n}\n", "import { isArray } from '../functions/utils';\n\nexport function awaitIterator (iterator: Iterator<any>) {\n  var callNext = result => iterator.next(result),\n      doThrow = error => iterator.throw(error),\n      onSuccess = step(callNext),\n      onError = step(doThrow);\n\n  function step(getNext: (any)=>any) {\n      return (val?) => {\n          var next = getNext(val),\n              value = next.value;\n\n          return next.done ? value :\n              (!value || typeof value.then !== 'function' ?\n                  isArray(value) ? Promise.all(value).then(onSuccess, onError) : onSuccess(value) :\n                  value.then(onSuccess, onError));\n      };\n  }\n\n  return step(callNext)();\n}\n", "import { TransactionMode } from '../../public/types/transaction-mode';\nimport { errnames, exceptions } from '../../errors';\nimport { flatten, isAsyncFunction } from '../../functions/utils';\nimport { <PERSON>ie } from './dexie';\nimport { Transaction } from '../transaction';\nimport { await<PERSON>terator } from '../../helpers/yield-support';\nimport Promise, {\n  PSD,\n  NativePromise,\n  decrementExpectedAwaits,\n  rejection,\n  incrementExpectedAwaits\n} from '../../helpers/promise';\n\nexport function extractTransactionArgs(mode: TransactionMode, _tableArgs_, scopeFunc) {\n  // Let table arguments be all arguments between mode and last argument.\n  var i = arguments.length;\n  if (i < 2) throw new exceptions.InvalidArgument(\"Too few arguments\");\n  // Prevent optimzation killer (https://github.com/petkaantonov/bluebird/wiki/Optimization-killers#32-leaking-arguments)\n  // and clone arguments except the first one into local var 'args'.\n  var args = new Array(i - 1);\n  while (--i) args[i - 1] = arguments[i];\n  // Let scopeFunc be the last argument and pop it so that args now only contain the table arguments.\n  scopeFunc = args.pop();\n  var tables = flatten(args); // Support using array as middle argument, or a mix of arrays and non-arrays.\n  return [mode, tables, scopeFunc];\n}\n\nexport function enterTransactionScope(\n  db: Dexie,\n  mode: IDBTransactionMode,\n  storeNames: string[],\n  parentTransaction: Transaction | undefined,\n  scopeFunc: ()=>PromiseLike<any> | any\n) {\n  return Promise.resolve().then(() => {\n    // Keep a pointer to last non-transactional PSD to use if someone calls Dexie.ignoreTransaction().\n    const transless = PSD.transless || PSD;\n    // Our transaction.\n    //return new Promise((resolve, reject) => {\n    const trans = db._createTransaction(mode, storeNames, db._dbSchema, parentTransaction);\n    // Let the transaction instance be part of a Promise-specific data (PSD) value.\n    const zoneProps = {\n      trans: trans,\n      transless: transless\n    };\n\n    if (parentTransaction) {\n      // Emulate transaction commit awareness for inner transaction (must 'commit' when the inner transaction has no more operations ongoing)\n      trans.idbtrans = parentTransaction.idbtrans;\n    } else {\n      try {\n        trans.create(); // Create the native transaction so that complete() or error() will trigger even if no operation is made upon it.\n        db._state.PR1398_maxLoop = 3;\n      } catch (ex) {\n        if (ex.name === errnames.InvalidState && db.isOpen() && --db._state.PR1398_maxLoop > 0) {\n          console.warn('Dexie: Need to reopen db');\n          db._close();\n          return db.open().then(() => enterTransactionScope(\n            db,\n            mode,\n            storeNames,\n            null,\n            scopeFunc\n          ));\n        }\n        return rejection(ex);\n      }\n    }\n\n    // Support for native async await.\n    const scopeFuncIsAsync = isAsyncFunction(scopeFunc);\n    if (scopeFuncIsAsync) {\n      incrementExpectedAwaits();\n    }\n\n    let returnValue;\n    const promiseFollowed = Promise.follow(() => {\n      // Finally, call the scope function with our table and transaction arguments.\n      returnValue = scopeFunc.call(trans, trans);\n      if (returnValue) {\n        if (scopeFuncIsAsync) {\n          // scopeFunc is a native async function - we know for sure returnValue is native promise.\n          var decrementor = decrementExpectedAwaits.bind(null, null);\n          returnValue.then(decrementor, decrementor);\n        } else if (typeof returnValue.next === 'function' && typeof returnValue.throw === 'function') {\n          // scopeFunc returned an iterator with throw-support. Handle yield as await.\n          returnValue = awaitIterator(returnValue);\n        }\n      }\n    }, zoneProps);\n    return (returnValue && typeof returnValue.then === 'function' ?\n      // Promise returned. User uses promise-style transactions.\n      Promise.resolve(returnValue).then(x => trans.active ?\n        x // Transaction still active. Continue.\n        : rejection(new exceptions.PrematureCommit(\n          \"Transaction committed too early. See http://bit.ly/2kdckMn\")))\n      // No promise returned. Wait for all outstanding promises before continuing. \n      : promiseFollowed.then(() => returnValue)\n    ).then(x => {\n      // sub transactions don't react to idbtrans.oncomplete. We must trigger a completion:\n      if (parentTransaction) trans._resolve();\n      // wait for trans._completion\n      // (if root transaction, this means 'complete' event. If sub-transaction, we've just fired it ourselves)\n      return trans._completion.then(() => x);\n    }).catch(e => {\n      trans._reject(e); // Yes, above then-handler were maybe not called because of an unhandled rejection in scopeFunc!\n      return rejection(e);\n    });\n  });\n}\n", "import {\n  DBCore,\n  DBCoreIndex,\n  DBCoreKeyRange,\n  DBCoreQueryRequest,\n  DBCoreRangeType,\n  DBCoreOpenCursorRequest,\n  DBCoreCountRequest,\n  DBCoreCursor,\n  DBCoreTable,\n} from \"../public/types/dbcore\";\nimport { isArray } from '../functions/utils';\nimport { getKeyExtractor } from './get-key-extractor';\nimport { getKeyPathAlias } from './dbcore-indexeddb';\nimport { Middleware } from '../public/types/middleware';\n\ninterface VirtualIndex extends DBCoreIndex {\n  /** True if this index is virtual, i.e. represents a compound index internally,\n   * but makes it act as as having a subset of its keyPaths.\n   */\n  isVirtual: boolean;\n\n  /** Number of keypaths that this index comprises. Can be 0..N.\n   * Note: This is the length of the *virtual index*, not the real index.\n   */\n  keyLength: number;\n\n  /** Number of popped keypaths from the real index.\n   */\n  keyTail: number;\n}\n\n// Move into some util:\nexport function pad (a: any | any[], value: any, count: number) {\n  const result = isArray(a) ? a.slice() : [a];\n  for (let i=0; i<count; ++i) result.push(value);\n  return result;\n}\n\n\nexport function createVirtualIndexMiddleware (down: DBCore) : DBCore {\n  return {\n    ...down,\n    table(tableName: string) {\n      const table = down.table(tableName);\n      const {schema} = table;\n      const indexLookup: {[indexAlias: string]: VirtualIndex[]} = {};\n      const allVirtualIndexes: VirtualIndex[] = [];\n\n      function addVirtualIndexes (keyPath: null | string | string[], keyTail: number, lowLevelIndex: DBCoreIndex): VirtualIndex {\n        const keyPathAlias = getKeyPathAlias(keyPath);\n        const indexList = (indexLookup[keyPathAlias] = indexLookup[keyPathAlias] || []);\n        const keyLength = keyPath == null ? 0: typeof keyPath === 'string' ? 1 : keyPath.length;\n        const isVirtual = keyTail > 0;\n        const virtualIndex = {\n          ...lowLevelIndex,\n          isVirtual,\n          keyTail,\n          keyLength,\n          extractKey: getKeyExtractor(keyPath),\n          unique: !isVirtual && lowLevelIndex.unique\n        };\n        indexList.push(virtualIndex);\n        if (!virtualIndex.isPrimaryKey) {\n          allVirtualIndexes.push(virtualIndex);\n        }\n        if (keyLength > 1) {\n          const virtualKeyPath = keyLength === 2 ?\n            keyPath[0] : // This is a compound [a, b]. Add a virtual normal index a.\n            keyPath.slice(0, keyLength - 1); // This is compound [a,b,c]. Add virtual compound [a,b].\n          addVirtualIndexes(virtualKeyPath, keyTail + 1, lowLevelIndex);\n        }\n        indexList.sort((a,b) => a.keyTail - b.keyTail); // Shortest keyTail is the best one (represents real index)\n        return virtualIndex;\n      }\n    \n      const primaryKey = addVirtualIndexes(schema.primaryKey.keyPath, 0, schema.primaryKey);\n      indexLookup[\":id\"] = [primaryKey];\n      for (const index of schema.indexes) {\n        addVirtualIndexes(index.keyPath, 0, index);\n      }\n    \n      function findBestIndex(keyPath: null | string | string[]): VirtualIndex {\n        const result = indexLookup[getKeyPathAlias(keyPath)];\n        return result && result[0];\n      }\n    \n      function translateRange (range: DBCoreKeyRange, keyTail: number): DBCoreKeyRange {\n        return {\n          type: range.type === DBCoreRangeType.Equal ?\n            DBCoreRangeType.Range :\n            range.type,\n          lower: pad(range.lower, range.lowerOpen ? down.MAX_KEY : down.MIN_KEY, keyTail),\n          lowerOpen: true, // doesn't matter true or false\n          upper: pad(range.upper, range.upperOpen ? down.MIN_KEY : down.MAX_KEY, keyTail),\n          upperOpen: true // doesn't matter true or false\n        };\n      }\n    \n      function translateRequest (req: DBCoreQueryRequest): DBCoreQueryRequest;\n      function translateRequest (req: DBCoreOpenCursorRequest): DBCoreOpenCursorRequest;\n      function translateRequest (req: DBCoreCountRequest): DBCoreCountRequest {\n        const index = req.query.index as VirtualIndex;\n        return index.isVirtual ? {\n          ...req,\n          query: {\n            index,\n            range: translateRange(req.query.range, index.keyTail)\n          }\n        } : req;\n      }\n    \n      const result: DBCoreTable = {\n        ...table,\n        schema: {\n          ...schema,\n          primaryKey,\n          indexes: allVirtualIndexes,\n          getIndexByKeyPath: findBestIndex\n        },\n\n        count(req) {\n          return table.count(translateRequest(req));\n        },    \n    \n        query(req) {\n          return table.query(translateRequest(req));\n        },\n    \n        openCursor(req) {\n          const {keyTail, isVirtual, keyLength} = (req.query.index as VirtualIndex);\n          if (!isVirtual) return table.openCursor(req);\n    \n          function createVirtualCursor(cursor: DBCoreCursor) : DBCoreCursor {\n            function _continue (key?: any) {\n              key != null ?\n                cursor.continue(pad(key, req.reverse ? down.MAX_KEY : down.MIN_KEY, keyTail)) :\n                req.unique ?\n                  cursor.continue(\n                    cursor.key.slice(0, keyLength)\n                      .concat(req.reverse\n                        ? down.MIN_KEY\n                        : down.MAX_KEY, keyTail)\n                  ) :\n                  cursor.continue()\n            }\n            const virtualCursor = Object.create(cursor, {\n              continue: {value: _continue},\n              continuePrimaryKey: {\n                value(key: any, primaryKey: any) {\n                  cursor.continuePrimaryKey(pad(key, down.MAX_KEY, keyTail), primaryKey);\n                }\n              },\n              primaryKey: {\n                get() {\n                  return cursor.primaryKey;\n                }\n              },\n              key: {\n                get() {\n                  const key = cursor.key as any[]; // A virtual cursor always operates on compound key\n                  return keyLength === 1 ?\n                    key[0] : // Cursor.key should not be an array.\n                    key.slice(0, keyLength); // Cursor.key should be first part of array.\n                }\n              },\n              value: {\n                get() {\n                  return cursor.value;\n                }\n              }\n            });\n            return virtualCursor;\n          }\n    \n          return table.openCursor(translateRequest(req))\n            .then(cursor => cursor && createVirtualCursor(cursor));\n        }\n      };\n      return result;\n    }\n  }\n}\n\nexport const virtualIndexMiddleware : Middleware<DBCore> = {\n  stack: \"dbcore\",\n  name: \"VirtualIndexMiddleware\",\n  level: 1,\n  create: createVirtualIndexMiddleware\n};\n\n", "import { keys, hasOwn, toStringTag } from './utils';\n\nexport function getObjectDiff(a: any, b: any, rv?: any, prfx?: string) {\n  // Compares objects a and b and produces a diff object.\n  rv = rv || {};\n  prfx = prfx || '';\n  keys(a).forEach((prop) => {\n    if (!hasOwn(b, prop)) {\n      // Property removed\n      rv[prfx + prop] = undefined;\n    } else {\n      var ap = a[prop],\n        bp = b[prop];\n      if (typeof ap === 'object' && typeof bp === 'object' && ap && bp) {\n        const apTypeName = toStringTag(ap);\n        const bpTypeName = toStringTag(bp);\n\n        if (apTypeName !== bpTypeName) {\n          rv[prfx + prop] = b[prop]; // Property changed to other type\n        } else if (apTypeName === 'Object') {\n          // Pojo objects (not Date, ArrayBuffer, Array etc). Go deep.\n          getObjectDiff(ap, bp, rv, prfx + prop + '.');\n        } else if (ap !== bp) {\n          // Values differ.\n          // Could have checked if Date, arrays or binary types have same\n          // content here but I think that would be a suboptimation.\n          // Prefer simplicity.\n          rv[prfx + prop] = b[prop];\n        }\n      } else if (ap !== bp) rv[prfx + prop] = b[prop]; // Primitive value changed\n    }\n  });\n  keys(b).forEach((prop) => {\n    if (!hasOwn(a, prop)) {\n      rv[prfx + prop] = b[prop]; // Property added\n    }\n  });\n  return rv;\n}\n", "import {\n  DBCoreAddRequest,\n  DBCorePutRequest,\n  DBCoreDeleteRequest,\n  DBCoreIndex,\n  DBCoreTable,\n} from \"../public/types/dbcore\";\n\nexport function getEffectiveKeys (\n  primaryKey: DBCoreIndex,\n  req: (Pick<DBCoreAddRequest | DBCorePutRequest, \"type\" | \"values\"> & {keys?: any[]}) | Pick<DBCoreDeleteRequest, \"keys\" | \"type\">)\n{\n  //const {outbound} = primaryKey;\n  if (req.type === 'delete') return req.keys;\n  return req.keys || req.values.map(primaryKey.extractKey)\n}\n", "import {\n  DBCore,\n  DBCoreTable,\n  DBCoreMutateResponse,\n  DBCoreDeleteRangeRequest,\n  DBCoreAddRequest,\n  DBCorePutRequest,\n  DBCoreDeleteRequest,\n  DBCoreTransaction,\n  DBCoreKeyRange\n} from \"../public/types/dbcore\";\nimport { nop } from '../functions/chaining-functions';\nimport { hasOwn, setByKeyPath } from '../functions/utils';\nimport { getObjectDiff } from \"../functions/get-object-diff\";\nimport { PSD } from '../helpers/promise';\n//import { LockableTableMiddleware } from '../dbcore/lockable-table-middleware';\nimport { getEffectiveKeys } from '../dbcore/get-effective-keys';\nimport { Middleware } from '../public/types/middleware';\nimport { Transaction } from '../classes/transaction';\n\nexport const hooksMiddleware: Middleware<DBCore>  = {\n  stack: \"dbcore\",\n  name: \"HooksMiddleware\",\n  level: 2,\n  create: (downCore: DBCore) => ({\n    ...downCore,\n    table(tableName: string) {\n      const downTable = downCore.table(tableName);\n      const {primaryKey} = downTable.schema;\n  \n      const tableMiddleware: DBCoreTable = {\n        ...downTable,\n        mutate(req):Promise<DBCoreMutateResponse> {\n          const dxTrans = PSD.trans as Transaction;\n          // Hooks can be transaction-bound. Need to grab them from transaction.table and not\n          // db.table!\n          const {deleting, creating, updating} = dxTrans.table(tableName).hook;\n          switch (req.type) {\n            case 'add':\n              if (creating.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'put':\n              if (creating.fire === nop && updating.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'delete':\n              if (deleting.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>addPutOrDelete(req), true);\n            case 'deleteRange':\n              if (deleting.fire === nop) break;\n              return dxTrans._promise('readwrite', ()=>deleteRange(req), true);\n          }\n          // Any of the breaks above happened (no hooks) - do the default:\n          return downTable.mutate(req);\n\n\n          function addPutOrDelete(req: DBCoreAddRequest | DBCorePutRequest | DBCoreDeleteRequest): Promise<DBCoreMutateResponse> {\n            const dxTrans = PSD.trans;\n            const keys = req.keys || getEffectiveKeys(primaryKey, req);\n            if (!keys) throw new Error(\"Keys missing\");\n            // Clone Request and set keys arg\n            req = req.type === 'add' || req.type === 'put' ?\n              {...req, keys} :\n              {...req};\n            if (req.type !== 'delete') req.values = [...req.values];\n            if (req.keys) req.keys = [...req.keys];\n  \n            return getExistingValues(downTable, req, keys).then (existingValues => {\n              const contexts = keys.map((key, i) => {\n                const existingValue = existingValues[i];\n                const ctx = { onerror: null, onsuccess: null };\n                if (req.type === 'delete') {\n                  // delete operation\n                  deleting.fire.call(ctx, key, existingValue, dxTrans);\n                } else if (req.type === 'add' || existingValue === undefined) {\n                  // The add() or put() resulted in a create\n                  const generatedPrimaryKey = creating.fire.call(ctx, key, req.values[i], dxTrans);\n                  if (key == null && generatedPrimaryKey != null) {\n                    key = generatedPrimaryKey;\n                    req.keys[i] = key;\n                    if (!primaryKey.outbound) {\n                      setByKeyPath(req.values[i], primaryKey.keyPath, key);\n                    }\n                  }\n                } else {\n                  // The put() operation resulted in an update\n                  const objectDiff = getObjectDiff(existingValue, req.values[i]);\n                  const additionalChanges = updating.fire.call(ctx, objectDiff, key, existingValue, dxTrans);\n                  if (additionalChanges) {\n                    const requestedValue = req.values[i];\n                    Object.keys(additionalChanges).forEach(keyPath => {\n                      if (hasOwn(requestedValue, keyPath)) {\n                        // keyPath is already present as a literal property of the object\n                        requestedValue[keyPath] = additionalChanges[keyPath];\n                      } else {\n                        // keyPath represents a new or existing path into the object\n                        setByKeyPath(requestedValue, keyPath, additionalChanges[keyPath]);\n                      }\n                    });\n                  }\n                }\n                return ctx;\n              });\n              return downTable.mutate(req).then(({failures, results, numFailures, lastResult}) => {\n                for (let i=0; i<keys.length; ++i) {\n                  const primKey = results ? results[i] : keys[i];\n                  const ctx = contexts[i];\n                  if (primKey == null) {\n                    ctx.onerror && ctx.onerror(failures[i]);\n                  } else {\n                    ctx.onsuccess && ctx.onsuccess(\n                      req.type === 'put' && existingValues[i] ? // the put resulted in an update\n                        req.values[i] : // update hooks expects existing value\n                        primKey // create hooks expects primary key\n                    );\n                  }\n                }\n                return {failures, results, numFailures, lastResult};\n              }).catch(error => {\n                contexts.forEach(ctx => ctx.onerror && ctx.onerror(error));\n                return Promise.reject(error);\n              });\n            });\n          }\n  \n          function deleteRange(req: DBCoreDeleteRangeRequest): Promise<DBCoreMutateResponse> {\n            return deleteNextChunk(req.trans, req.range, 10000);\n          }\n  \n          function deleteNextChunk(trans: DBCoreTransaction, range: DBCoreKeyRange, limit: number) {\n            // Query what keys in the DB within the given range\n            return downTable.query({trans, values: false, query: {index: primaryKey, range}, limit})\n            .then(({result}) => {\n              // Given a set of keys, bulk delete those using the same procedure as in addPutOrDelete().\n              // This will make sure that deleting hook is called.\n              return addPutOrDelete({type: 'delete', keys: result, trans}).then(res => {\n                if (res.numFailures > 0) return Promise.reject(res.failures[0]);\n                if (result.length < limit) {\n                  return {failures: [], numFailures: 0, lastResult: undefined} as DBCoreMutateResponse;\n                } else {\n                  return deleteNextChunk(trans, {...range, lower: result[result.length - 1], lowerOpen: true}, limit);\n                }\n              });\n            })\n          }\n        }\n      };\n      //const {lock, lockableMiddleware} = LockableTableMiddleware(tableMiddleware);\n\n      return tableMiddleware;\n    },\n  }) as DBCore\n};\n\nfunction getExistingValues(\n  table: DBCoreTable,\n  req: DBCoreAddRequest | DBCorePutRequest | DBCoreDeleteRequest,\n  effectiveKeys: any[]\n) {\n  return req.type === \"add\"\n    ? Promise.resolve([])\n    : table.getMany({ trans: req.trans, keys: effectiveKeys, cache: \"immutable\" });\n}\n", "import { deepClone } from \"../functions/utils\";\nimport { DBCore } from \"../public/types/dbcore\";\nimport { Middleware } from \"../public/types/middleware\";\nimport Promise from \"../helpers/promise\";\nimport { cmp } from '../functions/cmp';\n\nexport function getFromTransactionCache(\n  keys: any[],\n  cache: { keys: any[]; values: any[] } | undefined | null,\n  clone?: boolean\n) {\n  try {\n    if (!cache) return null;\n    if (cache.keys.length < keys.length) return null;\n    const result: any[] = [];\n    // Compare if the exact same order of keys was retrieved in same transaction:\n    // Allow some cached keys to be omitted from provided set of keys\n    // Use case: 1. getMany(keys) 2. update a subset of those 3. call put with the updated ones ==> middlewares should be able to find old values\n    for (let i = 0, j = 0; i < cache.keys.length && j < keys.length; ++i) {\n      if (cmp(cache.keys[i], keys[j]) !== 0) continue;\n      result.push(clone ? deepClone(cache.values[i]) : cache.values[i]);\n      ++j;\n    }\n    // If got all keys caller was looking for, return result.\n    return result.length === keys.length ? result : null;\n  } catch {\n    return null;\n  }\n}\n\nexport const cacheExistingValuesMiddleware: Middleware<DBCore> = {\n  stack: \"dbcore\",\n  level: -1,\n  create: (core) => {\n    return {\n      table: (tableName) => {\n        const table = core.table(tableName);\n        return {\n          ...table,\n          getMany: (req) => {\n            if (!req.cache) {\n              return table.getMany(req);\n            }\n            const cachedResult = getFromTransactionCache(\n              req.keys,\n              req.trans[\"_cache\"],\n              req.cache === \"clone\"\n            );\n            if (cachedResult) {\n              return Promise.resolve(cachedResult);\n            }\n            return table.getMany(req).then((res) => {\n              req.trans[\"_cache\"] = {\n                keys: req.keys,\n                values: req.cache === \"clone\" ? deepClone(res) : res,\n              };\n              return res;\n            });\n          },\n          mutate: (req) => {\n            // Invalidate cache on any mutate except \"add\" which can't change existing values:\n            if (req.type !== \"add\") req.trans[\"_cache\"] = null;\n            return table.mutate(req);\n          },\n        };\n      },\n    };\n  },\n};\n", "import { cmp } from \"../functions/cmp\";\nimport { extend, iteratorSymbol, props } from '../functions/utils';\nimport { IndexableType } from '../public';\nimport {\n  EmptyRange,\n  IntervalTree,\n  IntervalTreeNode,\n  RangeSetConstructor,\n  RangeSetPrototype,\n} from \"../public/types/rangeset\";\n\n/* An interval tree implementation to efficiently detect overlapping ranges of queried indexes.\n *\n * https://en.wikipedia.org/wiki/Interval_tree\n * \n */\n\nfunction isEmptyRange(node: IntervalTree | {from: IndexableType, to: IndexableType}): node is EmptyRange {\n  return !(\"from\" in node);\n}\n\nexport type RangeSet = RangeSetPrototype & IntervalTree;\n\nexport const RangeSet = function(fromOrTree: any, to?: any) {\n  if (this) {\n    // Called with new()\n    extend(this, arguments.length ? {d:1, from: fromOrTree, to: arguments.length > 1 ? to : fromOrTree} : {d:0});\n  } else {\n    // Called without new()\n    const rv = new RangeSet();\n    if (fromOrTree && (\"d\" in fromOrTree)) {\n      extend(rv, fromOrTree);\n    }\n    return rv;\n  }\n} as RangeSetConstructor;\n\nprops(RangeSet.prototype, {\n  add(rangeSet: IntervalTree | {from: IndexableType, to: IndexableType}) {\n    mergeRanges(this, rangeSet);\n    return this;\n  },\n  addKey(key: IndexableType) {\n    addRange(this, key, key);\n    return this;\n  },\n  addKeys(keys: IndexableType[]) {\n    keys.forEach(key => addRange(this, key, key));\n    return this;\n  },\n\n  [iteratorSymbol](): Iterator<IntervalTreeNode, undefined, IndexableType | undefined> {\n    return getRangeSetIterator(this);\n  }\n});\n\nfunction addRange(target: IntervalTree, from: IndexableType, to: IndexableType) {\n  const diff = cmp(from, to);\n  // cmp() returns NaN if one of the args are IDB-invalid keys.\n  // Avoid storing invalid keys in rangeset:\n  if (isNaN(diff)) return;\n\n  // Caller is trying to add a range where from is greater than to:\n  if (diff > 0) throw RangeError();\n  \n  if (isEmptyRange(target)) return extend(target, { from, to, d: 1 });\n  const left = target.l;\n  const right = target.r;\n  if (cmp(to, target.from) < 0) {\n    left\n      ? addRange(left, from, to)\n      : (target.l = { from, to, d: 1, l: null, r: null });\n    return rebalance(target);\n  }\n  if (cmp(from, target.to) > 0) {\n    right\n      ? addRange(right, from, to)\n      : (target.r = { from, to, d: 1, l: null, r: null });\n    return rebalance(target);\n  }\n  // Now we have some kind of overlap. We will be able to merge the new range into the node or let it be swallowed.\n\n  // Grow left?\n  if (cmp(from, target.from) < 0) {\n    target.from = from;\n    target.l = null; // Cut off for now. Re-add later.\n    target.d = right ? right.d + 1 : 1;\n  }\n  // Grow right?\n  if (cmp(to, target.to) > 0) {\n    target.to = to;\n    target.r = null; // Cut off for now. Re-add later.\n    target.d = target.l ? target.l.d + 1 : 1;\n  }\n  const rightWasCutOff = !target.r;\n  // Re-add left?\n  if (left && !target.l) {\n    //Ranges to the left may be swallowed. Cut it of and re-add all.\n    //Could probably be done more efficiently!\n    mergeRanges(target, left);\n  }\n  // Re-add right?\n  if (right && rightWasCutOff) {\n    //Ranges to the right may be swallowed. Cut it of and re-add all.\n    //Could probably be done more efficiently!\n    mergeRanges(target, right);\n  }\n}\n\nexport function mergeRanges(target: IntervalTree, newSet: IntervalTree | {from: IndexableType, to: IndexableType}) {\n  function _addRangeSet(\n    target: IntervalTree,\n    { from, to, l, r }: IntervalTreeNode | {from: IndexableType, to: IndexableType, l?: undefined, r?: undefined}\n  ) {\n    addRange(target, from, to);\n    if (l) _addRangeSet(target, l);\n    if (r) _addRangeSet(target, r);\n  }\n\n  if(!isEmptyRange(newSet)) _addRangeSet(target, newSet);\n}\n\nexport function rangesOverlap(\n  rangeSet1: IntervalTree,\n  rangeSet2: IntervalTree\n): boolean {\n    // Start iterating other from scratch.\n    const i1 = getRangeSetIterator(rangeSet2);\n    let nextResult1 = i1.next();\n    if (nextResult1.done) return false;\n    let a = nextResult1.value;\n\n    // Start iterating this from start of other\n    const i2 = getRangeSetIterator(rangeSet1);\n    let nextResult2 = i2.next(a.from); // Start from beginning of other range\n    let b = nextResult2.value;\n\n    while (!nextResult1.done && !nextResult2.done) {\n      if (cmp(b!.from, a.to) <= 0 && cmp(b!.to, a.from) >= 0) return true;\n      cmp(a.from, b!.from) < 0\n        ? (a = (nextResult1 = i1.next(b!.from)).value!) // a is behind. forward it to beginning of next b-range\n        : (b = (nextResult2 = i2.next(a.from)).value); // b is behind. forward it to beginning of next a-range\n    }\n  return false;\n}\n\ntype RangeSetIteratorState =\n  | {\n      up?: RangeSetIteratorState;\n      n: IntervalTreeNode;\n      s: 0 | 1 | 2 | 3;\n    }\n  | undefined\n  | null;\nexport function getRangeSetIterator(\n  node: EmptyRange | IntervalTreeNode\n): Generator<IntervalTreeNode, undefined, IndexableType | undefined> {\n  let state: RangeSetIteratorState = isEmptyRange(node) ? null : { s: 0, n: node };\n\n  return {\n    next(key?) {\n      const keyProvided = arguments.length > 0;\n      while (state) {\n        switch (state.s) {\n          case 0:\n            // Initial state for node.\n            // Fast forward to leftmost node.\n            state.s = 1;\n            if (keyProvided) {\n              while (state.n.l && cmp(key, state.n.from) < 0)\n                state = { up: state, n: state.n.l, s: 1 };\n            } else {\n              while (state.n.l) state = { up: state, n: state.n.l, s: 1 };\n            }\n          // intentionally fall into case 1:\n          case 1:\n            // We're on a node where it's left part is already handled or does not exist.\n            state.s = 2;\n            if (!keyProvided || cmp(key, state.n.to) <= 0)\n              return { value: state.n, done: false };\n          case 2:\n            // We've emitted our node and should continue with the right part or let parent take over from it's state 1\n            if (state.n.r) {\n              state.s = 3; // So when child is done, we know we're done.\n              state = { up: state, n: state.n.r, s: 0 };\n              continue; // Will fall in to case 0 with fast forward to left leaf of this subtree.\n            }\n          // intentionally fall into case 3:\n          case 3:\n            state = state.up;\n        }\n      }\n      return { done: true };\n    },\n  } as Generator<IntervalTreeNode, undefined, IndexableType>;\n}\n\nfunction rebalance(target: IntervalTreeNode) {\n  const diff = (target.r?.d || 0) - (target.l?.d || 0);\n  const r = diff > 1 ? \"r\" : diff < -1 ? \"l\" : \"\";\n  if (r) {\n\n    // Rotate (https://en.wikipedia.org/wiki/Tree_rotation)\n    //\n    // \n    //                    [OLDROOT]\n    //       [OLDROOT.L]            [NEWROOT]\n    //                        [NEWROOT.L] [NEWROOT.R]\n    //\n    // Is going to become:\n    //\n    // \n    //                    [NEWROOT]\n    //        [OLDROOT]             [NEWROOT.R]\n    // [OLDROOT.L] [NEWROOT.L]  \n\n    // * clone now has the props of OLDROOT\n    // Plan:\n    // * target must be given the props of NEWROOT\n    // * target[l] must point to a new OLDROOT\n    // * target[r] must point to NEWROOT.R\n    // * OLDROOT[r] must point to NEWROOT.L\n    const l = r === \"r\" ? \"l\" : \"r\"; // Support both left/right rotation\n    const rootClone = { ...target };\n    // We're gonna copy props from target's right node into target so that target will\n    // have same range as old target[r] (instead of changing pointers, we copy values.\n    // that way we do not need to adjust pointers in parents).\n    const oldRootRight = target[r]; \n    target.from = oldRootRight.from;\n    target.to = oldRootRight.to;\n    target[r] = oldRootRight[r];\n    rootClone[r] = oldRootRight[l];\n    target[l] = rootClone;\n    rootClone.d = computeDepth(rootClone);\n  }\n  target.d = computeDepth(target);\n}\n\nfunction computeDepth({ r, l }: Pick<IntervalTreeNode, \"l\" | \"r\">) {\n  return (r ? (l ? Math.max(r.d, l.d) : r.d) : l ? l.d : 0) + 1;\n}\n", "import { getFromTransactionCache } from \"../dbcore/cache-existing-values-middleware\";\nimport { cmp } from \"../functions/cmp\";\nimport { isArray, keys } from \"../functions/utils\";\nimport { PSD } from \"../helpers/promise\";\nimport { RangeSet } from \"../helpers/rangeset\";\nimport { ObservabilitySet } from \"../public/types/db-events\";\nimport {\n  DBCore,\n  DBCoreCountRequest,\n  DBCoreCursor,\n  DBCoreGetManyRequest,\n  DBCoreGetRequest,\n  DBCoreIndex,\n  DBCoreOpenCursorRequest,\n  DBCoreQueryRequest,\n  DBCoreQueryResponse,\n  DBCoreTable,\n  DBCoreTableSchema,\n  DBCoreTransaction,\n} from \"../public/types/dbcore\";\nimport { Middleware } from \"../public/types/middleware\";\n\nexport const observabilityMiddleware: Middleware<DBCore> = {\n  stack: \"dbcore\",\n  level: 0,\n  create: (core) => {\n    const dbName = core.schema.name;\n    const FULL_RANGE = new RangeSet(core.MIN_KEY, core.MAX_KEY);\n\n    return {\n      ...core,\n      table: (tableName) => {\n        const table = core.table(tableName);\n        const { schema } = table;\n        const { primaryKey } = schema;\n        const { extractKey, outbound } = primaryKey;\n        const tableClone: DBCoreTable = {\n          ...table,\n          mutate: (req) => {\n            const trans = req.trans as DBCoreTransaction & {\n              mutatedParts?: ObservabilitySet;\n            };\n            const mutatedParts =\n              trans.mutatedParts || (trans.mutatedParts = {});\n            const getRangeSet = (indexName: string) => {\n              const part = `idb://${dbName}/${tableName}/${indexName}`;\n              return (mutatedParts[part] ||\n                (mutatedParts[part] = new RangeSet())) as RangeSet;\n            };\n            const pkRangeSet = getRangeSet(\"\");\n            const delsRangeSet = getRangeSet(\":dels\");\n\n            const { type } = req;\n            let [keys, newObjs] =\n              req.type === \"deleteRange\"\n                ? [req.range] // keys will be an DBCoreKeyRange object - transformed later on to a [from,to]-style range.\n                : req.type === \"delete\"\n                ? [req.keys] // keys known already here. newObjs will be undefined.\n                : req.values.length < 50\n                ? [[], req.values] // keys = empty array - will be resolved in mutate().then(...).\n                : []; // keys and newObjs will both be undefined - changeSpec will become true (changed for entire table)\n            const oldCache = req.trans[\"_cache\"];\n            return table.mutate(req).then((res) => {\n              // Add the mutated table and optionally keys to the mutatedTables set on the transaction.\n              // Used by subscribers to txcommit event and for Collection.prototype.subscribe().\n              if (isArray(keys)) {\n                // keys is an array - delete, add or put of less than 50 rows.\n                if (type !== \"delete\") keys = res.results;\n                // individual keys (add put or delete)\n                pkRangeSet.addKeys(keys);\n                // Only get oldObjs if they have been cached recently\n                // (This applies to Collection.modify() only, but also if updating/deleting hooks have subscribers)\n                const oldObjs = getFromTransactionCache(keys, oldCache);\n\n                // Supply detailed values per index for both old and new objects:\n                if (!oldObjs && type !== \"add\") {\n                  // delete or put and we don't know old values.\n                  // Indicate this in the \":dels\" part, for the sake of count() queries only!\n                  delsRangeSet.addKeys(keys);\n                }\n                if (oldObjs || newObjs) {\n                  // No matter if knowning oldObjs or not, track the indices if it's a put, add or delete.\n                  trackAffectedIndexes(getRangeSet, schema, oldObjs, newObjs);\n                }\n              } else if (keys) {\n                // As we can't know deleted index ranges, mark index-based subscriptions must trigger.\n                const range = { from: keys.lower, to: keys.upper };\n                delsRangeSet.add(range);\n                // deleteRange. keys is a DBCoreKeyRange objects. Transform it to [from,to]-style range.\n                pkRangeSet.add(range);\n              } else {\n                // Too many requests to record the details without slowing down write performance.\n                // Let's just record a generic large range on primary key, the virtual :dels index and\n                // all secondary indices:\n                pkRangeSet.add(FULL_RANGE);\n                delsRangeSet.add(FULL_RANGE);\n                schema.indexes.forEach(idx => getRangeSet(idx.name).add(FULL_RANGE));\n              }\n              return res;\n            });\n          },\n        };\n\n        const getRange: (req: any) => [DBCoreIndex, RangeSet] = ({\n          query: { index, range },\n        }:\n          | DBCoreQueryRequest\n          | DBCoreCountRequest\n          | DBCoreOpenCursorRequest) => [\n          index,\n          new RangeSet(range.lower ?? core.MIN_KEY, range.upper ?? core.MAX_KEY),\n        ];\n\n        const readSubscribers: {[method in\n          Exclude<keyof DBCoreTable, \"name\" | \"schema\" | \"mutate\">]: \n          (req: any) => [DBCoreIndex, RangeSet]\n        } = {\n          get: (req) => [primaryKey, new RangeSet(req.key)],\n          getMany: (req) => [primaryKey, new RangeSet().addKeys(req.keys)],\n          count: getRange,\n          query: getRange,\n          openCursor: getRange,\n        }\n\n        keys(readSubscribers).forEach(method => {\n          tableClone[method] = function (\n            req:\n              | DBCoreGetRequest\n              | DBCoreGetManyRequest\n              | DBCoreQueryRequest\n              | DBCoreCountRequest\n              | DBCoreOpenCursorRequest\n          ) {\n            const { subscr } = PSD;\n            if (subscr) {\n              // Current zone want's to track all queries so they can be subscribed to.\n              // (The query is executed within a \"liveQuery\" zone)\n              // Check whether the query applies to a certain set of ranges:\n              // Track what we should be observing:\n              const getRangeSet = (indexName: string) => {\n                const part = `idb://${dbName}/${tableName}/${indexName}`;\n                return (subscr[part] ||\n                  (subscr[part] = new RangeSet())) as RangeSet;\n              };\n              const pkRangeSet = getRangeSet(\"\");\n              const delsRangeSet = getRangeSet(\":dels\");\n              const [queriedIndex, queriedRanges] = readSubscribers[method](req);\n              // A generic rule here: queried ranges should always be subscribed to.\n              getRangeSet(queriedIndex.name || \"\").add(queriedRanges);\n              if (!queriedIndex.isPrimaryKey) {\n                // Only count(), query() and openCursor() operates on secondary indices.\n                // Since put(), delete() and deleteRange() mutations may happen without knowing oldObjs,\n                // the mutate() method will be missing what secondary indices that are being deleted from\n                // the subscribed range. We are working around this issue by recording all the resulting\n                // primary keys from the queries. This only works for those kinds of queries where we can\n                // derive the primary key from the result.\n                // In this block we are accomplishing this using various strategies depending on the properties\n                // of the query result.\n\n                if (method === \"count\") {\n                  // We've got a problem! Delete and put mutations happen without known the oldObjs.\n                  // Those mutation could change the count.\n                  // Solution: Dedicated \":dels\" url represends a subscription to all mutations without oldObjs\n                  // (specially triggered in the mutators put(), delete() and deleteRange() when they don't know oldObject)\n                  delsRangeSet.add(FULL_RANGE);\n                } else {\n                  // openCursor() or query()\n\n                  // Prepare a keysPromise in case the we're doing an IDBIndex.getAll() on a store with outbound keys.\n                  const keysPromise =\n                    method === \"query\" &&\n                    outbound &&\n                    (req as DBCoreQueryRequest).values &&\n                    table.query({\n                      ...(req as DBCoreQueryRequest),\n                      values: false,\n                    });\n\n                  return table[method].apply(this, arguments).then((res) => {\n                    if (method === \"query\") {\n                      if (outbound && (req as DBCoreQueryRequest).values) {\n                        // If keys are outbound, we can't use extractKey to map what keys to observe.\n                        // We've queried an index (like 'dateTime') on an outbound table\n                        // and retrieve a list of objects\n                        // from who we cannot know their primary keys.\n                        // \"Luckily\" though, we've prepared the keysPromise to assist us in exact this condition.\n                        return keysPromise.then(\n                          ({ result: resultingKeys }: DBCoreQueryResponse) => {\n                            pkRangeSet.addKeys(resultingKeys);\n                            return res;\n                          }\n                        );\n                      }\n                      // query() inbound values, keys or outbound keys. Secondary indexes only since\n                      // for primary keys we would only add results within the already registered range.\n                      const pKeys = (req as DBCoreQueryRequest).values\n                        ? (res as DBCoreQueryResponse).result.map(extractKey)\n                        : (res as DBCoreQueryResponse).result;\n                      if ((req as DBCoreQueryRequest).values) {\n                        // Subscribe to any mutation made on the returned keys,\n                        // so that we detect both deletions and updated properties.\n                        pkRangeSet.addKeys(pKeys);\n                      } else {\n                        // Subscribe only to mutations on the returned keys\n                        // in case the mutator was unable to know oldObjs.\n                        // If it has oldObj, the mutator won't put anything in \":dels\" because\n                        // it can more fine-grained put the exact removed and added index value in the correct\n                        // index range that we subscribe to in the queried range sets.\n                        // We don't load values so a change on a property outside our index will not\n                        // require us to re-execute the query.\n                        delsRangeSet.addKeys(pKeys);\n                      }\n                    } else if (method === \"openCursor\") {\n                      // Caller requests a cursor.\n                      // For the same reason as when method===\"query\", we only need to observe\n                      // those keys whose values are possibly used or rendered - which could\n                      // only happen on keys where they get the cursor's key, primaryKey or value.\n                      const cursor: DBCoreCursor | null = res;\n                      const wantValues = (req as DBCoreOpenCursorRequest).values;\n                      return (\n                        cursor &&\n                        Object.create(cursor, {\n                          key: {\n                            get() {\n                              delsRangeSet.addKey(cursor.primaryKey);\n                              return cursor.key;\n                            },\n                          },\n                          primaryKey: {\n                            get() {\n                              const pkey = cursor.primaryKey;\n                              delsRangeSet.addKey(pkey);\n                              return pkey;\n                            },\n                          },\n                          value: {\n                            get() {\n                              wantValues && pkRangeSet.addKey(cursor.primaryKey);\n                              return cursor.value;\n                            },\n                          },\n                        })\n                      );\n                    }\n                    return res;\n                  });\n                }\n              }\n            }\n            return table[method].apply(this, arguments);\n          };\n        });\n        return tableClone;\n      },\n    };\n  },\n};\n\nfunction trackAffectedIndexes(\n  getRangeSet: (index: string) => RangeSet,\n  schema: DBCoreTableSchema,\n  oldObjs: any[] | undefined,\n  newObjs: any[] | undefined\n) {\n  function addAffectedIndex(ix: DBCoreIndex) {\n    const rangeSet = getRangeSet(ix.name || \"\");\n    function extractKey(obj: any) {\n      return obj != null ? ix.extractKey(obj) : null;\n    }\n    const addKeyOrKeys = (key: any) => ix.multiEntry && isArray(key)\n      // multiEntry and the old property was an array - add each array entry to the rangeSet:\n      ? key.forEach(key => rangeSet.addKey(key))\n      // Not multiEntry or the old property was not an array - add each array entry to the rangeSet:\n      : rangeSet.addKey(key);\n\n    (oldObjs || newObjs).forEach((_, i) => {\n      const oldKey = oldObjs && extractKey(oldObjs[i]);\n      const newKey = newObjs && extractKey(newObjs[i]);\n      if (cmp(oldKey, newKey) !== 0) {\n        // The index has changed. Add both old and new value of the index.\n        if (oldKey != null) addKeyOrKeys(oldKey); // If oldKey is invalid key, addKey() will be a noop.\n        if (newKey != null) addKeyOrKeys(newKey); // If newKey is invalid key, addKey() will be a noop.\n      }\n    });\n  }\n  schema.indexes.forEach(addAffectedIndex);\n}\n", "// Import types from the public API\nimport { <PERSON><PERSON> as IDexie } from \"../../public/types/dexie\";\nimport { <PERSON>ieOptions, DexieConstructor } from \"../../public/types/dexie-constructor\";\nimport { DbEvents } from \"../../public/types/db-events\";\n//import { PromiseExtended, PromiseExtendedConstructor } from '../../public/types/promise-extended';\nimport { Table as ITable } from '../../public/types/table';\nimport { TableSchema } from \"../../public/types/table-schema\";\nimport { DbSchema } from '../../public/types/db-schema';\n\n// Internal imports\nimport { Table, TableConstructor, createTableConstructor } from \"../table\";\nimport { Collection, CollectionConstructor, createCollectionConstructor } from '../collection';\nimport { WhereClause } from '../where-clause/where-clause';\nimport { WhereClauseConstructor, createWhereClauseConstructor } from '../where-clause/where-clause-constructor';\nimport { Transaction } from '../transaction';\nimport { TransactionConstructor, createTransactionConstructor } from '../transaction/transaction-constructor';\nimport { Version } from \"../version/version\";\nimport { VersionConstructor, createVersionConstructor } from '../version/version-constructor';\n\n// Other imports...\nimport { DexieEventSet } from '../../public/types/dexie-event-set';\nimport { DexieExceptionClasses } from '../../public/types/errors';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\nimport { nop, promisableChain } from '../../functions/chaining-functions';\nimport Promise, { PSD } from '../../helpers/promise';\nimport { extend, override, keys, hasOwn } from '../../functions/utils';\nimport Events from '../../helpers/Events';\nimport { maxString, connections, READONLY, READWRITE } from '../../globals/constants';\nimport { getMaxKey } from '../../functions/quirks';\nimport { exceptions } from '../../errors';\nimport { lowerVersionFirst } from '../version/schema-helpers';\nimport { dexieOpen } from './dexie-open';\nimport { wrap } from '../../helpers/promise';\nimport { _onDatabaseDeleted } from '../../helpers/database-enumerator';\nimport { eventRejectHandler } from '../../functions/event-wrappers';\nimport { extractTransactionArgs, enterTransactionScope } from './transaction-helpers';\nimport { TransactionMode } from '../../public/types/transaction-mode';\nimport { rejection } from '../../helpers/promise';\nimport { usePSD } from '../../helpers/promise';\nimport { DBCore } from '../../public/types/dbcore';\nimport { Middleware, DexieStacks } from '../../public/types/middleware';\nimport { virtualIndexMiddleware } from '../../dbcore/virtual-index-middleware';\nimport { hooksMiddleware } from '../../hooks/hooks-middleware';\nimport { IndexableType } from '../../public';\nimport { observabilityMiddleware } from '../../live-query/observability-middleware';\nimport { cacheExistingValuesMiddleware } from '../../dbcore/cache-existing-values-middleware';\n\nexport interface DbReadyState {\n  dbOpenError: any;\n  isBeingOpened: boolean;\n  onReadyBeingFired: undefined | Function[];\n  openComplete: boolean;\n  dbReadyResolve: () => void;\n  dbReadyPromise: Promise<any>;\n  cancelOpen: (reason?: Error) => void;\n  openCanceller: Promise<any> & { _stackHolder?: Error };\n  autoSchema: boolean;\n  vcFired?: boolean;\n  PR1398_maxLoop?: number;\n}\n\nexport class Dexie implements IDexie {\n  _options: DexieOptions;\n  _state: DbReadyState;\n  _versions: Version[];\n  _storeNames: string[];\n  _deps: DexieDOMDependencies;\n  _allTables: { [name: string]: Table; };\n  _createTransaction: (this: Dexie, mode: IDBTransactionMode, storeNames: ArrayLike<string>, dbschema: { [tableName: string]: TableSchema; }, parentTransaction?: Transaction) => Transaction;\n  _dbSchema: { [tableName: string]: TableSchema; };\n  _hasGetAll?: boolean;\n  _maxKey: IndexableType;\n  _fireOnBlocked: (ev: Event) => void;\n  _middlewares: {[StackName in keyof DexieStacks]?: Middleware<DexieStacks[StackName]>[]} = {};\n  _vip?: boolean;\n  _novip?: Dexie;// db._novip is to escape to orig db from db.vip.\n  core: DBCore;\n\n  name: string;\n  verno: number = 0;\n  idbdb: IDBDatabase | null;\n  vip: Dexie;\n  on: DbEvents;\n\n  Table: TableConstructor;\n  WhereClause: WhereClauseConstructor;\n  Collection: CollectionConstructor;\n  Version: VersionConstructor;\n  Transaction: TransactionConstructor;\n\n  constructor(name: string, options?: DexieOptions) {\n    const deps = (Dexie as any as DexieConstructor).dependencies;\n    this._options = options = {\n      // Default Options\n      addons: (Dexie as any as DexieConstructor).addons, // Pick statically registered addons by default\n      autoOpen: true,                 // Don't require db.open() explicitely.\n      // Default DOM dependency implementations from static prop.\n      indexedDB: deps.indexedDB,      // Backend IndexedDB api. Default to browser env.\n      IDBKeyRange: deps.IDBKeyRange,  // Backend IDBKeyRange api. Default to browser env.\n      ...options\n    };\n    this._deps = {\n      indexedDB: options.indexedDB as IDBFactory,\n      IDBKeyRange: options.IDBKeyRange as typeof IDBKeyRange\n    };\n    const {\n      addons,\n    } = options;\n    this._dbSchema = {};\n    this._versions = [];\n    this._storeNames = [];\n    this._allTables = {};\n    this.idbdb = null;\n    this._novip = this;\n    const state: DbReadyState = {\n      dbOpenError: null,\n      isBeingOpened: false,\n      onReadyBeingFired: null,\n      openComplete: false,\n      dbReadyResolve: nop,\n      dbReadyPromise: null as Promise,\n      cancelOpen: nop,\n      openCanceller: null as Promise,\n      autoSchema: true,\n      PR1398_maxLoop: 3\n    };\n    state.dbReadyPromise = new Promise(resolve => {\n      state.dbReadyResolve = resolve;\n    });\n    state.openCanceller = new Promise((_, reject) => {\n      state.cancelOpen = reject;\n    });\n    this._state = state;\n    this.name = name;\n    this.on = Events(this, \"populate\", \"blocked\", \"versionchange\", \"close\", { ready: [promisableChain, nop] }) as DbEvents;\n    this.on.ready.subscribe = override(this.on.ready.subscribe, subscribe => {\n      return (subscriber, bSticky) => {\n        (Dexie as any as DexieConstructor).vip(() => {\n          const state = this._state;\n          if (state.openComplete) {\n            // Database already open. Call subscriber asap.\n            if (!state.dbOpenError) Promise.resolve().then(subscriber);\n            // bSticky: Also subscribe to future open sucesses (after close / reopen) \n            if (bSticky) subscribe(subscriber);\n          } else if (state.onReadyBeingFired) {\n            // db.on('ready') subscribers are currently being executed and have not yet resolved or rejected\n            state.onReadyBeingFired.push(subscriber);\n            if (bSticky) subscribe(subscriber);\n          } else {\n            // Database not yet open. Subscribe to it.\n            subscribe(subscriber);\n            // If bSticky is falsy, make sure to unsubscribe subscriber when fired once.\n            const db = this;\n            if (!bSticky) subscribe(function unsubscribe() {\n              db.on.ready.unsubscribe(subscriber);\n              db.on.ready.unsubscribe(unsubscribe);\n            });\n          }\n        });\n      }\n    });\n\n    // Create derived classes bound to this instance of Dexie:\n    this.Collection = createCollectionConstructor(this);\n    this.Table = createTableConstructor(this);\n    this.Transaction = createTransactionConstructor(this);\n    this.Version = createVersionConstructor(this);\n    this.WhereClause = createWhereClauseConstructor(this);\n\n    // Default subscribers to \"versionchange\" and \"blocked\".\n    // Can be overridden by custom handlers. If custom handlers return false, these default\n    // behaviours will be prevented.\n    this.on(\"versionchange\", ev => {\n      // Default behavior for versionchange event is to close database connection.\n      // Caller can override this behavior by doing db.on(\"versionchange\", function(){ return false; });\n      // Let's not block the other window from making it's delete() or open() call.\n      // NOTE! This event is never fired in IE,Edge or Safari.\n      if (ev.newVersion > 0)\n        console.warn(`Another connection wants to upgrade database '${this.name}'. Closing db now to resume the upgrade.`);\n      else\n        console.warn(`Another connection wants to delete database '${this.name}'. Closing db now to resume the delete request.`);\n      this.close();\n      // In many web applications, it would be recommended to force window.reload()\n      // when this event occurs. To do that, subscribe to the versionchange event\n      // and call window.location.reload(true) if ev.newVersion > 0 (not a deletion)\n      // The reason for this is that your current web app obviously has old schema code that needs\n      // to be updated. Another window got a newer version of the app and needs to upgrade DB but\n      // your window is blocking it unless we close it here.\n    });\n    this.on(\"blocked\", ev => {\n      if (!ev.newVersion || ev.newVersion < ev.oldVersion)\n        console.warn(`Dexie.delete('${this.name}') was blocked`);\n      else\n        console.warn(`Upgrade '${this.name}' blocked by other connection holding version ${ev.oldVersion / 10}`);\n    });\n\n    this._maxKey = getMaxKey(options.IDBKeyRange as typeof IDBKeyRange);\n\n    this._createTransaction = (\n      mode: IDBTransactionMode,\n      storeNames: string[],\n      dbschema: DbSchema,\n      parentTransaction?: Transaction) => new this.Transaction(mode, storeNames, dbschema, this._options.chromeTransactionDurability, parentTransaction);\n\n    this._fireOnBlocked = ev => {\n      this.on(\"blocked\").fire(ev);\n      // Workaround (not fully*) for missing \"versionchange\" event in IE,Edge and Safari:\n      connections\n        .filter(c => c.name === this.name && c !== this && !c._state.vcFired)\n        .map(c => c.on(\"versionchange\").fire(ev));\n    }\n\n    // Default middlewares:\n    this.use(virtualIndexMiddleware);\n    this.use(hooksMiddleware);\n    this.use(observabilityMiddleware);\n    this.use(cacheExistingValuesMiddleware);\n\n    this.vip = Object.create(this, {_vip: {value: true}}) as Dexie;\n\n    // Call each addon:\n    addons.forEach(addon => addon(this));\n  }\n\n  version(versionNumber: number): Version {\n    if (isNaN(versionNumber) || versionNumber < 0.1) throw new exceptions.Type(`Given version is not a positive number`);\n    versionNumber = Math.round(versionNumber * 10) / 10;\n    if (this.idbdb || this._state.isBeingOpened)\n      throw new exceptions.Schema(\"Cannot add version when database is open\");\n    this.verno = Math.max(this.verno, versionNumber);\n    const versions = this._versions;\n    var versionInstance = versions.filter(\n      v => v._cfg.version === versionNumber)[0];\n    if (versionInstance) return versionInstance;\n    versionInstance = new this.Version(versionNumber);\n    versions.push(versionInstance);\n    versions.sort(lowerVersionFirst);\n    versionInstance.stores({}); // Derive earlier schemas by default.\n    // Disable autoschema mode, as at least one version is specified.\n    this._state.autoSchema = false;\n    return versionInstance;\n  }\n\n  _whenReady<T>(fn: () => Promise<T>): Promise<T> {\n    return (this.idbdb && (this._state.openComplete || PSD.letThrough || this._vip)) ? fn() : new Promise<T>((resolve, reject) => {\n      if (this._state.openComplete) {\n        // idbdb is falsy but openComplete is true. Must have been an exception durin open.\n        // Don't wait for openComplete as it would lead to infinite loop.\n        return reject(new exceptions.DatabaseClosed(this._state.dbOpenError));\n      }\n      if (!this._state.isBeingOpened) {\n        if (!this._options.autoOpen) {\n          reject(new exceptions.DatabaseClosed());\n          return;\n        }\n        this.open().catch(nop); // Open in background. If if fails, it will be catched by the final promise anyway.\n      }\n      this._state.dbReadyPromise.then(resolve, reject);\n    }).then(fn);\n  }\n\n  use({stack, create, level, name}: Middleware<DBCore>): this {\n    if (name) this.unuse({stack, name}); // Be able to replace existing middleware.\n    const middlewares = this._middlewares[stack] || (this._middlewares[stack] = []);\n    middlewares.push({stack, create, level: level == null ? 10 : level, name});\n    middlewares.sort((a, b) => a.level - b.level);\n    // Todo update db.core and db.tables...core ? Or should be expect this to have effect\n    // only after next open()?\n    return this;\n  }\n\n  unuse({stack, create}: Middleware<{stack: keyof DexieStacks}>): this;\n  unuse({stack, name}: {stack: keyof DexieStacks, name: string}): this;\n  unuse({stack, name, create}: {stack: keyof DexieStacks, name?: string, create?: Function}) {\n    if (stack && this._middlewares[stack]) {\n      this._middlewares[stack] = this._middlewares[stack].filter(mw =>\n        create ? mw.create !== create : // Given middleware has a create method. Match that exactly.\n        name ? mw.name !== name : // Given middleware spec \n        false);\n    }\n    return this;\n  }\n\n  open() {\n    return dexieOpen(this);\n  }\n\n  _close(): void {\n    const state = this._state;\n    const idx = connections.indexOf(this);\n    if (idx >= 0) connections.splice(idx, 1);\n    if (this.idbdb) {\n      try { this.idbdb.close(); } catch (e) { }\n      this._novip.idbdb = null; // db._novip is because db can be an Object.create(origDb).\n    }    \n    // Reset dbReadyPromise promise:\n    state.dbReadyPromise = new Promise(resolve => {\n      state.dbReadyResolve = resolve;\n    });\n    state.openCanceller = new Promise((_, reject) => {\n      state.cancelOpen = reject;\n    });\n  }\n\n  close(): void {\n    this._close();\n    const state = this._state;\n    this._options.autoOpen = false;\n    state.dbOpenError = new exceptions.DatabaseClosed();\n    if (state.isBeingOpened)\n      state.cancelOpen(state.dbOpenError);\n  }\n\n  delete(): Promise<void> {\n    const hasArguments = arguments.length > 0;\n    const state = this._state;\n    return new Promise((resolve, reject) => {\n      const doDelete = () => {\n        this.close();\n        var req = this._deps.indexedDB.deleteDatabase(this.name);\n        req.onsuccess = wrap(() => {\n          _onDatabaseDeleted(this._deps, this.name);\n          resolve();\n        });\n        req.onerror = eventRejectHandler(reject);\n        req.onblocked = this._fireOnBlocked;\n      }\n\n      if (hasArguments) throw new exceptions.InvalidArgument(\"Arguments not allowed in db.delete()\");\n      if (state.isBeingOpened) {\n        state.dbReadyPromise.then(doDelete);\n      } else {\n        doDelete();\n      }\n    });\n  }\n\n  backendDB() {\n    return this.idbdb;\n  }\n\n  isOpen() {\n    return this.idbdb !== null;\n  }\n\n  hasBeenClosed() {\n    const dbOpenError = this._state.dbOpenError;\n    return dbOpenError && (dbOpenError.name === 'DatabaseClosed');\n  }\n\n  hasFailed() {\n    return this._state.dbOpenError !== null;\n  }\n\n  dynamicallyOpened() {\n    return this._state.autoSchema;\n  }\n\n  get tables () {\n    return keys(this._allTables).map(name => this._allTables[name]);\n  }\n\n  transaction(): Promise {\n    const args = extractTransactionArgs.apply(this, arguments);\n    return this._transaction.apply(this, args);\n  }\n\n  _transaction(mode: TransactionMode, tables: Array<ITable | string>, scopeFunc: Function) {\n    let parentTransaction = PSD.trans as Transaction | undefined;\n    // Check if parent transactions is bound to this db instance, and if caller wants to reuse it\n    if (!parentTransaction || parentTransaction.db !== this || mode.indexOf('!') !== -1) parentTransaction = null;\n    const onlyIfCompatible = mode.indexOf('?') !== -1;\n    mode = mode.replace('!', '').replace('?', '') as TransactionMode; // Ok. Will change arguments[0] as well but we wont touch arguments henceforth.\n    let idbMode: IDBTransactionMode,\n        storeNames;\n\n    try {\n        //\n        // Get storeNames from arguments. Either through given table instances, or through given table names.\n        //\n        storeNames = tables.map(table => {\n            var storeName = table instanceof this.Table ? table.name : table;\n            if (typeof storeName !== 'string') throw new TypeError(\"Invalid table argument to Dexie.transaction(). Only Table or String are allowed\");\n            return storeName;\n        });\n\n        //\n        // Resolve mode. Allow shortcuts \"r\" and \"rw\".\n        //\n        if (mode == \"r\" || mode === READONLY)\n          idbMode = READONLY;\n        else if (mode == \"rw\" || mode == READWRITE)\n          idbMode = READWRITE;\n        else\n            throw new exceptions.InvalidArgument(\"Invalid transaction mode: \" + mode);\n\n        if (parentTransaction) {\n            // Basic checks\n            if (parentTransaction.mode === READONLY && idbMode === READWRITE) {\n                if (onlyIfCompatible) {\n                    // Spawn new transaction instead.\n                    parentTransaction = null; \n                }\n                else throw new exceptions.SubTransaction(\"Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY\");\n            }\n            if (parentTransaction) {\n                storeNames.forEach(storeName => {\n                    if (parentTransaction && parentTransaction.storeNames.indexOf(storeName) === -1) {\n                        if (onlyIfCompatible) {\n                            // Spawn new transaction instead.\n                            parentTransaction = null; \n                        }\n                        else throw new exceptions.SubTransaction(\"Table \" + storeName +\n                            \" not included in parent transaction.\");\n                    }\n                });\n            }\n            if (onlyIfCompatible && parentTransaction && !parentTransaction.active) {\n                // '?' mode should not keep using an inactive transaction.\n                parentTransaction = null;\n            }\n        }\n    } catch (e) {\n        return parentTransaction ?\n            parentTransaction._promise(null, (_, reject) => {reject(e);}) :\n            rejection (e);\n    }\n    // If this is a sub-transaction, lock the parent and then launch the sub-transaction.\n    const enterTransaction = enterTransactionScope.bind(null, this, idbMode, storeNames, parentTransaction, scopeFunc);\n    return (parentTransaction ?\n        parentTransaction._promise(idbMode, enterTransaction, \"lock\") :\n        PSD.trans ?\n            // no parent transaction despite PSD.trans exists. Make sure also\n            // that the zone we create is not a sub-zone of current, because\n            // Promise.follow() should not wait for it if so.\n            usePSD(PSD.transless, ()=>this._whenReady(enterTransaction)) :\n            this._whenReady (enterTransaction));\n  }\n\n  table(tableName: string): Table;\n  table<T, TKey extends IndexableType=IndexableType>(tableName: string): ITable<T, TKey>;\n  table(tableName: string): Table {\n    if (!hasOwn(this._allTables, tableName)) {\n      throw new exceptions.InvalidTable(`Table ${tableName} does not exist`); }\n    return this._allTables[tableName];\n  }\n}\n", "import {\n  Observable as IObservable,\n  Observer,\n  Subscription,\n} from \"../../public/types/observable\";\n\nconst symbolObservable: typeof Symbol.observable =\n  typeof Symbol !== \"undefined\" && \"observable\" in Symbol\n    ? Symbol.observable\n    : \"@@observable\" as any;\n\nexport class Observable<T> implements IObservable<T> {\n  private _subscribe: (observer: Observer<T>) => Subscription;\n  hasValue?: ()=>boolean;\n  getValue?: ()=>T;\n\n  constructor(subscribe: (observer: Observer<T>) => Subscription) {\n    this._subscribe = subscribe;\n  }\n\n  subscribe(\n    onNext?: ((value: T) => void) | null,\n    onError?: ((error: any) => void) | null,\n    onComplete?: (() => void) | null\n  ): Subscription;\n  subscribe(observer?: Observer<T> | null): Subscription;\n  subscribe(x?: any, error?: any, complete?: any): Subscription {\n    return this._subscribe(\n      !x || typeof x === \"function\" ? { next: x, error, complete } : x\n    );\n  }\n\n  [symbolObservable]() {\n    return this;\n  }\n}\n", "import { deepClone, keys } from \"../functions/utils\";\nimport { mergeRanges, RangeSet } from \"../helpers/rangeset\";\nimport { ObservabilitySet } from \"../public/types/db-events\";\n\nexport function extendObservabilitySet(\n  target: ObservabilitySet,\n  newSet: ObservabilitySet\n): ObservabilitySet {\n  keys(newSet).forEach(part => {\n    const rangeSet = target[part] || (target[part] = new RangeSet());\n    mergeRanges(rangeSet, newSet[part]);\n  });\n  return target;\n}\n", "import { isAsyncFunction, keys } from \"../functions/utils\";\nimport { globalEvents, DEXIE_STORAGE_MUTATED_EVENT_NAME } from \"../globals/global-events\";\nimport {\n  decrementExpectedAwaits,\n  incrementExpectedAwaits,\n  newScope,\n  PSD,\n  usePSD,\n} from \"../helpers/promise\";\nimport { ObservabilitySet } from \"../public/types/db-events\";\nimport {\n  Observable as IObservable,\n  Subscription,\n} from \"../public/types/observable\";\nimport { Observable } from \"../classes/observable/observable\";\nimport { extendObservabilitySet } from \"./extend-observability-set\";\nimport { rangesOverlap } from \"../helpers/rangeset\";\n\nexport function liveQuery<T>(querier: () => T | Promise<T>): IObservable<T> {\n  let hasValue = false;\n  let currentValue: T = undefined as any;\n  const observable = new Observable<T>((observer) => {\n    const scopeFuncIsAsync = isAsyncFunction(querier);\n    function execute(subscr: ObservabilitySet) {\n      if (scopeFuncIsAsync) {\n        incrementExpectedAwaits();\n      }\n      const exec = () => newScope(querier, { subscr, trans: null });\n      const rv = PSD.trans\n        ? // Ignore current transaction if active when calling subscribe().\n          usePSD(PSD.transless, exec)\n        : exec();\n      if (scopeFuncIsAsync) {\n        (rv as Promise<any>).then(\n          decrementExpectedAwaits,\n          decrementExpectedAwaits\n        );\n      }\n      return rv;\n    }\n\n    let closed = false;\n\n    let accumMuts: ObservabilitySet = {};\n    let currentObs: ObservabilitySet = {};\n\n    const subscription: Subscription = {\n      get closed() {\n        return closed;\n      },\n      unsubscribe: () => {\n        closed = true;\n        globalEvents.storagemutated.unsubscribe(mutationListener);\n      },\n    };\n\n    observer.start && observer.start(subscription); // https://github.com/tc39/proposal-observable\n\n    let querying = false,\n      startedListening = false;\n\n    function shouldNotify() {\n      return keys(currentObs).some(\n        (key) =>\n          accumMuts[key] && rangesOverlap(accumMuts[key], currentObs[key])\n      );\n    }\n\n    const mutationListener = (parts: ObservabilitySet) => {\n      extendObservabilitySet(accumMuts, parts);\n      if (shouldNotify()) {\n        doQuery();\n      }\n    };\n\n    const doQuery = () => {\n      if (querying || closed) return;\n      accumMuts = {};\n      const subscr: ObservabilitySet = {};\n      const ret = execute(subscr);\n      if (!startedListening) {\n        globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, mutationListener);\n        startedListening = true;\n      }\n      querying = true;\n      Promise.resolve(ret).then(\n        (result) => {\n          hasValue = true;\n          currentValue = result;\n          querying = false;\n          if (closed) return;\n          if (shouldNotify()) {\n            // Mutations has happened while we were querying. Redo query.\n            doQuery();\n          } else {\n            accumMuts = {};\n            // Update what we are subscribing for based on this last run:\n            currentObs = subscr;\n            observer.next && observer.next(result);\n          }\n        },\n        (err) => {\n          querying = false;\n          hasValue = false;\n          observer.error && observer.error(err);\n          subscription.unsubscribe();\n        }\n      );\n    };\n\n    doQuery();\n    return subscription;\n  });\n  observable.hasValue = () => hasValue;\n  observable.getValue = () => currentValue;\n  return observable;\n}\n", "import { _global } from '../../globals/global';\nimport { DexieDOMDependencies } from '../../public/types/dexie-dom-dependencies';\n\nexport let domDeps: DexieDOMDependencies\n\ntry {\n  domDeps = {\n    // Required:\n    indexedDB: _global.indexedDB || _global.mozIndexedDB || _global.webkitIndexedDB || _global.msIndexedDB,\n    IDBKeyRange: _global.IDBKeyRange || _global.webkitIDBKeyRange\n  };\n} catch (e) {\n  domDeps = { indexedDB: null, IDBKeyRange: null };\n}\n", "import { <PERSON><PERSON> as _<PERSON>ie } from './dexie';\nimport { _global } from '../../globals/global';\nimport { props, derive, extend, override, getBy<PERSON>eyPath, setByKeyPath, delByKeyPath, shallowClone, deepClone, asap } from '../../functions/utils';\nimport { getObjectDiff } from \"../../functions/get-object-diff\";\nimport { fullNameExceptions } from '../../errors';\nimport { DexieConstructor } from '../../public/types/dexie-constructor';\nimport { getDatabaseNames } from '../../helpers/database-enumerator';\nimport { PSD } from '../../helpers/promise';\nimport { usePSD } from '../../helpers/promise';\nimport { newScope } from '../../helpers/promise';\nimport { rejection } from '../../helpers/promise';\nimport { awaitIterator } from '../../helpers/yield-support';\nimport Promise from '../../helpers/promise';\nimport * as Debug from '../../helpers/debug';\nimport { dexieStackFrameFilter, minKey, connections, DEXIE_VERSION } from '../../globals/constants';\nimport Events from '../../helpers/Events';\nimport { exceptions } from '../../errors';\nimport { errnames } from '../../errors';\nimport { getMaxKey } from '../../functions/quirks';\nimport { vip } from './vip';\nimport { globalEvents } from '../../globals/global-events';\nimport { liveQuery } from '../../live-query/live-query';\nimport { extendObservabilitySet } from '../../live-query/extend-observability-set';\nimport { domDeps } from './dexie-dom-dependencies';\nimport { cmp } from '../../functions/cmp';\n\n/* (Dexie) is an instance of DexieConstructor, as defined in public/types/dexie-constructor.d.ts\n*  (new Dexie()) is an instance of Dexie, as defined in public/types/dexie.d.ts\n* \n* Why we're doing this?\n\n* Because we've choosen to define the public Dexie API using a DexieConstructor interface\n* rather than declaring a class. On that interface, all static props are defined.\n* In practice, class Dexie's constructor implements DexieConstructor and all member props\n* are defined in interface Dexie. We could say, it's a typescript limitation of not being\n* able to define a static interface that forces us to do the cast below.\n*/\nconst Dexie = _Dexie as any as DexieConstructor;\n\n//\n// Set all static methods and properties onto Dexie:\n// \nprops(Dexie, {\n\n  // Dexie.BulkError = class BulkError {...};\n  // Dexie.XXXError = class XXXError {...};\n  ...fullNameExceptions,\n\n  //\n  // Static delete() method.\n  //\n  delete(databaseName: string) {\n    const db = new Dexie(databaseName, {addons: []});\n    return db.delete();\n  },\n\n  //\n  // Static exists() method.\n  //\n  exists(name: string) {\n    return new Dexie(name, { addons: [] }).open().then(db => {\n      db.close();\n      return true;\n    }).catch('NoSuchDatabaseError', () => false);\n  },\n\n  //\n  // Static method for retrieving a list of all existing databases at current host.\n  //\n  getDatabaseNames(cb) {\n    try {\n      return getDatabaseNames(Dexie.dependencies).then(cb);\n    } catch {\n      return rejection(new exceptions.MissingAPI());\n    }\n  },\n\n  /** @deprecated */\n  defineClass() {\n    function Class(content) {\n      extend(this, content);\n    }\n    return Class;\n  },\n\n  ignoreTransaction(scopeFunc) {\n    // In case caller is within a transaction but needs to create a separate transaction.\n    // Example of usage:\n    //\n    // Let's say we have a logger function in our app. Other application-logic should be unaware of the\n    // logger function and not need to include the 'logentries' table in all transaction it performs.\n    // The logging should always be done in a separate transaction and not be dependant on the current\n    // running transaction context. Then you could use Dexie.ignoreTransaction() to run code that starts a new transaction.\n    //\n    //     Dexie.ignoreTransaction(function() {\n    //         db.logentries.add(newLogEntry);\n    //     });\n    //\n    // Unless using Dexie.ignoreTransaction(), the above example would try to reuse the current transaction\n    // in current Promise-scope.\n    //\n    // An alternative to Dexie.ignoreTransaction() would be setImmediate() or setTimeout(). The reason we still provide an\n    // API for this because\n    //  1) The intention of writing the statement could be unclear if using setImmediate() or setTimeout().\n    //  2) setTimeout() would wait unnescessary until firing. This is however not the case with setImmediate().\n    //  3) setImmediate() is not supported in the ES standard.\n    //  4) You might want to keep other PSD state that was set in a parent PSD, such as PSD.letThrough.\n    return PSD.trans ?\n      usePSD(PSD.transless, scopeFunc) : // Use the closest parent that was non-transactional.\n      scopeFunc(); // No need to change scope because there is no ongoing transaction.\n  },\n\n  vip,\n\n  async: function (generatorFn: Function) {\n    return function () {\n      try {\n        var rv = awaitIterator(generatorFn.apply(this, arguments));\n        if (!rv || typeof rv.then !== 'function')\n          return Promise.resolve(rv);\n        return rv;\n      } catch (e) {\n        return rejection(e);\n      }\n    };\n  },\n\n  spawn: function (generatorFn, args, thiz) {\n    try {\n      var rv = awaitIterator(generatorFn.apply(thiz, args || []));\n      if (!rv || typeof rv.then !== 'function')\n        return Promise.resolve(rv);\n      return rv;\n    } catch (e) {\n      return rejection(e);\n    }\n  },\n\n  // Dexie.currentTransaction property\n  currentTransaction: {\n    get: () => PSD.trans || null\n  },\n\n  waitFor: function (promiseOrFunction, optionalTimeout) {\n    // If a function is provided, invoke it and pass the returning value to Transaction.waitFor()\n    const promise = Promise.resolve(\n      typeof promiseOrFunction === 'function' ?\n        Dexie.ignoreTransaction(promiseOrFunction) :\n        promiseOrFunction)\n      .timeout(optionalTimeout || 60000); // Default the timeout to one minute. Caller may specify Infinity if required.       \n\n    // Run given promise on current transaction. If no current transaction, just return a Dexie promise based\n    // on given value.\n    return PSD.trans ?\n      PSD.trans.waitFor(promise) :\n      promise;\n  },\n\n  // Export our Promise implementation since it can be handy as a standalone Promise implementation\n  Promise: Promise,\n\n  // Dexie.debug proptery:\n  // Dexie.debug = false\n  // Dexie.debug = true\n  // Dexie.debug = \"dexie\" - don't hide dexie's stack frames.\n  debug: {\n    get: () => Debug.debug,\n    set: value => {\n      Debug.setDebug(value, value === 'dexie' ? () => true : dexieStackFrameFilter);\n    }\n  },\n\n  // Export our derive/extend/override methodology\n  derive: derive, // Deprecate?\n  extend: extend, // Deprecate?\n  props: props,\n  override: override, // Deprecate?\n  // Export our Events() function - can be handy as a toolkit\n  Events: Events,\n  on: globalEvents,\n  liveQuery,\n  extendObservabilitySet,\n  // Utilities\n  getByKeyPath: getByKeyPath,\n  setByKeyPath: setByKeyPath,\n  delByKeyPath: delByKeyPath,\n  shallowClone: shallowClone,\n  deepClone: deepClone,\n  getObjectDiff: getObjectDiff,\n  cmp,\n  asap: asap,\n  //maxKey: new Dexie('',{addons:[]})._maxKey,\n  minKey: minKey,\n  // Addon registry\n  addons: [],\n  // Global DB connection list\n  connections: connections,\n\n  //MultiModifyError: exceptions.Modify, // Obsolete!\n  errnames: errnames,\n\n  // Export other static classes\n  //IndexSpec: IndexSpec, Obsolete!\n  //TableSchema: TableSchema, Obsolete!\n\n  //\n  // Dependencies\n  //\n  // These will automatically work in browsers with indexedDB support, or where an indexedDB polyfill has been included.\n  //\n  // In node.js, however, these properties must be set \"manually\" before instansiating a new Dexie().\n  // For node.js, you need to require indexeddb-js or similar and then set these deps.\n  //\n  dependencies: domDeps,\n\n  // API Version Number: Type Number, make sure to always set a version number that can be comparable correctly. Example: 0.9, 0.91, 0.92, 1.0, 1.01, 1.1, 1.2, 1.21, etc.\n  semVer: DEXIE_VERSION,\n  version: DEXIE_VERSION.split('.')\n    .map(n => parseInt(n))\n    .reduce((p, c, i) => p + (c / Math.pow(10, i * 2))),\n\n  // https://github.com/dfahlander/Dexie.js/issues/186\n  // typescript compiler tsc in mode ts-->es5 & commonJS, will expect require() to return\n  // x.default. Workaround: Set Dexie.default = Dexie.\n  // default: Dexie, // Commented because solved in index-umd.ts instead.\n  // Make it possible to import {Dexie} (non-default import)\n  // Reason 1: May switch to that in future.\n  // Reason 2: We declare it both default and named exported in d.ts to make it possible\n  // to let addons extend the Dexie interface with Typescript 2.1 (works only when explicitely\n  // exporting the symbol, not just default exporting)\n  // Dexie: Dexie // Commented because solved in index-umd.ts instead.\n});\n\nDexie.maxKey = getMaxKey(Dexie.dependencies.IDBKeyRange);\n", "import { isIEOrEdge } from '../globals/constants';\nimport { globalEvents, DEXIE_STORAGE_MUTATED_EVENT_NAME, STORAGE_MUTATED_DOM_EVENT_NAME } from '../globals/global-events';\nimport { ObservabilitySet } from \"../public/types/db-events\";\n\nif (typeof dispatchEvent !== 'undefined' && typeof addEventListener !== 'undefined') {\n  globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, updatedParts => {\n    if (!propagatingLocally) {\n      let event: CustomEvent<ObservabilitySet>;\n      if (isIEOrEdge) {\n        event = document.createEvent('CustomEvent');\n        event.initCustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, true, true, updatedParts);\n      } else {\n        event = new CustomEvent(STORAGE_MUTATED_DOM_EVENT_NAME, {\n          detail: updatedParts\n        });\n      }\n      propagatingLocally = true;\n      dispatchEvent(event);\n      propagatingLocally = false;\n    }\n  });\n  addEventListener(STORAGE_MUTATED_DOM_EVENT_NAME, ({detail}: CustomEvent<ObservabilitySet>) => {\n    if (!propagatingLocally) {\n      propagateLocally(detail);\n    }\n  });\n}\n\nexport function propagateLocally(updateParts: ObservabilitySet) {\n  let wasMe = propagatingLocally;\n  try {\n    propagatingLocally = true;\n    globalEvents.storagemutated.fire(updateParts);\n  } finally {\n    propagatingLocally = wasMe;\n  }\n}\n\nexport let propagatingLocally = false;\n", "import {\n  globalEvents,\n  STORAGE_MUTATED_DOM_EVENT_NAME,\n  DEXIE_STORAGE_MUTATED_EVENT_NAME,\n} from '../globals/global-events';\nimport { propagateLocally, propagatingLocally } from './propagate-locally';\n\nif (typeof BroadcastChannel !== 'undefined') {\n  const bc = new BroadcastChannel(STORAGE_MUTATED_DOM_EVENT_NAME);\n\n  /**\n     * The Node.js BroadcastChannel will prevent the node process from exiting\n     * if the BroadcastChannel is not closed.\n     * Therefore we have to call unref() which allows the process to finish\n     * properly even when the BroadcastChannel is never closed.\n     * @link https://nodejs.org/api/worker_threads.html#broadcastchannelunref\n     * @link https://github.com/dexie/Dexie.js/pull/1576\n     */\n  if (typeof (bc as any).unref === 'function') {\n    (bc as any).unref();\n  } \n   \n  //\n  // Propagate local changes to remote tabs, windows and workers via BroadcastChannel\n  //\n  globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, (changedParts) => {\n    if (!propagatingLocally) {\n      bc.postMessage(changedParts);\n    }\n  });\n\n  //\n  // Propagate remote changes locally via storage event:\n  //\n  bc.onmessage = (ev) => {\n    if (ev.data) propagateLocally(ev.data);\n  };\n} else if (typeof self !== 'undefined' && typeof navigator !== 'undefined') {\n  // DOM verified - when typeof self !== \"undefined\", we are a window or worker. Not a Node process.\n\n  //\n  // Propagate local changes to remote tabs/windows via storage event and service worker\n  // via messages. We have this code here because of https://bugs.webkit.org/show_bug.cgi?id=161472.\n  //\n  globalEvents(DEXIE_STORAGE_MUTATED_EVENT_NAME, (changedParts) => {\n    try {\n      if (!propagatingLocally) {\n        if (typeof localStorage !== 'undefined') {\n          // We're a browsing window or tab. Propagate to other windows/tabs via storage event:\n          localStorage.setItem(\n            STORAGE_MUTATED_DOM_EVENT_NAME,\n            JSON.stringify({\n              trig: Math.random(),\n              changedParts,\n            })\n          );\n        }\n        if (typeof self['clients'] === 'object') {\n          // We're a service worker. Propagate to our browser clients.\n          [...self['clients'].matchAll({ includeUncontrolled: true })].forEach(\n            (client) =>\n              client.postMessage({\n                type: STORAGE_MUTATED_DOM_EVENT_NAME,\n                changedParts,\n              })\n          );\n        }\n      }\n    } catch {}\n  });\n\n  //\n  // Propagate remote changes locally via storage event:\n  //\n  if (typeof addEventListener !== 'undefined') {\n      addEventListener('storage', (ev: StorageEvent) => {\n      if (ev.key === STORAGE_MUTATED_DOM_EVENT_NAME) {\n        const data = JSON.parse(ev.newValue);\n        if (data) propagateLocally(data.changedParts);\n      }\n    });\n  }\n\n  //\n  // Propagate messages from service worker\n  //\n  const swContainer = self.document && navigator.serviceWorker; // self.document is to verify we're not the SW ourself\n  if (swContainer) {\n    // We're a browser window and want to propagate message from the SW:\n    swContainer.addEventListener('message', propagateMessageLocally);\n  }\n}\n\nfunction propagateMessageLocally({ data }: MessageEvent) {\n  if (data && data.type === STORAGE_MUTATED_DOM_EVENT_NAME) {\n    propagateLocally(data.changedParts);\n  }\n}\n", "import { <PERSON><PERSON> } from './classes/dexie';\nimport { DexieConstructor } from './public/types/dexie-constructor';\nimport { DexiePromise } from './helpers/promise';\nimport { mapError } from './errors';\nimport * as Debug from './helpers/debug';\nimport { dexieStackFrameFilter } from './globals/constants';\n\n// Generate all static properties such as Dexie.maxKey etc\n// (implement interface DexieConstructor):\nimport './classes/dexie/dexie-static-props';\nimport './live-query/enable-broadcast';\nimport { liveQuery } from './live-query/live-query';\n\n// Set rejectionMapper of DexiePromise so that it generally tries to map\n// DOMErrors and DOMExceptions to a DexieError instance with same name but with\n// async stack support and with a prototypal inheritance from DexieError and Error.\n// of Map DOMErrors and DOMExceptions to corresponding Dexie errors.\nDexiePromise.rejectionMapper = mapError;\n\n// Let the async stack filter focus on app code and filter away frames from dexie.min.js:\nDebug.setDebug(Debug.debug, dexieStackFrameFilter);\n\nexport { RangeSet, mergeRanges, rangesOverlap } from \"./helpers/rangeset\";\nexport { Dexie, liveQuery }; // Comply with public/index.d.ts.\nexport default Dexie;\n"], "mappings": ";;;AACO,IAAM,UACT,OAAO,eAAe,cAAc,aACpC,OAAO,SAAS,cAAc,OAC9B,OAAO,WAAW,cAAc,SAChC;ACJG,IAAM,OAAO,OAAO;AACpB,IAAM,UAAU,MAAM;AAC7B,IAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,SAAQ;AAInD,UAAQ,UAAU;;SAIN,OAA0C,KAAQ,WAAY;AAC1E,MAAI,OAAO,cAAc;AAAU,WAAO;AAC1C,OAAK,SAAS,EAAE,QAAQ,SAAU,KAAG;AACjC,QAAI,GAAG,IAAI,UAAU,GAAG;GAC3B;AACD,SAAO;AACX;AAEO,IAAM,WAAW,OAAO;AACxB,IAAM,UAAU,CAAA,EAAG;SACV,OAAO,KAAK,MAAI;AAC5B,SAAO,QAAQ,KAAK,KAAK,IAAI;AACjC;SAEgB,MAAO,OAAO,WAAS;AACnC,MAAI,OAAO,cAAc;AAAY,gBAAY,UAAU,SAAS,KAAK,CAAC;AAC1E,GAAC,OAAO,YAAY,cAAc,OAAO,QAAQ,SAAS,SAAS,EAAE,QAAQ,SAAG;AAC5E,YAAQ,OAAO,KAAK,UAAU,GAAG,CAAC;GACrC;AACL;AAEO,IAAM,iBAAiB,OAAO;SAErB,QAAQ,KAAK,MAAM,kBAAkB,SAAQ;AACzD,iBAAe,KAAK,MAAM,OAAO,oBAAoB,OAAO,kBAAkB,KAAK,KAAK,OAAO,iBAAiB,QAAQ,aACpH,EAAC,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,KAAK,cAAc,KAAI,IACzE,EAAC,OAAO,kBAAkB,cAAc,MAAM,UAAU,KAAI,GAAG,OAAO,CAAC;AAC/E;SAEgB,OAAO,OAAK;AACxB,SAAO;IACH,MAAM,SAAU,QAAM;AAClB,YAAM,YAAY,OAAO,OAAO,OAAO,SAAS;AAChD,cAAQ,MAAM,WAAW,eAAe,KAAK;AAC7C,aAAO;QACH,QAAQ,MAAM,KAAK,MAAM,MAAM,SAAS;;;;AAIxD;AAEO,IAAM,2BAA2B,OAAO;SAE/B,sBAAsB,KAAK,MAAI;AAC3C,QAAM,KAAK,yBAAyB,KAAK,IAAI;AAC7C,MAAI;AACJ,SAAO,OAAO,QAAQ,SAAS,GAAG,MAAM,sBAAuB,OAAO,IAAI;AAC9E;AAEA,IAAM,SAAS,CAAA,EAAG;SACF,MAAM,MAAM,OAAQ,KAAI;AACpC,SAAO,OAAO,KAAK,MAAM,OAAO,GAAG;AACvC;SAEgB,SAAS,UAAU,kBAAgB;AAC/C,SAAO,iBAAiB,QAAQ;AACpC;SAEgB,OAAQ,GAAC;AACrB,MAAI,CAAC;AAAG,UAAM,IAAI,MAAM,kBAAkB;AAC9C;SAEgBA,OAAK,IAAE;AAEnB,MAAI,QAAQ;AAAc,iBAAa,EAAE;;AAAQ,eAAW,IAAI,CAAC;AACrE;SAWgB,cAAoB,OAAY,WAA0C;AACtF,SAAO,MAAM,OAAO,CAAC,QAAQ,MAAM,MAAC;AAChC,QAAI,eAAe,UAAU,MAAM,CAAC;AACpC,QAAI;AAAc,aAAO,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC;AAC1D,WAAO;KACR,CAAA,CAAE;AACT;SAYgB,SAAS,IAA4B,SAAS,MAAK;AAC/D,MAAI;AACA,OAAG,MAAM,MAAM,IAAI;WACd,IAAI;AACT,eAAW,QAAQ,EAAE;;AAE7B;SAEgB,aAAa,KAAK,SAAO;AAErC,MAAI,OAAO,YAAY,YAAY,OAAO,KAAK,OAAO;AAAG,WAAO,IAAI,OAAO;AAC3E,MAAI,CAAC;AAAS,WAAO;AACrB,MAAI,OAAO,YAAY,UAAU;AAC7B,QAAI,KAAK,CAAA;AACT,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,UAAI,MAAM,aAAa,KAAK,QAAQ,CAAC,CAAC;AACtC,SAAG,KAAK,GAAG;;AAEf,WAAO;;AAEX,MAAI,SAAS,QAAQ,QAAQ,GAAG;AAChC,MAAI,WAAW,IAAI;AACf,QAAI,WAAW,IAAI,QAAQ,OAAO,GAAG,MAAM,CAAC;AAC5C,WAAO,YAAY,OAAO,SAAY,aAAa,UAAU,QAAQ,OAAO,SAAS,CAAC,CAAC;;AAE3F,SAAO;AACX;SAEgB,aAAa,KAAK,SAAS,OAAK;AAC5C,MAAI,CAAC,OAAO,YAAY;AAAW;AACnC,MAAI,cAAc,UAAU,OAAO,SAAS,GAAG;AAAG;AAClD,MAAI,OAAO,YAAY,YAAY,YAAY,SAAS;AACpD,WAAO,OAAO,UAAU,YAAY,YAAY,KAAK;AACrD,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,mBAAa,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC;;SAEvC;AACH,QAAI,SAAS,QAAQ,QAAQ,GAAG;AAChC,QAAI,WAAW,IAAI;AACf,UAAI,iBAAiB,QAAQ,OAAO,GAAG,MAAM;AAC7C,UAAI,mBAAmB,QAAQ,OAAO,SAAS,CAAC;AAChD,UAAI,qBAAqB;AACrB,YAAI,UAAU,QAAW;AACrB,cAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,SAAS,cAAc,CAAC;AAAG,gBAAI,OAAO,gBAAgB,CAAC;;AAC7E,mBAAO,IAAI,cAAc;;AAC3B,cAAI,cAAc,IAAI;WAC5B;AACD,YAAI,WAAW,IAAI,cAAc;AACjC,YAAI,CAAC,YAAY,CAAC,OAAO,KAAK,cAAc;AAAG,qBAAY,IAAI,cAAc,IAAI,CAAA;AACjF,qBAAa,UAAU,kBAAkB,KAAK;;WAE/C;AACH,UAAI,UAAU,QAAW;AACrB,YAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,SAAS,OAAO,CAAC;AAAG,cAAI,OAAO,SAAS,CAAC;;AAC/D,iBAAO,IAAI,OAAO;;AACpB,YAAI,OAAO,IAAI;;;AAGlC;SAEgB,aAAa,KAAK,SAAO;AACrC,MAAI,OAAO,YAAY;AACnB,iBAAa,KAAK,SAAS,MAAS;WAC/B,YAAY;AACjB,KAAA,EAAG,IAAI,KAAK,SAAS,SAAS,IAAE;AAC5B,mBAAa,KAAK,IAAI,MAAS;KAClC;AACT;SAEgB,aAAa,KAAG;AAC5B,MAAI,KAAK,CAAA;AACT,WAAS,KAAK,KAAK;AACf,QAAI,OAAO,KAAK,CAAC;AAAG,SAAG,CAAC,IAAI,IAAI,CAAC;;AAErC,SAAO;AACX;AAEA,IAAM,SAAS,CAAA,EAAG;SACF,QAAY,GAAc;AACtC,SAAO,OAAO,MAAM,CAAA,GAAI,CAAC;AAC7B;AAGA,IAAM,qBACF,iNACC,MAAM,GAAG,EAAE,OACR,QAAQ,CAAC,GAAE,IAAG,IAAG,EAAE,EAAE,IAAI,SAAK,CAAC,OAAM,QAAO,OAAO,EAAE,IAAI,OAAG,IAAE,MAAI,OAAO,CAAC,CAAC,CAAC,EAC9E,OAAO,OAAG,QAAQ,CAAC,CAAC;AAC1B,IAAM,iBAAiB,mBAAmB,IAAI,OAAG,QAAQ,CAAC,CAAC;AACvB,cAAc,oBAAoB,OAAG,CAAC,GAAE,IAAI,CAAC;AAEjF,IAAI,eAAwC;SAC5B,UAAa,KAAM;AAC/B,iBAAe,OAAO,YAAY,eAAe,oBAAI,QAAO;AAC5D,QAAM,KAAK,eAAe,GAAG;AAC7B,iBAAe;AACf,SAAO;AACX;AAEA,SAAS,eAAkB,KAAM;AAC7B,MAAI,CAAC,OAAO,OAAO,QAAQ;AAAU,WAAO;AAC5C,MAAI,KAAK,gBAAgB,aAAa,IAAI,GAAG;AAC7C,MAAI;AAAI,WAAO;AACf,MAAI,QAAQ,GAAG,GAAG;AACd,SAAK,CAAA;AACL,oBAAgB,aAAa,IAAI,KAAK,EAAE;AACxC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,EAAE,GAAG;AACxC,SAAG,KAAK,eAAe,IAAI,CAAC,CAAC,CAAC;;aAE3B,eAAe,QAAQ,IAAI,WAAW,KAAK,GAAG;AACrD,SAAK;SACF;AACH,UAAM,QAAQ,SAAS,GAAG;AAC1B,SAAK,UAAU,OAAO,YAAY,CAAA,IAAK,OAAO,OAAO,KAAK;AAC1D,oBAAgB,aAAa,IAAI,KAAK,EAAE;AACxC,aAAS,QAAQ,KAAK;AAClB,UAAI,OAAO,KAAK,IAAI,GAAG;AACnB,WAAG,IAAI,IAAI,eAAe,IAAI,IAAI,CAAC;;;;AAI/C,SAAO;AACX;AAEA,IAAM,EAAC,SAAQ,IAAI,CAAA;SACH,YAAY,GAAS;AACjC,SAAO,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACvC;AAGO,IAAM,iBAAiB,OAAO,WAAW,cAC5C,OAAO,WACP;AACG,IAAM,gBAAgB,OAAO,mBAAmB,WAAW,SAAS,GAAC;AACxE,MAAI;AACJ,SAAO,KAAK,SAAS,IAAI,EAAE,cAAc,MAAM,EAAE,MAAM,CAAC;AAC5D,IAAI,WAAA;AAAc,SAAO;AAAK;AAKvB,IAAM,gBAAgB,CAAA;SASb,WAAY,WAAS;AACjC,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,UAAU,WAAW,GAAG;AACxB,QAAI,QAAQ,SAAS;AAAG,aAAO,UAAU,MAAK;AAC9C,QAAI,SAAS,iBAAiB,OAAO,cAAc;AAAU,aAAO,CAAC,SAAS;AAC9E,QAAK,KAAK,cAAc,SAAS,GAAI;AACjC,UAAI,CAAA;AACJ,aAAQ,IAAI,GAAG,KAAI,GAAK,CAAC,EAAE;AAAM,UAAE,KAAK,EAAE,KAAK;AAC/C,aAAO;;AAEX,QAAI,aAAa;AAAM,aAAO,CAAC,SAAS;AACxC,QAAI,UAAU;AACd,QAAI,OAAO,MAAM,UAAU;AACvB,UAAI,IAAI,MAAM,CAAC;AACf,aAAO;AAAK,UAAE,CAAC,IAAI,UAAU,CAAC;AAC9B,aAAO;;AAEX,WAAO,CAAC,SAAS;;AAErB,MAAI,UAAU;AACd,MAAI,IAAI,MAAM,CAAC;AACf,SAAO;AAAK,MAAE,CAAC,IAAI,UAAU,CAAC;AAC9B,SAAO;AACX;AACO,IAAM,kBAAkB,OAAO,WAAW,cAC3C,CAAC,OAAiB,GAAG,OAAO,WAAW,MAAM,kBAC7C,MAAI;ACvRH,IAAI,QAAQ,OAAO,aAAa,eAE/B,6CAA6C,KAAK,SAAS,IAAI;SAEvD,SAAS,OAAO,QAAM;AAClC,UAAQ;AACR,kBAAgB;AACpB;AAEO,IAAI,gBAAgB,MAAM;AAE1B,IAAM,wBAAwB,CAAC,IAAI,MAAM,EAAE,EAAE;SAEpC,oBAAiB;AAE7B,MAAI;AAAuB,QAAI;AAM3B,wBAAkB;AAClB,YAAM,IAAI,MAAK;aACX,GAAG;AACP,aAAO;;AAEX,SAAO,IAAI,MAAK;AACpB;SAEgB,YAAY,WAAW,kBAAgB;AACnD,MAAI,QAAQ,UAAU;AACtB,MAAI,CAAC;AAAO,WAAO;AACnB,qBAAoB,oBAAoB;AACxC,MAAI,MAAM,QAAQ,UAAU,IAAI,MAAM;AAClC,yBAAqB,UAAU,OAAO,UAAU,SAAS,MAAM,IAAI,EAAE;AACzE,SAAO,MAAM,MAAM,IAAI,EAClB,MAAM,gBAAgB,EACtB,OAAO,aAAa,EACpB,IAAI,WAAS,OAAO,KAAK,EACzB,KAAK,EAAE;AAChB;ACvCA,IAAI,kBAAkB;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ,IAAI,mBAAmB;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ,IAAI,YAAY,gBAAgB,OAAO,gBAAgB;AAEvD,IAAI,eAAe;EACf,gBAAgB;EAChB,gBAAgB;EAChB,OAAO;EACP,qBAAqB;EACrB,YAAY;;SAMA,WAAY,MAAM,KAAG;AAMjC,OAAK,KAAK,kBAAiB;AAC3B,OAAK,OAAO;AACZ,OAAK,UAAU;AACnB;AAEA,OAAO,UAAU,EAAE,KAAK,KAAK,EAAE,OAAO;EAClC,OAAO;IACH,KAAK,WAAA;AACD,aAAO,KAAK,WACP,KAAK,SAAS,KAAK,OAAO,OAAO,KAAK,UAAU,YAAY,KAAK,IAAI,CAAC;;;EAGnF,UAAU,WAAA;AAAY,WAAO,KAAK,OAAO,OAAO,KAAK;EAAQ;CAChE;AAED,SAAS,qBAAsB,KAAK,UAAQ;AACxC,SAAO,MAAM,eAAe,OAAO,KAAK,QAAQ,EAC3C,IAAI,SAAK,SAAS,GAAG,EAAE,SAAQ,CAAE,EACjC,OAAO,CAAC,GAAE,GAAE,MAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAClC,KAAK,IAAI;AAClB;SAMgB,YAAa,KAAK,UAAU,cAAc,YAAU;AAChE,OAAK,KAAK,kBAAiB;AAC3B,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,OAAK,eAAe;AACpB,OAAK,UAAU,qBAAqB,KAAK,QAAQ;AACrD;AACA,OAAO,WAAW,EAAE,KAAK,UAAU;SAEnB,UAAW,KAAK,UAAQ;AACpC,OAAK,KAAK,kBAAiB;AAC3B,OAAK,OAAO;AACZ,OAAK,WAAW,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAO,SAAS,GAAG,CAAC;AAC9D,OAAK,gBAAgB;AACrB,OAAK,UAAU,qBAAqB,KAAK,QAAQ;AACrD;AACA,OAAO,SAAS,EAAE,KAAK,UAAU;AAU1B,IAAI,WAAW,UAAU,OAAO,CAAC,KAAI,UAAQ,IAAI,IAAI,IAAE,OAAK,SAAQ,MAAK,CAAA,CAAE;AAGlF,IAAM,gBAAgB;AAEf,IAAI,aAAa,UAAU,OAAO,CAAC,KAAI,SAAI;AAO9C,MAAI,WAAW,OAAO;AACtB,WAASC,YAAY,YAAY,OAAK;AAClC,SAAK,KAAK,kBAAiB;AAC3B,SAAK,OAAO;AACZ,QAAI,CAAC,YAAY;AACb,WAAK,UAAU,aAAa,IAAI,KAAK;AACrC,WAAK,QAAQ;eACN,OAAO,eAAe,UAAU;AACvC,WAAK,UAAU,GAAG,UAAU,GAAG,CAAC,QAAQ,KAAK,QAAQ,KAAK;AAC1D,WAAK,QAAQ,SAAS;eACf,OAAO,eAAe,UAAU;AACvC,WAAK,UAAU,GAAG,WAAW,IAAI,IAAI,WAAW,OAAO;AACvD,WAAK,QAAQ;;;AAGrB,SAAOA,WAAU,EAAE,KAAK,aAAa;AACrC,MAAI,IAAI,IAAEA;AACV,SAAO;AACX,GAAE,CAAA,CAAE;AAGJ,WAAW,SAAS;AACpB,WAAW,OAAO;AAClB,WAAW,QAAQ;AAEZ,IAAI,eAAe,iBAAiB,OAAO,CAAC,KAAK,SAAI;AACxD,MAAI,OAAO,OAAO,IAAI,WAAW,IAAI;AACrC,SAAO;AACX,GAAG,CAAA,CAAE;SAEW,SAAU,UAAU,SAAO;AACvC,MAAI,CAAC,YAAY,oBAAoB,cAAc,oBAAoB,aAAa,oBAAoB,eAAe,CAAC,SAAS,QAAQ,CAAC,aAAa,SAAS,IAAI;AAChK,WAAO;AACX,MAAI,KAAK,IAAI,aAAa,SAAS,IAAI,EAAE,WAAW,SAAS,SAAS,QAAQ;AAC9E,MAAI,WAAW,UAAU;AAErB,YAAQ,IAAI,SAAS,EAAC,KAAK,WAAA;AACvB,aAAO,KAAK,MAAM;MACrB,CAAC;;AAEN,SAAO;AACX;AAEO,IAAI,qBAAqB,UAAU,OAAO,CAAC,KAAK,SAAI;AACvD,MAAI,CAAC,UAAS,QAAO,OAAO,EAAE,QAAQ,IAAI,MAAM;AAC5C,QAAI,OAAO,OAAO,IAAI,WAAW,IAAI;AACzC,SAAO;AACX,GAAG,CAAA,CAAE;AAEL,mBAAmB,cAAc;AACjC,mBAAmB,aAAa;AAChC,mBAAmB,YAAY;SC3Kf,MAAG;AAAA;SACH,OAAO,KAAG;AAAI,SAAO;AAAI;SACzB,kBAAkB,IAAI,IAAE;AAGpC,MAAI,MAAM,QAAQ,OAAO;AAAQ,WAAO;AACxC,SAAO,SAAU,KAAG;AAChB,WAAO,GAAG,GAAG,GAAG,CAAC;;AAEzB;SAEgB,SAAS,KAAK,KAAG;AAC7B,SAAO,WAAA;AACH,QAAI,MAAM,MAAM,SAAS;AACzB,QAAI,MAAM,MAAM,SAAS;;AAEjC;SAEgB,kBAAkB,IAAI,IAAE;AAGpC,MAAI,OAAO;AAAK,WAAO;AACvB,SAAO,WAAA;AACH,QAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,QAAI,QAAQ;AAAW,gBAAU,CAAC,IAAI;AACtC,QAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,QAAI,OAAO,GAAG,MAAM,MAAM,SAAS;AACnC,QAAI;AAAW,WAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,QAAI;AAAS,WAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;AAC7E,WAAO,SAAS,SAAY,OAAO;;AAE3C;SAEgB,kBAAkB,IAAI,IAAE;AACpC,MAAI,OAAO;AAAK,WAAO;AACvB,SAAO,WAAA;AACH,OAAG,MAAM,MAAM,SAAS;AACxB,QAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,SAAK,YAAY,KAAK,UAAU;AAChC,OAAG,MAAM,MAAM,SAAS;AACxB,QAAI;AAAW,WAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,QAAI;AAAS,WAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;;AAErF;SAEgB,kBAAkB,IAAI,IAAE;AACpC,MAAI,OAAO;AAAK,WAAO;AACvB,SAAO,SAAU,eAAa;AAC1B,QAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,WAAO,eAAe,GAAG;AACzB,QAAI,YAAY,KAAK,WACjB,UAAU,KAAK;AACnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,QAAI,OAAO,GAAG,MAAM,MAAM,SAAS;AACnC,QAAI;AAAW,WAAK,YAAY,KAAK,YAAY,SAAS,WAAW,KAAK,SAAS,IAAI;AACvF,QAAI;AAAS,WAAK,UAAU,KAAK,UAAU,SAAS,SAAS,KAAK,OAAO,IAAI;AAC7E,WAAO,QAAQ,SACV,SAAS,SAAY,SAAY,OACjC,OAAO,KAAK,IAAI;;AAE7B;SAEgB,2BAA2B,IAAI,IAAE;AAC7C,MAAI,OAAO;AAAK,WAAO;AACvB,SAAO,WAAA;AACH,QAAI,GAAG,MAAM,MAAM,SAAS,MAAM;AAAO,aAAO;AAChD,WAAO,GAAG,MAAM,MAAM,SAAS;;AAEvC;SAUgB,gBAAgB,IAAI,IAAE;AAClC,MAAI,OAAO;AAAK,WAAO;AACvB,SAAO,WAAA;AACH,QAAI,MAAM,GAAG,MAAM,MAAM,SAAS;AAClC,QAAI,OAAO,OAAO,IAAI,SAAS,YAAY;AACvC,UAAI,OAAO,MACP,IAAI,UAAU,QACd,OAAO,IAAI,MAAM,CAAC;AACtB,aAAO;AAAK,aAAK,CAAC,IAAI,UAAU,CAAC;AACjC,aAAO,IAAI,KAAK,WAAA;AACZ,eAAO,GAAG,MAAM,MAAM,IAAI;OAC7B;;AAEL,WAAO,GAAG,MAAM,MAAM,SAAS;;AAEvC;AChEA,IAAI,WAAW,CAAA;AAGf,IACI,yBAAyB;AAD7B,IAGI,kBAAkB;AAHtB,IAII,kBAAkB;AAJtB,IAKI,CAAC,uBAAuB,oBAAoB,qBAAqB,IAAI,OAAO,YAAY,cACpF,CAAA,KACC,MAAA;AACG,MAAI,UAAU,QAAQ,QAAO;AAC7B,MAAI,OAAO,WAAW,eAAe,CAAC,OAAO;AACzC,WAAO,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO;AAE/C,QAAM,UAAU,OAAO,OAAO,OAAO,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AACnE,SAAO;IACH;IACA,SAAS,OAAO;IAChB;;GAEP;AAlBT,IAmBI,oBAAoB,sBAAsB,mBAAmB;AAE1D,IAAM,gBAAgB,yBAAyB,sBAAsB;AAC5E,IAAM,qBAAqB,CAAC,CAAC;AAE7B,IAAI,wBAAwB;AAS5B,IAAI,uBAAuB,wBACvB,MAAA;AAAO,wBAAsB,KAAK,YAAY;AAAE,IAEhD,QAAQ,eAEJ,aAAa,KAAK,MAAM,YAAY,IACpC,QAAQ,mBAEJ,MAAA;AACI,MAAI,YAAY,SAAS,cAAc,KAAK;AAC5C,EAAC,IAAI,iBAAiB,MAAA;AAClB,iBAAY;AACZ,gBAAY;GACf,EAAG,QAAQ,WAAW,EAAE,YAAY,KAAI,CAAE;AAC3C,YAAU,aAAa,KAAK,GAAG;IAKnC,MAAA;AAAK,aAAW,cAAa,CAAC;AAAE;AAO5C,IAAI,OAAO,SAAU,UAAU,MAAI;AAC/B,iBAAe,KAAK,CAAC,UAAU,IAAI,CAAC;AACpC,MAAI,sBAAsB;AACtB,yBAAoB;AACpB,2BAAuB;;AAE/B;AAEA,IAAI,qBAAqB;AAAzB,IACI,uBAAuB;AAD3B,IAEI,kBAAkB,CAAA;AAFtB,IAGI,kBAAkB,CAAA;AAHtB,IAII,mBAAmB;AAJvB,IAKI,kBAAkB;AAEf,IAAI,YAAY;EACnB,IAAI;EACJ,QAAQ;EACR,KAAK;EACL,YAAY,CAAA;EACZ,aAAa;EACb,KAAK;EACL,KAAK,CAAA;EACL,UAAU,WAAA;AACN,SAAK,WAAW,QAAQ,QAAE;AACtB,UAAI;AACA,oBAAY,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;eACnB,GAAG;MAAA;KACf;;;AAIF,IAAI,MAAM;AAEV,IAAI,iBAAiB,CAAA;AACrB,IAAI,oBAAoB;AACxB,IAAI,iBAAiB,CAAA;SAEJ,aAAa,IAAE;AACnC,MAAI,OAAO,SAAS;AAAU,UAAM,IAAI,UAAU,sCAAsC;AACxF,OAAK,aAAa,CAAA;AAClB,OAAK,cAAc;AAQnB,OAAK,OAAO;AAEZ,MAAI,MAAO,KAAK,OAAO;AAEvB,MAAI,OAAO;AACP,SAAK,eAAe,kBAAiB;AACrC,SAAK,QAAQ;AACb,SAAK,WAAW;;AAGpB,MAAI,OAAO,OAAO,YAAY;AAC1B,QAAI,OAAO;AAAU,YAAM,IAAI,UAAU,gBAAgB;AAGzD,SAAK,SAAS,UAAU,CAAC;AACzB,SAAK,SAAS,UAAU,CAAC;AACzB,QAAI,KAAK,WAAW;AAChB,sBAAgB,MAAM,KAAK,MAAM;AACrC;;AAGJ,OAAK,SAAS;AACd,OAAK,SAAS;AACd,IAAE,IAAI;AACN,qBAAmB,MAAM,EAAE;AAC/B;AAGA,IAAM,WAAW;EACb,KAAK,WAAA;AACD,QAAI,MAAM,KAAK,cAAc;AAE7B,aAAS,KAAM,aAAa,YAAU;AAClC,UAAI,gBAAgB,CAAC,IAAI,WAAW,QAAQ,OAAO,gBAAgB;AACnE,YAAM,UAAU,iBAAiB,CAAC,wBAAuB;AACzD,UAAI,KAAK,IAAI,aAAa,CAAC,SAAS,WAAM;AACtC,4BAAoB,MAAM,IAAI,SAC1B,0BAA0B,aAAa,KAAK,eAAe,OAAO,GAClE,0BAA0B,YAAY,KAAK,eAAe,OAAO,GACjE,SACA,QACA,GAAG,CAAC;OACX;AACD,eAAS,sBAAsB,IAAI,IAAI;AACvC,aAAO;;AAGX,SAAK,YAAY;AAEjB,WAAO;;EAIX,KAAK,SAAU,OAAK;AAChB,YAAS,MAAM,QAAQ,SAAS,MAAM,cAAc,WAChD,WACA;MACI,KAAK,WAAA;AACD,eAAO;;MAEX,KAAK,SAAS;KACjB;;;AAKb,MAAM,aAAa,WAAW;EAC1B,MAAM;EACN,OAAO,SAAU,aAAa,YAAU;AAEpC,wBAAoB,MAAM,IAAI,SAAS,MAAM,MAAM,aAAa,YAAY,GAAG,CAAC;;EAGpF,OAAO,SAAU,YAAU;AACvB,QAAI,UAAU,WAAW;AAAG,aAAO,KAAK,KAAK,MAAM,UAAU;AAE7D,QAAIC,QAAO,UAAU,CAAC,GAClB,UAAU,UAAU,CAAC;AACzB,WAAO,OAAOA,UAAS,aAAa,KAAK,KAAK,MAAM,SAGhD,eAAeA,QAAO,QAAQ,GAAG,IAAI,cAAc,GAAG,CAAC,IACzD,KAAK,KAAK,MAAM,SAId,OAAO,IAAI,SAASA,QAAO,QAAQ,GAAG,IAAI,cAAc,GAAG,CAAC;;EAGpE,SAAS,SAAU,WAAS;AACxB,WAAO,KAAK,KAAK,WAAK;AAClB,gBAAS;AACT,aAAO;OACR,SAAG;AACF,gBAAS;AACT,aAAO,cAAc,GAAG;KAC3B;;EAGL,OAAO;IACH,KAAK,WAAA;AACD,UAAI,KAAK;AAAQ,eAAO,KAAK;AAC7B,UAAI;AACA,gCAAwB;AACxB,YAAI,SAAS,SAAU,MAAM,CAAA,GAAI,eAAe;AAChD,YAAI,QAAQ,OAAO,KAAK,mBAAmB;AAC3C,YAAI,KAAK,WAAW;AAAM,eAAK,SAAS;AACxC,eAAO;;AAEP,gCAAwB;;;;EAKpC,SAAS,SAAU,IAAI,KAAG;AACtB,WAAO,KAAK,WACR,IAAI,aAAa,CAAC,SAAS,WAAM;AAC7B,UAAI,SAAS,WAAW,MAAM,OAAO,IAAI,WAAW,QAAQ,GAAG,CAAC,GAAG,EAAE;AACrE,WAAK,KAAK,SAAS,MAAM,EAAE,QAAQ,aAAa,KAAK,MAAM,MAAM,CAAC;KACrE,IAAI;;CAEhB;AAED,IAAI,OAAO,WAAW,eAAe,OAAO;AACxC,UAAQ,aAAa,WAAW,OAAO,aAAa,eAAe;AAIvE,UAAU,MAAM,SAAQ;AAExB,SAAS,SAAS,aAAa,YAAY,SAAS,QAAQ,MAAI;AAC5D,OAAK,cAAc,OAAO,gBAAgB,aAAa,cAAc;AACrE,OAAK,aAAa,OAAO,eAAe,aAAa,aAAa;AAClE,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,MAAM;AACf;AAGA,MAAO,cAAc;EACjB,KAAK,WAAA;AACD,QAAI,SAAS,WAAW,MAAM,MAAM,SAAS,EACxC,IAAI,wBAAwB;AACjC,WAAO,IAAI,aAAa,SAAU,SAAS,QAAM;AAC7C,UAAI,OAAO,WAAW;AAAG,gBAAQ,CAAA,CAAE;AACnC,UAAI,YAAY,OAAO;AACvB,aAAO,QAAQ,CAAC,GAAE,MAAM,aAAa,QAAQ,CAAC,EAAE,KAAK,OAAC;AAClD,eAAO,CAAC,IAAI;AACZ,YAAI,CAAC,EAAE;AAAW,kBAAQ,MAAM;SACjC,MAAM,CAAC;KACb;;EAGL,SAAS,WAAK;AACV,QAAI,iBAAiB;AAAc,aAAO;AAC1C,QAAI,SAAS,OAAO,MAAM,SAAS;AAAY,aAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACnF,cAAM,KAAK,SAAS,MAAM;OAC7B;AACD,QAAI,KAAK,IAAI,aAAa,UAAU,MAAM,KAAK;AAC/C,0BAAsB,IAAI,gBAAgB;AAC1C,WAAO;;EAGX,QAAQ;EAER,MAAM,WAAA;AACF,QAAI,SAAS,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AAC3E,WAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACpC,aAAO,IAAI,WAAS,aAAa,QAAQ,KAAK,EAAE,KAAK,SAAS,MAAM,CAAC;KACxE;;EAGL,KAAK;IACD,KAAK,MAAI;IACT,KAAK,WAAS,MAAM;;EAGxB,aAAa,EAAC,KAAK,MAAI,YAAW;EAIlC,QAAQ;EAER;EAEA,WAAW;IACP,KAAK,MAAM;IACX,KAAK,WAAK;AAAK,aAAO;IAAK;;EAG/B,iBAAiB;IACb,KAAK,MAAM;IACX,KAAK,WAAK;AAAK,wBAAkB;IAAM;;EAG3C,QAAQ,CAAC,IAAI,cAAS;AAClB,WAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACpC,aAAO,SAAS,CAACC,UAASC,YAAM;AAC5B,YAAI,MAAM;AACV,YAAI,aAAa,CAAA;AACjB,YAAI,cAAcA;AAClB,YAAI,WAAW,SAAS,WAAA;AAIpB,mDAAyC,MAAA;AACrC,iBAAK,WAAW,WAAW,IAAID,SAAO,IAAKC,QAAO,KAAK,WAAW,CAAC,CAAC;WACvE;WACF,IAAI,QAAQ;AACf,WAAE;SACH,WAAW,SAAS,MAAM;KAChC;;CAER;AAED,IAAI,eAAe;AACf,MAAI,cAAc;AAAY,YAAS,cAAc,cAAc,WAAA;AAC/D,YAAM,mBAAmB,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AACvF,aAAO,IAAI,aAAa,aAAO;AAC3B,YAAI,iBAAiB,WAAW;AAAG,kBAAQ,CAAA,CAAE;AAC7C,YAAI,YAAY,iBAAiB;AACjC,cAAM,UAAU,IAAI,MAAM,SAAS;AACnC,yBAAiB,QAAQ,CAAC,GAAG,MAAM,aAAa,QAAQ,CAAC,EAAE,KACvD,WAAS,QAAQ,CAAC,IAAI,EAAC,QAAQ,aAAa,MAAK,GACjD,YAAU,QAAQ,CAAC,IAAI,EAAC,QAAQ,YAAY,OAAM,CAAC,EAClD,KAAK,MAAI,EAAE,aAAa,QAAQ,OAAO,CAAC,CAAC;OACjD;KACJ;AACD,MAAI,cAAc,OAAO,OAAO,mBAAmB;AAAa,YAAQ,cAAc,OAAO,WAAA;AACzF,YAAM,mBAAmB,WAAW,MAAM,MAAM,SAAS,EAAE,IAAI,wBAAwB;AACvF,aAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACpC,YAAI,iBAAiB,WAAW;AAAG,iBAAO,IAAI,eAAe,CAAA,CAAE,CAAC;AAChE,YAAI,YAAY,iBAAiB;AACjC,cAAM,WAAW,IAAI,MAAM,SAAS;AACpC,yBAAiB,QAAQ,CAAC,GAAG,MAAM,aAAa,QAAQ,CAAC,EAAE,KACvD,WAAS,QAAQ,KAAK,GACtB,aAAO;AACH,mBAAS,CAAC,IAAI;AACd,cAAI,CAAC,EAAE;AAAW,mBAAO,IAAI,eAAe,QAAQ,CAAC;SACxD,CAAC;OACT;KACJ;;AASL,SAAS,mBAAoB,SAAS,IAAE;AAGpC,MAAI;AACA,OAAG,WAAK;AACJ,UAAI,QAAQ,WAAW;AAAM;AAC7B,UAAI,UAAU;AAAS,cAAM,IAAI,UAAU,2CAA2C;AACtF,UAAI,oBAAoB,QAAQ,QAAQ,oBAAmB;AAC3D,UAAI,SAAS,OAAO,MAAM,SAAS,YAAY;AAC3C,2BAAmB,SAAS,CAAC,SAAS,WAAM;AACxC,2BAAiB,eACb,MAAM,MAAM,SAAS,MAAM,IAC3B,MAAM,KAAK,SAAS,MAAM;SACjC;aACE;AACH,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,8BAAsB,OAAO;;AAEjC,UAAI;AAAmB,0BAAiB;OACzC,gBAAgB,KAAK,MAAM,OAAO,CAAC;WACjC,IAAI;AACT,oBAAgB,SAAS,EAAE;;AAEnC;AAEA,SAAS,gBAAiB,SAAS,QAAM;AACrC,kBAAgB,KAAK,MAAM;AAC3B,MAAI,QAAQ,WAAW;AAAM;AAC7B,MAAI,oBAAoB,QAAQ,QAAQ,oBAAmB;AAC3D,WAAS,gBAAgB,MAAM;AAC/B,UAAQ,SAAS;AACjB,UAAQ,SAAS;AACjB,WAAS,WAAW,QAAQ,OAAO,WAAW,YAAY,CAAC,OAAO,YAAY,SAAS,MAAA;AACnF,QAAI,WAAW,sBAAsB,QAAQ,OAAO;AACpD,WAAO,WAAW;AAClB,YAAQ,QAAQ,SAAS;MACrB,KAAK,MACD,wBACI,aAAa,SAAS,MACV,SAAS,IAAI,MAAM,MAAM,IACzB,SAAS,SACrB,QAAQ;KACnB;GACJ;AAED,4BAA0B,OAAO;AACjC,wBAAsB,OAAO;AAC7B,MAAI;AAAmB,sBAAiB;AAC5C;AAEA,SAAS,sBAAuB,SAAO;AAEnC,MAAI,YAAY,QAAQ;AACxB,UAAQ,aAAa,CAAA;AACrB,WAAS,IAAI,GAAG,MAAM,UAAU,QAAQ,IAAI,KAAK,EAAE,GAAG;AAClD,wBAAoB,SAAS,UAAU,CAAC,CAAC;;AAE7C,MAAI,MAAM,QAAQ;AAClB,IAAE,IAAI,OAAO,IAAI,SAAQ;AACzB,MAAI,sBAAsB,GAAG;AAMzB,MAAE;AACF,SAAK,MAAA;AACD,UAAI,EAAE,sBAAsB;AAAG,6BAAoB;OACpD,CAAA,CAAE;;AAEb;AAEA,SAAS,oBAAoB,SAAS,UAAQ;AAC1C,MAAI,QAAQ,WAAW,MAAM;AACzB,YAAQ,WAAW,KAAK,QAAQ;AAChC;;AAGJ,MAAI,KAAK,QAAQ,SAAS,SAAS,cAAc,SAAS;AAC1D,MAAI,OAAO,MAAM;AAEb,YAAQ,QAAQ,SAAS,SAAS,UAAU,SAAS,QAAS,QAAQ,MAAM;;AAEhF,IAAE,SAAS,IAAI;AACf,IAAE;AACF,OAAM,cAAc,CAAC,IAAI,SAAS,QAAQ,CAAC;AAC/C;AAEA,SAAS,aAAc,IAAI,SAAS,UAAQ;AACxC,MAAI;AAGA,uBAAmB;AAGnB,QAAI,KAAK,QAAQ,QAAQ;AAEzB,QAAI,QAAQ,QAAQ;AAEhB,YAAM,GAAI,KAAK;WACZ;AAEH,UAAI,gBAAgB;AAAQ,0BAAkB,CAAA;AAC9C,YAAM,GAAG,KAAK;AACd,UAAI,gBAAgB,QAAQ,KAAK,MAAM;AACnC,2BAAmB,OAAO;;AAElC,aAAS,QAAQ,GAAG;WACf,GAAG;AAER,aAAS,OAAO,CAAC;;AAGjB,uBAAmB;AACnB,QAAI,EAAE,sBAAsB;AAAG,2BAAoB;AACnD,MAAE,SAAS,IAAI,OAAO,SAAS,IAAI,SAAQ;;AAEnD;AAEA,SAAS,SAAU,SAAS,QAAQ,OAAK;AACrC,MAAI,OAAO,WAAW;AAAO,WAAO;AACpC,MAAI,QAAQ;AACZ,MAAI,QAAQ,WAAW,OAAO;AAC1B,QAAI,UAAU,QAAQ,QAClB,WACA;AAEJ,QAAI,WAAW,MAAM;AACjB,kBAAY,QAAQ,QAAQ;AAC5B,gBAAU,QAAQ,WAAW;AAC7B,cAAQ,YAAY,SAAS,CAAC;WAC3B;AACH,kBAAY;AACZ,gBAAU;;AAEd,WAAO,KAAK,aAAa,UAAU,OAAO,UAAU,MAAM,KAAK;;AAEnE,MAAI,OAAO;AACP,YAAQ,YAAY,QAAQ,cAAc,CAAC;AAC3C,QAAI,SAAS,OAAO,QAAQ,KAAK,MAAM;AAAI,aAAO,KAAK,KAAK;AAC5D,QAAI,QAAQ;AAAO,eAAS,QAAQ,OAAO,QAAQ,KAAK;;AAE5D,SAAO;AACX;AAEA,SAAS,sBAAsB,SAAS,MAAI;AAExC,MAAI,UAAU,OAAO,KAAK,WAAW,IAAI;AACzC,MAAI,UAAU,wBAAwB;AAClC,YAAQ,QAAQ;AAChB,YAAQ,WAAW;;AAE3B;AAKA,SAAS,eAAY;AACjB,sBAAmB,KAAM,kBAAiB;AAC9C;SAEgB,sBAAmB;AAC/B,MAAI,cAAc;AAClB,uBAAqB;AACrB,yBAAuB;AACvB,SAAO;AACX;SAUgB,oBAAiB;AAC7B,MAAI,WAAW,GAAG;AAClB,KAAG;AACC,WAAO,eAAe,SAAS,GAAG;AAC9B,kBAAY;AACZ,uBAAiB,CAAA;AACjB,UAAI,UAAU;AACd,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACpB,YAAI,OAAO,UAAU,CAAC;AACtB,aAAK,CAAC,EAAE,MAAM,MAAM,KAAK,CAAC,CAAC;;;WAG9B,eAAe,SAAS;AACjC,uBAAqB;AACrB,yBAAuB;AAC3B;AAEA,SAAS,uBAAoB;AACzB,MAAI,gBAAgB;AACpB,oBAAkB,CAAA;AAClB,gBAAc,QAAQ,OAAC;AACnB,MAAE,KAAK,YAAY,KAAK,MAAM,EAAE,QAAQ,CAAC;GAC5C;AACD,MAAI,aAAa,eAAe,MAAM,CAAC;AACvC,MAAI,IAAI,WAAW;AACnB,SAAO;AAAG,eAAW,EAAE,CAAC,EAAC;AAC7B;AAEA,SAAS,yCAA0C,IAAE;AACjD,WAAS,YAAS;AACd,OAAE;AACF,mBAAe,OAAO,eAAe,QAAQ,SAAS,GAAG,CAAC;;AAE9D,iBAAe,KAAK,SAAS;AAC7B,IAAE;AACF,OAAK,MAAA;AACD,QAAI,EAAE,sBAAsB;AAAG,2BAAoB;KACpD,CAAA,CAAE;AACT;AAEA,SAAS,0BAA0B,SAAO;AAItC,MAAI,CAAC,gBAAgB,KAAK,OAAK,EAAE,WAAW,QAAQ,MAAM;AACtD,oBAAgB,KAAK,OAAO;AACpC;AAEA,SAAS,mBAAmB,SAAO;AAI/B,MAAI,IAAI,gBAAgB;AACxB,SAAO;AAAG,QAAI,gBAAgB,EAAE,CAAC,EAAE,WAAW,QAAQ,QAAQ;AAG1D,sBAAgB,OAAO,GAAG,CAAC;AAC3B;;AAER;AAEA,SAAS,cAAe,QAAM;AAC1B,SAAO,IAAI,aAAa,UAAU,OAAO,MAAM;AACnD;SAEgB,KAAM,IAAI,cAAY;AAClC,MAAI,MAAM;AACV,SAAO,WAAA;AACH,QAAI,cAAc,oBAAmB,GACjC,aAAa;AAEjB,QAAI;AACA,mBAAa,KAAK,IAAI;AACtB,aAAO,GAAG,MAAM,MAAM,SAAS;aAC1B,GAAG;AACR,sBAAgB,aAAa,CAAC;;AAE9B,mBAAa,YAAY,KAAK;AAC9B,UAAI;AAAa,0BAAiB;;;AAG9C;AAMA,IAAM,OAAO,EAAE,QAAQ,GAAG,QAAQ,GAAG,IAAI,EAAC;AAC1C,IAAI,cAAc;AAClB,IAAI,YAAY,CAAA;AAChB,IAAI,aAAa;AACjB,IAAI,cAAc;AAGlB,IAAI,kBAAkB;SACN,SAAU,IAAIC,QAAO,IAAI,IAAE;AACvC,MAAI,SAAS,KACT,MAAM,OAAO,OAAO,MAAM;AAC9B,MAAI,SAAS;AACb,MAAI,MAAM;AACV,MAAI,SAAS;AACb,MAAI,KAAK,EAAE;AAEX,MAAI,YAAY,UAAU;AAC1B,MAAI,MAAM,qBAAqB;IAC3B,SAAS;IACT,aAAa,EAAC,OAAO,cAAc,cAAc,MAAM,UAAU,KAAI;IACrE,KAAK,aAAa;IAClB,MAAM,aAAa;IACnB,YAAY,aAAa;IACzB,KAAK,aAAa;IAClB,SAAS,aAAa;IACtB,QAAQ,aAAa;IACrB,OAAO,sBAAuB,UAAU,OAAO,GAAG;IAClD,OAAO,sBAAuB,UAAU,OAAO,GAAG;MAClD,CAAA;AACJ,MAAIA;AAAO,WAAO,KAAKA,MAAK;AAM5B,IAAE,OAAO;AACT,MAAI,WAAW,WAAA;AACX,MAAE,KAAK,OAAO,OAAO,KAAK,OAAO,SAAQ;;AAE7C,MAAI,KAAK,OAAQ,KAAK,IAAI,IAAI,EAAE;AAChC,MAAI,IAAI,QAAQ;AAAG,QAAI,SAAQ;AAC/B,SAAO;AACX;SAIgB,0BAAuB;AACnC,MAAI,CAAC,KAAK;AAAI,SAAK,KAAK,EAAE;AAC1B,IAAE,KAAK;AACP,OAAK,UAAU;AACf,SAAO,KAAK;AAChB;SAKgB,0BAAuB;AACnC,MAAI,CAAC,KAAK;AAAQ,WAAO;AACzB,MAAI,EAAE,KAAK,WAAW;AAAG,SAAK,KAAK;AACnC,OAAK,SAAS,KAAK,SAAS;AAC5B,SAAO;AACX;AAEA,KAAK,KAAG,mBAAmB,QAAQ,eAAe,MAAM,IAAI;AAGxD,4BAA0B,0BAA0B;;SAIxC,yBAA0B,iBAAe;AACrD,MAAI,KAAK,UAAU,mBAAmB,gBAAgB,gBAAgB,eAAe;AACjF,4BAAuB;AACvB,WAAO,gBAAgB,KAAK,OAAC;AACzB,8BAAuB;AACvB,aAAO;OACR,OAAC;AACA,8BAAuB;AACvB,aAAO,UAAU,CAAC;KACrB;;AAEL,SAAO;AACX;AAEA,SAAS,cAAc,YAAU;AAC7B,IAAE;AAEF,MAAI,CAAC,KAAK,UAAU,EAAE,KAAK,WAAW,GAAG;AACrC,SAAK,SAAS,KAAK,KAAK;;AAG5B,YAAU,KAAK,GAAG;AAClB,eAAa,YAAY,IAAI;AACjC;AAEA,SAAS,gBAAa;AAClB,MAAI,OAAO,UAAU,UAAU,SAAO,CAAC;AACvC,YAAU,IAAG;AACb,eAAa,MAAM,KAAK;AAC5B;AAEA,SAAS,aAAc,YAAY,eAAa;AAC5C,MAAI,cAAc;AAClB,MAAI,gBAAgB,KAAK,WAAW,CAAC,gBAAgB,eAAe,OAAO,eAAe,CAAC,EAAE,cAAc,eAAe,MAAM;AAG5H,2BAAuB,gBAAgB,cAAc,KAAK,MAAM,UAAU,IAAI,aAAa;;AAE/F,MAAI,eAAe;AAAK;AAExB,QAAM;AAGN,MAAI,gBAAgB;AAAW,cAAU,MAAM,SAAQ;AAEvD,MAAI,oBAAoB;AAEpB,QAAI,gBAAgB,UAAU,IAAI;AAElC,QAAI,YAAY,WAAW;AAI3B,uBAAmB,OAAO,UAAU;AACpC,kBAAc,UAAU,OAAO,UAAU;AAEzC,QAAI,YAAY,UAAU,WAAW,QAAQ;AAIzC,aAAO,eAAe,SAAS,WAAW,UAAU,WAAW;AAI/D,oBAAc,MAAM,UAAU;AAC9B,oBAAc,OAAO,UAAU;AAC/B,oBAAc,UAAU,UAAU;AAClC,oBAAc,SAAS,UAAU;AACjC,UAAI,UAAU;AAAY,sBAAc,aAAa,UAAU;AAC/D,UAAI,UAAU;AAAK,sBAAc,MAAM,UAAU;;;AAG7D;AAEA,SAAS,WAAQ;AACb,MAAI,gBAAgB,QAAQ;AAC5B,SAAO,qBAAqB;IACxB,SAAS;IACT,aAAa,OAAO,yBAAyB,SAAS,SAAS;IAC/D,KAAK,cAAc;IACnB,MAAM,cAAc;IACpB,YAAY,cAAc;IAC1B,KAAK,cAAc;IACnB,SAAS,cAAc;IACvB,QAAQ,cAAc;IACtB,OAAO,mBAAmB;IAC1B,OAAO,cAAc,UAAU;MAC/B,CAAA;AACR;SAEgB,OAAQ,KAAK,IAAI,IAAI,IAAI,IAAE;AACvC,MAAI,aAAa;AACjB,MAAI;AACA,iBAAa,KAAK,IAAI;AACtB,WAAO,GAAG,IAAI,IAAI,EAAE;;AAEpB,iBAAa,YAAY,KAAK;;AAEtC;AAEA,SAAS,uBAAwB,KAAG;AAIhC,oBAAkB,KAAK,uBAAuB,GAAG;AACrD;AAEA,SAAS,0BAA0B,IAAI,MAAM,eAAe,SAAO;AAC/D,SAAO,OAAO,OAAO,aAAa,KAAK,WAAA;AACnC,QAAI,YAAY;AAChB,QAAI;AAAe,8BAAuB;AAC1C,iBAAa,MAAM,IAAI;AACvB,QAAI;AACA,aAAO,GAAG,MAAM,MAAM,SAAS;;AAE/B,mBAAa,WAAW,KAAK;AAC7B,UAAI;AAAS,+BAAuB,uBAAuB;;;AAGvE;AAEA,SAAS,sBAAuB,UAAU,MAAI;AAC1C,SAAO,SAAU,YAAY,YAAU;AACnC,WAAO,SAAS,KAAK,MACjB,0BAA0B,YAAY,IAAI,GAC1C,0BAA0B,YAAY,IAAI,CAAC;;AAEvD;AAEA,IAAM,qBAAqB;AAE3B,SAAS,YAAY,KAAK,SAAO;AAC7B,MAAI;AACJ,MAAI;AACA,SAAK,QAAQ,YAAY,GAAG;WACvB,GAAG;EAAA;AACZ,MAAI,OAAO;AAAO,QAAI;AAClB,UAAI,OAAO,YAAY,EAAC,SAAkB,QAAQ,IAAG;AACrD,UAAI,QAAQ,YAAY,SAAS,aAAa;AAC1C,gBAAQ,SAAS,YAAY,OAAO;AACpC,cAAM,UAAU,oBAAoB,MAAM,IAAI;AAC9C,eAAO,OAAO,SAAS;iBAChB,QAAQ,aAAa;AAC5B,gBAAQ,IAAI,YAAY,oBAAoB,EAAC,QAAQ,UAAS,CAAC;AAC/D,eAAO,OAAO,SAAS;;AAE3B,UAAI,SAAS,QAAQ,eAAe;AAChC,sBAAc,KAAK;AACnB,YAAI,CAAC,QAAQ,yBAAyB,QAAQ;AAE1C,cAAI;AAAC,oBAAQ,qBAAqB,KAAK;mBAAW,GAAG;UAAA;;AAE7D,UAAI,SAAS,SAAS,CAAC,MAAM,kBAAkB;AAC3C,gBAAQ,KAAK,wBAAwB,IAAI,SAAS,GAAG,EAAE;;aAEtD,GAAG;IAAA;AAChB;AAEO,IAAI,YAAY,aAAa;SCh3BpB,gBACd,IACA,MACA,YACA,IAAgD;AAGhD,MAAI,CAAC,GAAG,SAAU,CAAC,GAAG,OAAO,iBAAiB,CAAC,IAAI,cAAc,CAAC,GAAG,OAAQ;AAC3E,QAAI,GAAG,OAAO,cAAc;AAG1B,aAAO,UAAU,IAAI,WAAW,eAAe,GAAG,OAAO,WAAW,CAAC;;AAEvE,QAAI,CAAC,GAAG,OAAO,eAAe;AAC5B,UAAI,CAAC,GAAG,SAAS;AACf,eAAO,UAAU,IAAI,WAAW,eAAc,CAAE;AAClD,SAAG,KAAI,EAAG,MAAM,GAAG;;AAErB,WAAO,GAAG,OAAO,eAAe,KAAK,MAAM,gBAAgB,IAAI,MAAM,YAAY,EAAE,CAAC;SAC/E;AACL,QAAI,QAAQ,GAAG,mBAAmB,MAAM,YAAY,GAAG,SAAS;AAChE,QAAI;AACF,YAAM,OAAM;AACZ,SAAG,OAAO,iBAAiB;aACpB,IAAI;AACX,UAAI,GAAG,SAAS,SAAS,gBAAgB,GAAG,OAAM,KAAM,EAAE,GAAG,OAAO,iBAAiB,GAAG;AACtF,gBAAQ,KAAK,0BAA0B;AACvC,WAAG,OAAM;AACT,eAAO,GAAG,KAAI,EAAG,KAAK,MAAI,gBAAgB,IAAI,MAAM,YAAY,EAAE,CAAC;;AAErE,aAAO,UAAU,EAAE;;AAErB,WAAO,MAAM,SAAS,MAAM,CAAC,SAAS,WAAM;AAC1C,aAAO,SAAS,MAAA;AACd,YAAI,QAAQ;AACZ,eAAO,GAAG,SAAS,QAAQ,KAAK;OACjC;KACF,EAAE,KAAK,YAAM;AAWZ,aAAO,MAAM,YAAY,KAAK,MAAM,MAAM;KAC3C;;AAKL;AC7DO,IAAM,gBAAgB;AACtB,IAAM,YAAY,OAAO,aAAa,KAAK;AAC3C,IAAM,SAAS;AACf,IAAM,uBACX;AACK,IAAM,kBAAkB;AACxB,IAAM,cAAuB,CAAA;AAC7B,IAAM,aACX,OAAO,cAAc,eAAe,sBAAsB,KAAK,UAAU,SAAS;AAC7E,IAAM,4BAA4B;AAClC,IAAM,6BAA6B;AACnC,IAAM,wBAAwB,WAAS,CAAC,6BAA6B,KAAK,KAAK;AAC/E,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,YAAY;SChBT,QAAQ,SAAS,SAAO;AACtC,SAAO,UACH,UACI,WAAA;AAAc,WAAO,QAAQ,MAAM,MAAM,SAAS,KAAK,QAAQ,MAAM,MAAM,SAAS;EAAE,IACtF,UACJ;AACN;ACJO,IAAM,WAA2B;EACtC,MAAI;EACJ,OAAO;EACP,WAAW;EACX,OAAO,CAAC,CAAA,CAAE;EACV,WAAW;;SCFG,8BAA8B,SAAmC;AAE/E,SAAO,OAAO,YAAY,YAAY,CAAC,KAAK,KAAK,OAAO,IACtD,CAAC,QAAW;AACZ,QAAI,IAAI,OAAO,MAAM,UAAc,WAAW,KAAM;AAIlD,YAAM,UAAU,GAAG;AACnB,aAAO,IAAI,OAAO;;AAEpB,WAAO;MAEP,CAAC,QAAgB;AACrB;ICIa,cAAK;EAQhB,OACE,MACA,IACA,aAA8B;AAE9B,UAAM,QAAqB,KAAK,OAAO,IAAI;AAC3C,UAAM,YAAY,KAAK;AAEvB,aAAS,wBAAwB,SAAS,QAAQC,QAAkB;AAClE,UAAI,CAACA,OAAM,OAAO,SAAS;AACzB,cAAM,IAAI,WAAW,SAAS,WAAW,YAAY,0BAA0B;AACjF,aAAO,GAAGA,OAAM,UAAUA,MAAK;;AAejC,UAAM,cAAc,oBAAmB;AACvC,QAAI;AACF,aAAO,SAAS,MAAM,OAAO,KAAK,KAChC,UAAU,IAAI,QACZ,MAAM,SAAS,MAAM,yBAAyB,WAAW,IACzD,SAAS,MAAM,MAAM,SAAS,MAAM,yBAAyB,WAAW,GAAG,EAAE,OAAc,WAAW,IAAI,aAAa,IAAG,CAAE,IAC9H,gBAAgB,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,uBAAuB;;AAErE,UAAI;AAAa,0BAAiB;;;EAStC,IAAI,WAAW,IAAG;AAChB,QAAI,aAAa,UAAU,gBAAgB;AACzC,aAAO,KAAK,MAAM,SAA6C,EAAE,MAAM,EAAE;AAE3E,WAAO,KAAK,OAAO,YAAY,CAAC,UAAK;AACnC,aAAO,KAAK,KAAK,IAAI,EAAC,OAAO,KAAK,UAAS,CAAC,EACzC,KAAK,SAAO,KAAK,KAAK,QAAQ,KAAK,GAAG,CAAC;KAC3C,EAAE,KAAK,EAAE;;EAQZ,MAAM,aAAiE;AACrE,QAAI,OAAO,gBAAgB;AACzB,aAAO,IAAI,KAAK,GAAG,YAAY,MAAM,WAAW;AAClD,QAAI,QAAQ,WAAW;AACrB,aAAO,IAAI,KAAK,GAAG,YAAY,MAAM,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG;AAEnE,UAAM,WAAW,KAAK,WAAW;AACjC,QAAI,SAAS,WAAW;AAEtB,aAAO,KACJ,MAAM,SAAS,CAAC,CAAC,EACjB,OAAO,YAAY,SAAS,CAAC,CAAC,CAAC;AAKpC,UAAM,gBAAgB,KAAK,OAAO,QAAQ,OAAO,KAAK,OAAO,OAAO,EAAE,OAAO,QAAE;AAC7E,UACE,GAAG,YACH,SAAS,MAAM,aAAW,GAAG,QAAQ,QAAQ,OAAO,KAAK,CAAC,GAAG;AAC3D,iBAAS,IAAE,GAAG,IAAE,SAAS,QAAQ,EAAE,GAAG;AACpC,cAAI,SAAS,QAAQ,GAAG,QAAQ,CAAC,CAAC,MAAM;AAAI,mBAAO;;AAErD,eAAO;;AAET,aAAO;KACR,EAAE,KAAK,CAAC,GAAE,MAAM,EAAE,QAAQ,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAEzD,QAAI,iBAAiB,KAAK,GAAG,YAAY,WAAW;AAGlD,YAAM,uBAAwB,cAAc,QAAqB,MAAM,GAAG,SAAS,MAAM;AACzF,aAAO,KACJ,MAAM,oBAAoB,EAC1B,OAAO,qBAAqB,IAAI,QAAM,YAAY,EAAE,CAAC,CAAC;;AAG3D,QAAI,CAAC,iBAAiB;AAAO,cAAQ,KACnC,aAAa,KAAK,UAAU,WAAW,CAAC,OAAO,KAAK,IAAI,uCACrC,SAAS,KAAK,GAAG,CAAC,GAAG;AAI1C,UAAM,EAAE,UAAS,IAAK,KAAK;AAC3B,UAAM,MAAM,KAAK,GAAG,MAAM;AAE1B,aAAS,OAAQ,GAAG,GAAC;AACnB,UAAI;AACF,eAAO,IAAI,IAAI,GAAE,CAAC,MAAM;eACjB,GAAG;AACV,eAAO;;;AAIX,UAAM,CAAC,KAAK,cAAc,IAAI,SAAS,OAAO,CAAC,CAAC,WAAW,YAAY,GAAG,YAAO;AAC/E,YAAM,QAAQ,UAAU,OAAO;AAC/B,YAAM,QAAQ,YAAY,OAAO;AACjC,aAAO;QACL,aAAa;QACb,aAAa,CAAC,QACZ,QACE,cACA,SAAS,MAAM,QACb,OAAC;AACC,gBAAM,OAAO,aAAa,GAAG,OAAO;AACpC,iBAAO,QAAQ,IAAI,KAAK,KAAK,KAAK,UAAQ,OAAO,OAAO,IAAI,CAAC;YAC3D,OAAK,OAAO,OAAO,aAAa,GAAG,OAAO,CAAC,CAAC,IAClD;;OAEL,CAAC,MAAM,IAAI,CAAC;AAEf,WAAO,MACL,KAAK,MAAM,IAAI,IAAI,EAAE,OAAO,YAAY,IAAI,OAAO,CAAC,EACjD,OAAO,cAAc,IACxB,gBACE,KAAK,OAAO,cAAc,IAC1B,KAAK,MAAM,QAAQ,EAAE,OAAO,EAAE;;EAQpC,OAAO,gBAAqC;AAC1C,WAAO,KAAK,aAAY,EAAG,IAAI,cAAc;;EAQ/C,MAAM,cAAkB;AACtB,WAAO,KAAK,aAAY,EAAG,MAAM,YAAY;;EAQ/C,OAAO,QAAc;AACnB,WAAO,KAAK,aAAY,EAAG,OAAO,MAAM;;EAQ1C,MAAM,SAAe;AACnB,WAAO,KAAK,aAAY,EAAG,MAAM,OAAO;;EAQ1C,KAAK,UAAsF;AACzF,WAAO,KAAK,aAAY,EAAG,KAAK,QAAQ;;EAQ1C,QAAQ,cAAkB;AACxB,WAAO,KAAK,aAAY,EAAG,QAAQ,YAAY;;EAQjD,eAAY;AACV,WAAO,IAAI,KAAK,GAAG,WAAW,IAAI,KAAK,GAAG,YAAY,IAAI,CAAC;;EAQ7D,QAAQ,OAAwB;AAC9B,WAAO,IAAI,KAAK,GAAG,WACjB,IAAI,KAAK,GAAG,YAAY,MAAM,QAAQ,KAAK,IACzC,IAAI,MAAM,KAAK,GAAG,CAAC,MACnB,KAAK,CAAC;;EAQZ,UAAO;AACL,WAAO,KAAK,aAAY,EAAG,QAAO;;EAQpC,WAAW,aAAqB;AAC9B,SAAK,OAAO,cAAc;AAG1B,UAAM,WAAW,SAAG;AAClB,UAAI,CAAC;AAAK,eAAO;AAEjB,YAAM,MAAM,OAAO,OAAO,YAAY,SAAS;AAE/C,eAAS,KAAK;AAAK,YAAI,OAAO,KAAK,CAAC;AAAG,cAAI;AAAE,gBAAI,CAAC,IAAI,IAAI,CAAC;mBAAY,GAAG;UAAA;AAC1E,aAAO;;AAGT,QAAI,KAAK,OAAO,UAAU;AACxB,WAAK,KAAK,QAAQ,YAAY,KAAK,OAAO,QAAQ;;AAEpD,SAAK,OAAO,WAAW;AACvB,SAAK,KAAK,WAAW,QAAQ;AAC7B,WAAO;;EAIT,cAAW;AACT,aAAS,MAAO,SAAO;AACrB,aAAO,MAAM,OAAO;;AAEtB,WAAO,KAAK,WAAW,KAAK;;EAQ9B,IAAI,KAAK,KAAmB;AAC1B,UAAM,EAAC,MAAM,QAAO,IAAI,KAAK,OAAO;AACpC,QAAI,WAAW;AACf,QAAI,WAAW,MAAM;AACnB,iBAAW,8BAA8B,OAAO,EAAE,GAAG;;AAEvD,WAAO,KAAK,OAAO,aAAa,WAAK;AACnC,aAAO,KAAK,KAAK,OAAO,EAAC,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,CAAC,QAAQ,EAAC,CAAC;KACnG,EAAE,KAAK,SAAO,IAAI,cAAcC,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI,UAAU,EAChF,KAAK,gBAAU;AACd,UAAI,SAAS;AAIX,YAAG;AAAC,uBAAa,KAAK,SAAS,UAAU;iBAAS,GAAE;QAAA;;AAEtD,aAAO;KACR;;EAQH,OAAO,aAAa,eAAqH;AACvI,QAAI,OAAO,gBAAgB,YAAY,CAAC,QAAQ,WAAW,GAAG;AAC5D,YAAM,MAAM,aAAa,aAAa,KAAK,OAAO,QAAQ,OAAO;AACjE,UAAI,QAAQ;AAAW,eAAO,UAAU,IAAI,WAAW,gBACrD,+CAA+C,CAAC;AAKlD,UAAI;AACF,YAAI,OAAO,kBAAkB,YAAY;AACvC,eAAK,aAAa,EAAE,QAAQ,aAAO;AACjC,yBAAa,aAAa,SAAS,cAAc,OAAO,CAAC;WAC1D;eACI;AAGL,wBAAc,aAAa,EAAC,OAAO,aAAa,SAAS,IAAG,CAAC;;eAE/D,IAAM;;AAIR,aAAO,KAAK,MAAM,KAAK,EAAE,OAAO,GAAG,EAAE,OAAO,aAAa;WACpD;AAEL,aAAO,KAAK,MAAM,KAAK,EAAE,OAAO,WAAW,EAAE,OAAO,aAAa;;;EASrE,IAAI,KAAK,KAAmB;AAC1B,UAAM,EAAC,MAAM,QAAO,IAAI,KAAK,OAAO;AACpC,QAAI,WAAW;AACf,QAAI,WAAW,MAAM;AACnB,iBAAW,8BAA8B,OAAO,EAAE,GAAG;;AAEvD,WAAO,KAAK,OACV,aACA,WAAS,KAAK,KAAK,OAAO,EAAC,OAAO,MAAM,OAAO,QAAQ,CAAC,QAAQ,GAAG,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,KAAI,CAAC,CAAC,EACtG,KAAK,SAAO,IAAI,cAAcA,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,IAAI,UAAU,EAC9E,KAAK,gBAAU;AACd,UAAI,SAAS;AAIX,YAAG;AAAC,uBAAa,KAAK,SAAS,UAAU;iBAAS,GAAE;QAAA;;AAEtD,aAAO;KACR;;EAQH,OAAO,KAAkB;AACvB,WAAO,KAAK,OAAO,aACjB,WAAS,KAAK,KAAK,OAAO,EAAC,OAAO,MAAM,UAAU,MAAM,CAAC,GAAG,EAAC,CAAC,CAAC,EAChE,KAAK,SAAO,IAAI,cAAcA,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,MAAS;;EAQ5E,QAAK;AACH,WAAO,KAAK,OAAO,aACjB,WAAS,KAAK,KAAK,OAAO,EAAC,OAAO,MAAM,eAAe,OAAO,SAAQ,CAAC,CAAC,EACrE,KAAK,SAAO,IAAI,cAAcA,aAAQ,OAAO,IAAI,SAAS,CAAC,CAAC,IAAI,MAAS;;EAShF,QAAQC,OAAqB;AAC3B,WAAO,KAAK,OAAO,YAAY,WAAK;AAClC,aAAO,KAAK,KAAK,QAAQ;QACvB,MAAAA;QACA;OACD,EAAE,KAAK,YAAU,OAAO,IAAI,SAAO,KAAK,KAAK,QAAQ,KAAK,GAAG,CAAC,CAAC;KACjE;;EAQH,QACE,SACA,eACA,SAA+B;AAE/B,UAAMA,QAAO,MAAM,QAAQ,aAAa,IAAI,gBAAgB;AAC5D,cAAU,YAAYA,QAAO,SAAY;AACzC,UAAM,cAAc,UAAU,QAAQ,UAAU;AAEhD,WAAO,KAAK,OAAO,aAAa,WAAK;AACnC,YAAM,EAAC,MAAM,QAAO,IAAI,KAAK,OAAO;AACpC,UAAI,WAAWA;AACb,cAAM,IAAI,WAAW,gBAAgB,8DAA8D;AACrG,UAAIA,SAAQA,MAAK,WAAW,QAAQ;AAClC,cAAM,IAAI,WAAW,gBAAgB,sDAAsD;AAE7F,YAAM,aAAa,QAAQ;AAC3B,UAAI,eAAe,WAAW,OAC5B,QAAQ,IAAI,8BAA8B,OAAO,CAAC,IAClD;AACF,aAAO,KAAK,KAAK,OACf,EAAC,OAAO,MAAM,OAAO,MAAMA,OAAyB,QAAQ,cAAc,YAAW,CAAC,EAErF,KAAK,CAAC,EAAC,aAAa,SAAQ,YAAY,SAAQ,MAAC;AAChD,cAAM,SAAS,cAAc,UAAU;AACvC,YAAI,gBAAgB;AAAG,iBAAO;AAC9B,cAAM,IAAI,UACR,GAAG,KAAK,IAAI,eAAe,WAAW,OAAO,UAAU,sBAAsB,QAAQ;OACxF;KACJ;;EAQH,QACE,SACA,eACA,SAA+B;AAE/B,UAAMA,QAAO,MAAM,QAAQ,aAAa,IAAI,gBAAgB;AAC5D,cAAU,YAAYA,QAAO,SAAY;AACzC,UAAM,cAAc,UAAU,QAAQ,UAAU;AAEhD,WAAO,KAAK,OAAO,aAAa,WAAK;AACnC,YAAM,EAAC,MAAM,QAAO,IAAI,KAAK,OAAO;AACpC,UAAI,WAAWA;AACb,cAAM,IAAI,WAAW,gBAAgB,8DAA8D;AACrG,UAAIA,SAAQA,MAAK,WAAW,QAAQ;AAClC,cAAM,IAAI,WAAW,gBAAgB,sDAAsD;AAE7F,YAAM,aAAa,QAAQ;AAC3B,UAAI,eAAe,WAAW,OAC5B,QAAQ,IAAI,8BAA8B,OAAO,CAAC,IAClD;AAEF,aAAO,KAAK,KAAK,OACf,EAAC,OAAO,MAAM,OAAO,MAAMA,OAAyB,QAAQ,cAAc,YAAW,CAAC,EAErF,KAAK,CAAC,EAAC,aAAa,SAAS,YAAY,SAAQ,MAAC;AACjD,cAAM,SAAS,cAAc,UAAU;AACvC,YAAI,gBAAgB;AAAG,iBAAO;AAC9B,cAAM,IAAI,UACR,GAAG,KAAK,IAAI,eAAe,WAAW,OAAO,UAAU,sBAAsB,QAAQ;OACxF;KACJ;;EAQH,WAAWA,OAAkC;AAC3C,UAAM,UAAUA,MAAK;AACrB,WAAO,KAAK,OAAO,aAAa,WAAK;AACnC,aAAO,KAAK,KAAK,OAAO,EAAC,OAAO,MAAM,UAAU,MAAMA,MAAuB,CAAC;KAC/E,EAAE,KAAK,CAAC,EAAC,aAAa,YAAY,SAAQ,MAAC;AAC1C,UAAI,gBAAgB;AAAG,eAAO;AAC9B,YAAM,IAAI,UACR,GAAG,KAAK,IAAI,kBAAkB,WAAW,OAAO,OAAO,sBAAsB,QAAQ;KACxF;;;SC7emB,OAAO,KAAG;AAC9B,MAAI,MAAM,CAAA;AACV,MAAI,KAAK,SAAU,WAAW,YAAU;AACpC,QAAI,YAAY;AAEZ,UAAIC,KAAI,UAAU,QAAQ,OAAO,IAAI,MAAMA,KAAI,CAAC;AAChD,aAAO,EAAEA;AAAG,aAAKA,KAAI,CAAC,IAAI,UAAUA,EAAC;AACrC,UAAI,SAAS,EAAE,UAAU,MAAM,MAAM,IAAI;AACzC,aAAO;eACA,OAAQ,cAAe,UAAU;AAExC,aAAO,IAAI,SAAS;;;AAG5B,KAAG,eAAe;AAElB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,QAAI,UAAU,CAAC,CAAC;;AAGpB,SAAO;AAEP,WAAS,IAAI,WAAW,eAAe,iBAAe;AAClD,QAAI,OAAO,cAAc;AAAU,aAAO,oBAAoB,SAAS;AACvE,QAAI,CAAC;AAAe,sBAAgB;AACpC,QAAI,CAAC;AAAiB,wBAAkB;AAExC,QAAI,UAAU;MACV,aAAa,CAAA;MACb,MAAM;MACN,WAAW,SAAU,IAAE;AACnB,YAAI,QAAQ,YAAY,QAAQ,EAAE,MAAM,IAAI;AACxC,kBAAQ,YAAY,KAAK,EAAE;AAC3B,kBAAQ,OAAO,cAAc,QAAQ,MAAM,EAAE;;;MAGrD,aAAa,SAAU,IAAE;AACrB,gBAAQ,cAAc,QAAQ,YAAY,OAAO,SAAU,IAAE;AAAI,iBAAO,OAAO;QAAG,CAAE;AACpF,gBAAQ,OAAO,QAAQ,YAAY,OAAO,eAAe,eAAe;;;AAGhF,QAAI,SAAS,IAAI,GAAG,SAAS,IAAI;AACjC,WAAO;;AAGX,WAAS,oBAAoB,KAAG;AAE5B,SAAK,GAAG,EAAE,QAAQ,SAAU,WAAS;AACjC,UAAI,OAAO,IAAI,SAAS;AACxB,UAAI,QAAQ,IAAI,GAAG;AACf,YAAI,WAAW,IAAI,SAAS,EAAE,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC,CAAC;iBAC5C,SAAS,QAAQ;AAGxB,YAAI,UAAU,IAAI,WAAW,QAAQ,SAAS,OAAI;AAE9C,cAAIA,KAAI,UAAU,QAAQC,QAAO,IAAI,MAAMD,EAAC;AAC5C,iBAAOA;AAAK,YAAAC,MAAKD,EAAC,IAAI,UAAUA,EAAC;AAEjC,kBAAQ,YAAY,QAAQ,SAAU,IAAE;AACpCT,mBAAK,SAAS,YAAS;AACnB,iBAAG,MAAM,MAAMU,KAAI;aACtB;WACJ;SACJ;;AACE,cAAM,IAAI,WAAW,gBAAgB,sBAAsB;KACrE;;AAET;SCrEgB,qBAAoC,WAAmB,aAAqB;AAiB1F,SAAO,WAAW,EAAE,KAAK,EAAC,UAAS,CAAC;AACpC,SAAO;AACT;SCFgB,uBAAwB,IAAS;AAC/C,SAAO,qBACL,MAAM,WAEN,SAASC,OAAoB,MAAc,aAA0B,OAAmB;AACtF,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO,GAAG,WAAW,IAAI,IAAI,GAAG,WAAW,IAAI,EAAE,OAAO,OAAO,MAAM;MACxE,YAAY,CAAC,mBAAmB,GAAG;MACnC,WAAW,CAAC,mBAAmB,MAAM;MACrC,YAAY,CAAC,mBAAmB,GAAG;MACnC,YAAY,CAAC,mBAAmB,GAAG;KACpC;GACF;AAGL;SC5BgB,gBAAiB,KAAwB,mBAA2B;AAClF,SAAO,EAAE,IAAI,UAAU,IAAI,aAAa,IAAI,QACvC,oBAAoB,IAAI,YAAY,CAAC,IAAI;AAChD;SAEgB,UAAU,KAAwB,IAAY;AAC5D,MAAI,SAAS,QAAQ,IAAI,QAAQ,EAAE;AACrC;SAEgB,gBAAiB,KAAwB,SAAS,eAAc;AAC9E,MAAI,OAAO,IAAI;AACf,MAAI,eAAe,OAAO,MAAI,QAAQ,KAAI,GAAI,QAAO,CAAE,IAAI;AAC3D,MAAI,YAAY,iBAAiB,CAAC;AACpC;SAEgB,eAAe,KAAwB,IAAE;AACvD,MAAI,UAAU,QAAQ,IAAI,SAAS,EAAE;AACvC;SAEgB,gBAAgB,KAAwB,YAA6B;AAGnF,MAAI,IAAI;AAAW,WAAO,WAAW;AACrC,QAAM,QAAQ,WAAW,kBAAkB,IAAI,KAAK;AACpD,MAAI,CAAC;AAAO,UAAM,IAAI,WAAW,OAAO,aAAa,IAAI,QAAQ,sBAAsB,WAAW,OAAO,iBAAiB;AAC1H,SAAO;AACT;SAEgB,WAAW,KAAwB,WAAwB,OAAwB;AACjG,QAAM,QAAQ,gBAAgB,KAAK,UAAU,MAAM;AACnD,SAAO,UAAU,WAAW;IAC1B;IACA,QAAQ,CAAC,IAAI;IACb,SAAS,IAAI,QAAQ;IACrB,QAAQ,CAAC,CAAC,IAAI;IACd,OAAO;MACL;MACA,OAAO,IAAI;;GAEd;AACH;SAEgB,KACd,KACA,IACA,WACA,WAAsB;AAEtB,QAAM,SAAS,IAAI,eAAe,QAAQ,IAAI,QAAQ,IAAI,aAAY,CAAE,IAAI,IAAI;AAChF,MAAI,CAAC,IAAI,IAAI;AACT,WAAO,QACL,WAAW,KAAK,WAAW,SAAS,GACpC,QAAQ,IAAI,WAAW,MAAM,GAAG,IAAI,CAAC,IAAI,YAAY,IAAI,WAAW;SACnE;AACH,UAAM,MAAM,CAAA;AAEZ,UAAM,QAAQ,CAAC,MAAW,QAAsB,YAAO;AACnD,UAAI,CAAC,UAAU,OAAO,QAAQ,SAAS,YAAQ,OAAO,KAAK,MAAM,GAAG,SAAO,OAAO,KAAK,GAAG,CAAC,GAAG;AAC1F,YAAI,aAAa,OAAO;AACxB,YAAI,MAAM,KAAK;AACf,YAAI,QAAQ;AAAwB,gBAAM,KAAK,IAAI,WAAW,UAAU;AACxE,YAAI,CAAC,OAAO,KAAK,GAAG,GAAG;AACnB,cAAI,GAAG,IAAI;AACX,aAAG,MAAM,QAAQ,OAAO;;;;AAKpC,WAAO,QAAQ,IAAI;MACjB,IAAI,GAAG,SAAS,OAAO,SAAS;MAChC,QAAQ,WAAW,KAAK,WAAW,SAAS,GAAG,IAAI,WAAW,OAAO,CAAC,IAAI,YAAY,IAAI,WAAW;KACtG;;AAEP;AAEA,SAAS,QAAQ,eAAsC,QAAQ,IAAI,aAAW;AAG5E,MAAI,WAAW,cAAc,CAAC,GAAE,GAAE,MAAM,GAAG,YAAY,CAAC,GAAE,GAAE,CAAC,IAAI;AAEjE,MAAI,YAAY,KAAK,QAAQ;AAE7B,SAAO,cAAc,KAAK,YAAM;AAC9B,QAAI,QAAQ;AACV,aAAO,OAAO,MAAM,MAAA;AAClB,YAAI,IAAI,MAAI,OAAO,SAAQ;AAC3B,YAAI,CAAC,UAAU,OAAO,QAAQ,cAAY,IAAI,UAAU,SAAG;AAAG,iBAAO,KAAK,GAAG;AAAE,cAAE;QAAG,GAAG,OAAC;AAAK,iBAAO,KAAK,CAAC;AAAE,cAAI;QAAI,CAAC;AACnH,oBAAU,OAAO,OAAO,QAAQ,cAAY,IAAI,QAAQ;AAC1D,UAAC;OACF;;GAEJ;AACH;SCjGgB,IAAI,GAAQ,GAAM;AAChC,MAAI;AACF,UAAM,KAAK,KAAK,CAAC;AACjB,UAAM,KAAK,KAAK,CAAC;AACjB,QAAI,OAAO,IAAI;AACb,UAAI,OAAO;AAAS,eAAO;AAC3B,UAAI,OAAO;AAAS,eAAO;AAC3B,UAAI,OAAO;AAAU,eAAO;AAC5B,UAAI,OAAO;AAAU,eAAO;AAC5B,UAAI,OAAO;AAAU,eAAO;AAC5B,UAAI,OAAO;AAAU,eAAO;AAC5B,UAAI,OAAO;AAAQ,eAAO;AAC1B,UAAI,OAAO;AAAQ,eAAO;AAC1B,aAAO;;AAET,YAAQ,IAAE;MACR,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;MAClC,KAAK,UAAU;AACb,eAAO,mBAAmB,cAAc,CAAC,GAAG,cAAc,CAAC,CAAC;;MAE9D,KAAK;AACH,eAAO,cAAc,GAAG,CAAC;;WAE7B,IAAM;EAAA;AACR,SAAO;AACT;SAEgB,cAAc,GAAU,GAAQ;AAC9C,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,IAAI,KAAK,KAAK,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,MAAM,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1B,QAAI,QAAQ;AAAG,aAAO;;AAExB,SAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;AACxC;SAEgB,mBACd,GACA,GAAa;AAEb,QAAM,KAAK,EAAE;AACb,QAAM,KAAK,EAAE;AACb,QAAM,IAAI,KAAK,KAAK,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC;AAAG,aAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;;AAE/C,SAAO,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;AACxC;AAGA,SAAS,KAAK,GAAM;AAClB,QAAM,IAAI,OAAO;AACjB,MAAI,MAAM;AAAU,WAAO;AAC3B,MAAI,YAAY,OAAO,CAAC;AAAG,WAAO;AAClC,QAAM,QAAQ,YAAY,CAAC;AAC3B,SAAO,UAAU,gBAAgB,WAAY;AAC/C;AAgBA,SAAS,cAAc,GAAa;AAClC,MAAI,aAAa;AAAY,WAAO;AACpC,MAAI,YAAY,OAAO,CAAC;AAEtB,WAAO,IAAI,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU;AAC5D,SAAO,IAAI,WAAW,CAAC;AACzB;ICpEa,mBAAU;EAwBrB,MAAS,IAAwE,IAAG;AAClF,QAAI,MAAM,KAAK;AACf,WAAO,IAAI,QACT,IAAI,MAAM,OAAO,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,IACtD,IAAI,MAAM,OAAO,YAAY,EAAE,EAAE,KAAK,EAAE;;EAG5C,OAAU,IAAsE;AAC9E,QAAI,MAAM,KAAK;AACf,WAAO,IAAI,QACT,IAAI,MAAM,OAAO,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK,CAAC,IACtD,IAAI,MAAM,OAAO,aAAa,IAAI,QAAQ;;EAG9C,cAAc,IAAE;AACd,QAAI,MAAM,KAAK;AACf,QAAI,YAAY,QAAQ,IAAI,WAAW,EAAE;;EAG3C,SACE,IACA,WAA4B;AAE5B,WAAO,KAAK,KAAK,MAAM,IAAI,WAAW,KAAK,KAAK,MAAM,IAAI;;EAQ5D,MAAMN,QAAM;AACV,QAAI,KAAK,OAAO,OAAO,KAAK,YAAY,SAAS,GAC/C,MAAM,OAAO,OAAO,KAAK,IAAI;AAC/B,QAAIA;AAAO,aAAO,KAAKA,MAAK;AAC5B,OAAG,OAAO;AACV,WAAO;;EAQT,MAAG;AACD,SAAK,KAAK,cAAc;AACxB,WAAO;;EAQT,KAAK,IAAsC;AACzC,QAAI,MAAM,KAAK;AAEf,WAAO,KAAK,MAAM,WAAS,KAAK,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,CAAC;;EAQjE,MAAM,IAAG;AACP,WAAO,KAAK,MAAM,WAAK;AACrB,YAAM,MAAM,KAAK;AACjB,YAAM,YAAY,IAAI,MAAM;AAC5B,UAAI,gBAAgB,KAAK,IAAI,GAAG;AAE9B,eAAO,UAAU,MAAM;UACrB;UACA,OAAO;YACL,OAAO,gBAAgB,KAAK,UAAU,MAAM;YAC5C,OAAO,IAAI;;SAEd,EAAE,KAAK,CAAAO,WAAS,KAAK,IAAIA,QAAO,IAAI,KAAK,CAAC;aACtC;AAEL,YAAI,QAAQ;AACZ,eAAO,KAAK,KAAK,MAAA;AAAQ,YAAE;AAAO,iBAAO;QAAM,GAAI,OAAO,SAAS,EAClE,KAAK,MAAI,KAAK;;KAElB,EAAE,KAAK,EAAE;;EAUZ,OAAO,SAAiB,IAA6B;AACnD,UAAM,QAAQ,QAAQ,MAAM,GAAG,EAAE,QAAO,GACtC,WAAW,MAAM,CAAC,GAClB,YAAY,MAAM,SAAS;AAC7B,aAAS,OAAO,KAAK,GAAC;AACpB,UAAI;AAAG,eAAO,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;AACzC,aAAO,IAAI,QAAQ;;AAErB,QAAI,QAAQ,KAAK,KAAK,QAAQ,SAAS,IAAI;AAE3C,aAAS,OAAO,GAAG,GAAC;AAClB,UAAI,OAAO,OAAO,GAAG,SAAS,GAC5B,OAAO,OAAO,GAAG,SAAS;AAC5B,aAAO,OAAO,OAAO,CAAC,QAAQ,OAAO,OAAO,QAAQ;;AAEtD,WAAO,KAAK,QAAQ,SAAU,GAAC;AAC7B,aAAO,EAAE,KAAK,MAAM;KACrB,EAAE,KAAK,EAAE;;EAQZ,QAAQ,IAAG;AACT,WAAO,KAAK,MAAM,WAAK;AACrB,UAAI,MAAM,KAAK;AACf,UAAI,IAAI,QAAQ,UAAU,gBAAgB,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;AAGrE,cAAM,EAAC,YAAW,IAAI;AACtB,cAAM,QAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAK,MAAM;AACxD,eAAO,IAAI,MAAM,KAAK,MAAM;UAC1B;UACA,OAAO,IAAI;UACX,QAAQ;UACR,OAAO;YACL;YACA,OAAO,IAAI;;SAEd,EAAE,KAAK,CAAC,EAAC,OAAM,MAAM,cAAc,OAAO,IAAI,WAAW,IAAI,MAAM;aAC/D;AAEL,cAAM,IAAI,CAAA;AACV,eAAO,KAAK,KAAK,UAAQ,EAAE,KAAK,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,EAAE,KAAK,MAAI,CAAC;;OAEzE,EAAE;;EAQP,OAAO,QAAc;AACnB,QAAI,MAAM,KAAK;AACf,QAAI,UAAU;AAAG,aAAO;AACxB,QAAI,UAAU;AACd,QAAI,gBAAgB,GAAG,GAAG;AACxB,sBAAgB,KAAK,MAAA;AACnB,YAAI,aAAa;AACjB,eAAO,CAAC,QAAQ,YAAO;AACrB,cAAI,eAAe;AAAG,mBAAO;AAC7B,cAAI,eAAe,GAAG;AAAE,cAAE;AAAY,mBAAO;;AAC7C,kBAAQ,MAAA;AACN,mBAAO,QAAQ,UAAU;AACzB,yBAAa;WACd;AACD,iBAAO;;OAEV;WACI;AACL,sBAAgB,KAAK,MAAA;AACnB,YAAI,aAAa;AACjB,eAAO,MAAO,EAAE,aAAa;OAC9B;;AAEH,WAAO;;EAQT,MAAM,SAAe;AACnB,SAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,OAAO,OAAO;AACnD,oBAAgB,KAAK,MAAM,MAAA;AACzB,UAAI,WAAW;AACf,aAAO,SAAU,QAAQ,SAAS,SAAO;AACvC,YAAI,EAAE,YAAY;AAAG,kBAAQ,OAAO;AACpC,eAAO,YAAY;;OAEpB,IAAI;AACP,WAAO;;EAQT,MAAM,gBAAgC,mBAAkB;AACtD,cAAU,KAAK,MAAM,SAAU,QAAQ,SAAS,SAAO;AACrD,UAAI,eAAe,OAAO,KAAK,GAAG;AAChC,gBAAQ,OAAO;AACf,eAAO;aACF;AACL,eAAO;;KAEV;AACD,WAAO;;EAQT,MAAM,IAAG;AACP,WAAO,KAAK,MAAM,CAAC,EAAE,QAAQ,SAAU,GAAC;AAAI,aAAO,EAAE,CAAC;IAAE,CAAE,EAAE,KAAK,EAAE;;EAQrE,KAAK,IAAG;AACN,WAAO,KAAK,QAAO,EAAG,MAAM,EAAE;;EAQhC,OAAO,gBAA8B;AAEnC,cAAU,KAAK,MAAM,SAAU,QAAM;AACnC,aAAO,eAAe,OAAO,KAAK;KACnC;AAGD,mBAAe,KAAK,MAAM,cAAc;AACxC,WAAO;;EAQT,IAAI,QAAsB;AACxB,WAAO,KAAK,OAAO,MAAM;;EAQ3B,GAAG,WAAiB;AAClB,WAAO,IAAI,KAAK,GAAG,YAAY,KAAK,KAAK,OAAO,WAAW,IAAI;;EAQjE,UAAO;AACL,SAAK,KAAK,MAAO,KAAK,KAAK,QAAQ,SAAS,SAAS;AACrD,QAAI,KAAK;AAAoB,WAAK,mBAAmB,KAAK,KAAK,GAAG;AAClE,WAAO;;EAQT,OAAI;AACF,WAAO,KAAK,QAAO;;EAQrB,QAAQ,IAAG;AACT,QAAI,MAAM,KAAK;AACf,QAAI,WAAW,CAAC,IAAI;AACpB,WAAO,KAAK,KAAK,SAAU,KAAK,QAAM;AAAI,SAAG,OAAO,KAAK,MAAM;IAAE,CAAE;;EAQrE,cAAc,IAAG;AACf,SAAK,KAAK,SAAS;AACnB,WAAO,KAAK,QAAQ,EAAE;;EAQxB,eAAe,IAAG;AAChB,QAAI,MAAM,KAAK;AACf,QAAI,WAAW,CAAC,IAAI;AACpB,WAAO,KAAK,KAAK,SAAU,KAAK,QAAM;AAAI,SAAG,OAAO,YAAY,MAAM;IAAE,CAAE;;EAQ5E,KAAK,IAAG;AACN,QAAI,MAAM,KAAK;AACf,QAAI,WAAW,CAAC,IAAI;AACpB,QAAI,IAAI,CAAA;AACR,WAAO,KAAK,KAAK,SAAU,MAAM,QAAM;AACrC,QAAE,KAAK,OAAO,GAAG;KAClB,EAAE,KAAK,WAAA;AACN,aAAO;KACR,EAAE,KAAK,EAAE;;EAQZ,YAAY,IAAG;AACb,QAAI,MAAM,KAAK;AACf,QAAI,IAAI,QAAQ,UAAU,gBAAgB,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;AAGrE,aAAO,KAAK,MAAM,WAAK;AACrB,YAAI,QAAQ,gBAAgB,KAAK,IAAI,MAAM,KAAK,MAAM;AACtD,eAAO,IAAI,MAAM,KAAK,MAAM;UAC1B;UACA,QAAQ;UACR,OAAO,IAAI;UACX,OAAO;YACL;YACA,OAAO,IAAI;;SACX;OACL,EAAE,KAAK,CAAC,EAAC,OAAM,MAAI,MAAM,EAAE,KAAK,EAAE;;AAErC,QAAI,WAAW,CAAC,IAAI;AACpB,QAAI,IAAI,CAAA;AACR,WAAO,KAAK,KAAK,SAAU,MAAM,QAAM;AACrC,QAAE,KAAK,OAAO,UAAU;KACzB,EAAE,KAAK,WAAA;AACN,aAAO;KACR,EAAE,KAAK,EAAE;;EAQZ,WAAW,IAAG;AACZ,SAAK,KAAK,SAAS;AACnB,WAAO,KAAK,KAAK,EAAE;;EAQrB,SAAS,IAAG;AACV,WAAO,KAAK,MAAM,CAAC,EAAE,KAAK,SAAU,GAAC;AAAI,aAAO,EAAE,CAAC;IAAE,CAAE,EAAE,KAAK,EAAE;;EAQlE,QAAQ,IAAG;AACT,WAAO,KAAK,QAAO,EAAG,SAAS,EAAE;;EAQnC,WAAQ;AACN,QAAI,MAAM,KAAK,MACb,MAAM,IAAI,SAAS,IAAI,MAAM,OAAO,UAAU,IAAI,KAAK;AACzD,QAAI,CAAC,OAAO,CAAC,IAAI;AAAO,aAAO;AAC/B,QAAI,MAAM,CAAA;AACV,cAAU,KAAK,MAAM,SAAU,QAAoB;AACjD,UAAI,SAAS,OAAO,WAAW,SAAQ;AACvC,UAAI,QAAQ,OAAO,KAAK,MAAM;AAC9B,UAAI,MAAM,IAAI;AACd,aAAO,CAAC;KACT;AACD,WAAO;;EAaT,OAAO,SAA+E;AACpF,QAAI,MAAM,KAAK;AACf,WAAO,KAAK,OAAO,WAAK;AACtB,UAAI;AACJ,UAAI,OAAO,YAAY,YAAY;AAEjC,mBAAW;aACN;AAEL,YAAI,WAAW,KAAK,OAAO;AAC3B,YAAI,UAAU,SAAS;AACvB,mBAAW,SAAU,MAAI;AACvB,cAAI,mBAAmB;AACvB,mBAAS,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAChC,gBAAI,UAAU,SAAS,CAAC,GAAG,MAAM,QAAQ,OAAO;AAChD,gBAAI,aAAa,MAAM,OAAO,MAAM,KAAK;AACvC,2BAAa,MAAM,SAAS,GAAG;AAC/B,iCAAmB;;;AAGvB,iBAAO;;;AAIX,YAAM,YAAY,IAAI,MAAM;AAC5B,YAAM,EAAC,UAAU,WAAU,IAAI,UAAU,OAAO;AAChD,YAAM,QAAQ,KAAK,GAAG,SAAS,mBAAmB;AAClD,YAAM,gBAAgB,CAAA;AACtB,UAAI,eAAe;AACnB,YAAM,aAA8B,CAAA;AACpC,YAAM,oBAAoB,CAAC,eAAuB,QAAyB;AACzE,cAAM,EAAC,UAAU,YAAW,IAAI;AAChC,wBAAgB,gBAAgB;AAChC,iBAAS,OAAO,KAAK,QAAQ,GAAG;AAC9B,wBAAc,KAAK,SAAS,GAAG,CAAC;;;AAGpC,aAAO,KAAK,MAAK,EAAG,YAAW,EAAG,KAAK,CAAAJ,UAAI;AAEzC,cAAM,YAAY,CAAC,WAAc;AAC/B,gBAAM,QAAQ,KAAK,IAAI,OAAOA,MAAK,SAAS,MAAM;AAClD,iBAAO,UAAU,QAAQ;YACvB;YACA,MAAMA,MAAK,MAAM,QAAQ,SAAS,KAAK;YACvC,OAAO;WAIR,EAAE,KAAK,YAAM;AACZ,kBAAM,YAAY,CAAA;AAClB,kBAAM,YAAY,CAAA;AAClB,kBAAM,UAAU,WAAW,CAAA,IAAK;AAChC,kBAAM,aAAa,CAAA;AACnB,qBAAS,IAAE,GAAG,IAAE,OAAO,EAAE,GAAG;AAC1B,oBAAM,YAAY,OAAO,CAAC;AAC1B,oBAAMK,OAAM;gBACV,OAAO,UAAU,SAAS;gBAC1B,SAASL,MAAK,SAAO,CAAC;;AAExB,kBAAI,SAAS,KAAKK,MAAKA,KAAI,OAAOA,IAAG,MAAM,OAAO;AAChD,oBAAIA,KAAI,SAAS,MAAM;AAErB,6BAAW,KAAKL,MAAK,SAAO,CAAC,CAAC;2BACrB,CAAC,YAAY,IAAI,WAAW,SAAS,GAAG,WAAWK,KAAI,KAAK,CAAC,MAAM,GAAG;AAE/E,6BAAW,KAAKL,MAAK,SAAO,CAAC,CAAC;AAC9B,4BAAU,KAAKK,KAAI,KAAK;uBACnB;AAEL,4BAAU,KAAKA,KAAI,KAAK;AACxB,sBAAI;AAAU,4BAAQ,KAAKL,MAAK,SAAO,CAAC,CAAC;;;;AAI/C,kBAAM,WAAW,gBAAgB,GAAG,KAClC,IAAI,UAAU,aACb,OAAO,YAAY,cAAc,YAAY,mBAAmB;cAC/D,OAAO,IAAI;cACX,OAAO,IAAI;;AAGf,mBAAO,QAAQ,QAAQ,UAAU,SAAS,KACxC,UAAU,OAAO,EAAC,OAAO,MAAM,OAAO,QAAQ,UAAS,CAAC,EACrD,KAAK,SAAG;AACP,uBAAS,OAAO,IAAI,UAAU;AAE5B,2BAAW,OAAO,SAAS,GAAG,GAAG,CAAC;;AAEpC,gCAAkB,UAAU,QAAQ,GAAG;aACxC,CAAC,EACJ,KAAK,OAAK,UAAU,SAAS,KAAM,YAAY,OAAO,YAAY,aAChE,UAAU,OAAO;cACf;cACA,MAAM;cACN,MAAM;cACN,QAAQ;cACR;cACA,YAAY,OAAO,YAAY,cAC1B;aACN,EAAE,KAAK,SAAK,kBAAkB,UAAU,QAAQ,GAAG,CAAC,CAAC,EACxD,KAAK,OAAK,WAAW,SAAS,KAAM,YAAY,YAAY,mBAC1D,UAAU,OAAO;cACf;cACA,MAAM;cACN,MAAM;cACN;aACD,EAAE,KAAK,SAAK,kBAAkB,WAAW,QAAQ,GAAG,CAAC,CAAC,EACzD,KAAK,MAAA;AACL,qBAAOA,MAAK,SAAS,SAAS,SAAS,UAAU,SAAS,KAAK;aAChE;WACF;;AAGH,eAAO,UAAU,CAAC,EAAE,KAAK,MAAA;AACvB,cAAI,cAAc,SAAS;AACzB,kBAAM,IAAI,YAAY,uCAAuC,eAAe,cAAc,UAAwC;AAEpI,iBAAOA,MAAK;SACb;OACF;KAEF;;EAQH,SAAM;AACJ,QAAI,MAAM,KAAK,MACb,QAAQ,IAAI;AAGd,QAAI,gBAAgB,GAAG,MACnB,IAAI,aAAa,CAAC,8BAA+B,MAAM,SAAI,IAC/D;AAKE,aAAO,KAAK,OAAO,WAAK;AAEtB,cAAM,EAAC,WAAU,IAAI,IAAI,MAAM,KAAK;AACpC,cAAM,YAAY;AAClB,eAAO,IAAI,MAAM,KAAK,MAAM,EAAC,OAAO,OAAO,EAAC,OAAO,YAAY,OAAO,UAAS,EAAC,CAAC,EAAE,KAAK,WAAK;AAC3F,iBAAO,IAAI,MAAM,KAAK,OAAO,EAAC,OAAO,MAAM,eAAe,OAAO,UAAS,CAAC,EAC1E,KAAK,CAAC,EAAC,UAAU,YAAY,SAAS,YAAW,MAAC;AACjD,gBAAI;AAAa,oBAAM,IAAI,YAAY,gCACrC,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAO,SAAS,GAAG,CAAC,GAC9C,QAAQ,WAAW;AACrB,mBAAO,QAAQ;WAChB;SACF;OACF;;AAGH,WAAO,KAAK,OAAO,cAAc;;;AAIrC,IAAM,iBAAiB,CAAC,OAAO,QAAQ,IAAI,QAAQ;SC1lBnC,4BAA4B,IAAS;AACnD,SAAO,qBACL,WAAW,WAEX,SAASM,YAEP,aACA,mBAAwC;AAExC,SAAK,KAAK;AACV,QAAI,WAAW,UAAU,QAAQ;AACjC,QAAI;AAAmB,UAAI;AACzB,mBAAW,kBAAiB;eACrB,IAAI;AACX,gBAAQ;;AAGV,UAAM,WAAW,YAAY;AAC7B,UAAM,QAAQ,SAAS;AACvB,UAAM,cAAc,MAAM,KAAK,QAAQ;AACvC,SAAK,OAAO;MACV;MACA,OAAO,SAAS;MAChB,WAAY,CAAC,SAAS,SAAU,MAAM,OAAO,QAAQ,WAAW,SAAS,UAAU,MAAM,OAAO,QAAQ;MACxG,OAAO;MACP,UAAU;MACV,KAAK;MACL,QAAQ;MACR,WAAW;MACX,QAAQ;MACR,cAAc;MACd,WAAW;MACX,SAAS;MACT,QAAQ;MACR,OAAO;MACP;MACA,IAAI,SAAS;MACb,aAAa,gBAAgB,SAAS,cAAc;;GAEvD;AAEL;SC3DgB,cAAc,GAAG,GAAC;AAChC,SAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AACpC;SAEgB,qBAAqB,GAAG,GAAC;AACvC,SAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AACpC;SCDgB,KAAK,yBAAmD,KAAK,GAAE;AAC7E,MAAI,aAAa,mCAAmC,cAChD,IAAI,wBAAwB,WAAY,uBAAuB,IAC/D;AAEJ,aAAW,KAAK,QAAQ,IAAI,IAAI,EAAE,GAAG,IAAI,IAAI,UAAU,GAAG;AAC1D,SAAO;AACT;SAEgB,gBAAgB,aAAwB;AACtD,SAAO,IAAI,YAAY,WAAY,aAAa,MAAM,WAAW,EAAE,CAAC,EAAE,MAAM,CAAC;AAC/E;SAEgB,aAAa,KAAoB;AAC/C,SAAO,QAAQ,SACb,CAAC,MAAc,EAAE,YAAW,IAC5B,CAAC,MAAc,EAAE,YAAW;AAChC;SAEgB,aAAa,KAAoB;AAC/C,SAAO,QAAQ,SACb,CAAC,MAAc,EAAE,YAAW,IAC5B,CAAC,MAAc,EAAE,YAAW;AAChC;SAEgB,WAAW,KAAK,UAAU,aAAa,aAAaC,MAAK,KAAG;AAC1E,MAAI,SAAS,KAAK,IAAI,IAAI,QAAQ,YAAY,MAAM;AACpD,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC7B,QAAI,aAAa,SAAS,CAAC;AAC3B,QAAI,eAAe,YAAY,CAAC,GAAG;AAC/B,UAAIA,KAAI,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI;AAAG,eAAO,IAAI,OAAO,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,OAAO,IAAI,CAAC;AACxG,UAAIA,KAAI,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,IAAI;AAAG,eAAO,IAAI,OAAO,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,YAAY,OAAO,IAAI,CAAC;AACxG,UAAI,OAAO;AAAG,eAAO,IAAI,OAAO,GAAG,GAAG,IAAI,SAAS,GAAG,IAAI,YAAY,OAAO,MAAM,CAAC;AACpF,aAAO;;AAEX,QAAIA,KAAI,IAAI,CAAC,GAAG,UAAU,IAAI;AAAG,YAAM;;AAE3C,MAAI,SAAS,YAAY,UAAU,QAAQ;AAAQ,WAAO,MAAM,YAAY,OAAO,IAAI,MAAM;AAC7F,MAAI,SAAS,IAAI,UAAU,QAAQ;AAAQ,WAAO,IAAI,OAAO,GAAG,YAAY,MAAM;AAClF,SAAQ,MAAM,IAAI,OAAO,IAAI,OAAO,GAAG,GAAG,IAAI,YAAY,GAAG,IAAI,YAAY,OAAO,MAAM,CAAC;AAC7F;SAEgB,uBAAuB,aAA0B,OAAO,SAAS,QAAM;AAErF,MAAI,OAAO,OAAO,SAAS,cAAc,cAAc,WAAW,eAC9D,aAAa,QAAQ;AACzB,MAAI,CAAC,QAAQ,MAAM,OAAK,OAAO,MAAM,QAAQ,GAAG;AAC5C,WAAO,KAAK,aAAa,eAAe;;AAE5C,WAAS,cAAc,KAAG;AACtB,YAAQ,aAAa,GAAG;AACxB,YAAQ,aAAa,GAAG;AACxB,cAAW,QAAQ,SAAS,gBAAgB;AAC5C,QAAI,eAAe,QAAQ,IAAI,SAAU,QAAM;AAC3C,aAAO,EAAC,OAAO,MAAM,MAAM,GAAG,OAAO,MAAM,MAAM,EAAC;KACrD,EAAE,KAAK,SAAS,GAAE,GAAC;AAChB,aAAO,QAAQ,EAAE,OAAO,EAAE,KAAK;KAClC;AACD,mBAAe,aAAa,IAAI,SAAU,IAAE;AAAG,aAAO,GAAG;IAAM,CAAE;AACjE,mBAAe,aAAa,IAAI,SAAU,IAAE;AAAG,aAAO,GAAG;IAAM,CAAE;AACjE,gBAAY;AACZ,oBAAiB,QAAQ,SAAS,KAAK;;AAE3C,gBAAc,MAAM;AAEpB,MAAI,IAAI,IAAI,YAAY,WACpB,aACA,MAAI,YAAY,aAAa,CAAC,GAAG,aAAa,aAAW,CAAC,IAAI,MAAM,CAAC;AAGzE,IAAE,qBAAqB,SAAUC,YAAS;AAEtC,kBAAcA,UAAS;;AAG3B,MAAI,sBAAsB;AAE1B,IAAE,cAAc,SAAU,QAAQ,SAAS,SAAO;AAI9C,QAAI,MAAM,OAAO;AACjB,QAAI,OAAO,QAAQ;AAAU,aAAO;AACpC,QAAI,WAAW,MAAM,GAAG;AACxB,QAAI,MAAM,UAAU,cAAc,mBAAmB,GAAG;AACpD,aAAO;WACJ;AACH,UAAI,uBAAuB;AAC3B,eAAS,IAAE,qBAAqB,IAAE,YAAY,EAAE,GAAG;AAC/C,YAAI,SAAS,WAAW,KAAK,UAAU,aAAa,CAAC,GAAG,aAAa,CAAC,GAAG,SAAS,SAAS;AAC3F,YAAI,WAAW,QAAQ,yBAAyB;AAC5C,gCAAsB,IAAI;iBACrB,yBAAyB,QAAQ,QAAQ,sBAAsB,MAAM,IAAI,GAAG;AACjF,iCAAuB;;;AAG/B,UAAI,yBAAyB,MAAM;AAC/B,gBAAQ,WAAA;AAAc,iBAAO,SAAS,uBAAuB,aAAa;QAAE,CAAE;aAC3E;AACH,gBAAQ,OAAO;;AAEnB,aAAO;;GAEd;AACD,SAAO;AACT;SAEgB,YAAa,OAAsB,OAAsB,WAAqB,WAAmB;AAC7G,SAAO;IACH,MAAI;IACJ;IACA;IACA;IACA;;AAER;SAEgB,WAAY,OAAoB;AAC5C,SAAO;IACH,MAAI;IACJ,OAAO;IACP,OAAO;;AAEf;ICpHa,oBAAW;EActB,IAAI,aAAU;AACZ,WAAO,KAAK,KAAK,MAAM,GAAG;;EAQ5B,QAAQ,OAAsB,OAAsB,cAAwB,cAAsB;AAChG,mBAAe,iBAAiB;AAChC,mBAAe,iBAAiB;AAChC,QAAI;AACF,UAAK,KAAK,KAAK,OAAO,KAAK,IAAI,KAC5B,KAAK,KAAK,OAAO,KAAK,MAAM,MAAM,gBAAgB,iBAAiB,EAAE,gBAAgB;AACtF,eAAO,gBAAgB,IAAI;AAC7B,aAAO,IAAI,KAAK,WAAW,MAAM,MAAI,YAAY,OAAO,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC;aACrF,GAAG;AACV,aAAO,KAAK,MAAM,oBAAoB;;;EAS1C,OAAO,OAAoB;AACzB,QAAI,SAAS;AAAM,aAAO,KAAK,MAAM,oBAAoB;AACzD,WAAO,IAAI,KAAK,WAAW,MAAM,MAAM,WAAW,KAAK,CAAC;;EAQ1D,MAAM,OAAoB;AACxB,QAAI,SAAS;AAAM,aAAO,KAAK,MAAM,oBAAoB;AACzD,WAAO,IAAI,KAAK,WAAW,MAAM,MAAM,YAAY,OAAO,QAAW,IAAI,CAAC;;EAQ5E,aAAa,OAAoB;AAC/B,QAAI,SAAS;AAAM,aAAO,KAAK,MAAM,oBAAoB;AACzD,WAAO,IAAI,KAAK,WAAW,MAAM,MAAM,YAAY,OAAO,QAAW,KAAK,CAAC;;EAQ7E,MAAM,OAAoB;AACxB,QAAI,SAAS;AAAM,aAAO,KAAK,MAAM,oBAAoB;AACzD,WAAO,IAAI,KAAK,WAAW,MAAM,MAAM,YAAY,QAAW,OAAO,OAAO,IAAI,CAAC;;EAQnF,aAAa,OAAoB;AAC/B,QAAI,SAAS;AAAM,aAAO,KAAK,MAAM,oBAAoB;AACzD,WAAO,IAAI,KAAK,WAAW,MAAM,MAAM,YAAY,QAAW,KAAK,CAAC;;EAQtE,WAAW,KAAW;AACpB,QAAI,OAAO,QAAQ;AAAU,aAAO,KAAK,MAAM,eAAe;AAC9D,WAAO,KAAK,QAAQ,KAAK,MAAM,WAAW,MAAM,IAAI;;EAQtD,qBAAqB,KAAW;AAC9B,QAAI,QAAQ;AAAI,aAAO,KAAK,WAAW,GAAG;AAC1C,WAAO,uBAAuB,MAAM,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,SAAS;;EAQvF,iBAAiB,KAAW;AAC1B,WAAO,uBAAuB,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE;;EAUrE,kBAAe;AACb,QAAI,MAAM,WAAW,MAAM,eAAe,SAAS;AACnD,QAAI,IAAI,WAAW;AAAG,aAAO,gBAAgB,IAAI;AACjD,WAAO,uBAAuB,MAAM,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK,EAAE;;EAU5E,4BAAyB;AACvB,QAAI,MAAM,WAAW,MAAM,eAAe,SAAS;AACnD,QAAI,IAAI,WAAW;AAAG,aAAO,gBAAgB,IAAI;AACjD,WAAO,uBAAuB,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,OAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,SAAS;;EAU/F,QAAK;AACH,UAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AACrD,QAAI,UAAU,KAAK;AACnB,QAAI;AAAE,UAAI,KAAK,OAAO;aAAY,GAAG;AAAE,aAAO,KAAK,MAAM,oBAAoB;;AAC7E,QAAI,IAAI,WAAW;AAAG,aAAO,gBAAgB,IAAI;AACjD,UAAM,IAAI,IAAI,KAAK,WAAW,MAAM,MAAM,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC;AAElF,MAAE,qBAAqB,eAAS;AAC9B,gBAAW,cAAc,SACvB,KAAK,aACL,KAAK;AACP,UAAI,KAAK,OAAO;;AAGlB,QAAI,IAAI;AACR,MAAE,cAAc,CAAC,QAAQ,SAAS,YAAO;AACvC,YAAM,MAAM,OAAO;AACnB,aAAO,QAAQ,KAAK,IAAI,CAAC,CAAC,IAAI,GAAG;AAE/B,UAAE;AACF,YAAI,MAAM,IAAI,QAAQ;AAEpB,kBAAQ,OAAO;AACf,iBAAO;;;AAGX,UAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,MAAM,GAAG;AAE9B,eAAO;aACF;AAEL,gBAAQ,MAAA;AAAQ,iBAAO,SAAS,IAAI,CAAC,CAAC;QAAE,CAAE;AAC1C,eAAO;;KAEV;AACD,WAAO;;EAQT,SAAS,OAAoB;AAC3B,WAAO,KAAK,WAAW,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,eAAe,OAAO,eAAe,MAAK,CAAE;;EAUpH,SAAM;AACJ,UAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AACrD,QAAI,IAAI,WAAW;AAAG,aAAO,IAAI,KAAK,WAAW,IAAI;AACrD,QAAI;AAAE,UAAI,KAAK,KAAK,UAAU;aAAY,GAAG;AAAE,aAAO,KAAK,MAAM,oBAAoB;;AAErF,UAAM,SAAS,IAAI,OACjB,CAAC,KAAK,QAAQ,MACZ,IAAI,OAAO,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAC1C,CAAC,CAAC,QAAQ,GAAG,CAAC,GAChB,IAAI;AACN,WAAO,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC;AAClD,WAAO,KAAK,WAAW,QAAQ,EAAE,eAAe,OAAO,eAAe,MAAK,CAAE;;EAQ/E,WACE,QACA,SAA8D;AAE9D,UAAMD,OAAM,KAAK,MACX,YAAY,KAAK,YACjB,aAAa,KAAK,aAClB,MAAM,KAAK,MACX,MAAM,KAAK;AAEjB,QAAI,OAAO,WAAW;AAAG,aAAO,gBAAgB,IAAI;AACpD,QAAI,CAAC,OAAO,MAAM,WAChB,MAAM,CAAC,MAAM,UACb,MAAM,CAAC,MAAM,UACb,UAAU,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG;AACrC,aAAO,KACL,MACA,8HACA,WAAW,eAAe;;AAE9B,UAAM,gBAAgB,CAAC,WAAW,QAAQ,kBAAkB;AAC5D,UAAM,gBAAgB,WAAW,QAAQ,kBAAkB;AAE3D,aAASE,UAASC,SAAQ,UAAQ;AAChC,UAAI,IAAI,GAAG,IAAIA,QAAO;AACtB,aAAO,IAAI,GAAG,EAAE,GAAG;AACjB,cAAM,QAAQA,QAAO,CAAC;AACtB,YAAIH,KAAI,SAAS,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,KAAKA,KAAI,SAAS,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG;AACpE,gBAAM,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;AACpC,gBAAM,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;AACpC;;;AAGJ,UAAI,MAAM;AACR,QAAAG,QAAO,KAAK,QAAQ;AACtB,aAAOA;;AAGT,QAAI,gBAAgB;AACpB,aAAS,YAAY,GAAG,GAAC;AAAI,aAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAAE;AAG9D,QAAI;AACJ,QAAI;AACF,YAAM,OAAO,OAAOD,WAAU,CAAA,CAAE;AAChC,UAAI,KAAK,WAAW;aACb,IAAI;AACX,aAAO,KAAK,MAAM,oBAAoB;;AAGxC,QAAI,WAAW;AACf,UAAM,0BAA0B,gBAC9B,SAAO,UAAU,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,IAAI,IAC1C,SAAO,UAAU,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,KAAK;AAE7C,UAAM,0BAA0B,gBAC9B,SAAO,WAAW,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,IAAI,IAC3C,SAAO,WAAW,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,KAAK;AAE9C,aAAS,sBAAsB,KAAG;AAChC,aAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC,wBAAwB,GAAG;;AAGtE,QAAI,WAAW;AAEf,UAAM,IAAI,IAAI,KAAK,WACjB,MACA,MAAM,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC;AAEtF,MAAE,qBAAqB,eAAS;AAC9B,UAAI,cAAc,QAAQ;AACxB,mBAAW;AACX,wBAAgB;aACX;AACL,mBAAW;AACX,wBAAgB;;AAElB,UAAI,KAAK,WAAW;;AAGtB,MAAE,cAAc,CAAC,QAAQ,SAAS,YAAO;AACvC,UAAI,MAAM,OAAO;AACjB,aAAO,SAAS,GAAG,GAAG;AAEpB,UAAE;AACF,YAAI,aAAa,IAAI,QAAQ;AAE3B,kBAAQ,OAAO;AACf,iBAAO;;;AAGX,UAAI,sBAAsB,GAAG,GAAG;AAE9B,eAAO;iBACE,KAAK,KAAK,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,KAAK,KAAK,KAAK,KAAK,IAAI,QAAQ,EAAE,CAAC,CAAC,MAAM,GAAG;AAG3F,eAAO;aACF;AAEL,gBAAQ,MAAA;AACN,cAAI,kBAAkB;AAAW,mBAAO,SAAS,IAAI,QAAQ,EAAE,CAAC,CAAC;;AAC5D,mBAAO,SAAS,IAAI,QAAQ,EAAE,CAAC,CAAC;SACtC;AACD,eAAO;;KAEV;AACD,WAAO;;EAUT,kBAAe;AACb,UAAM,MAAM,WAAW,MAAM,eAAe,SAAS;AAErD,QAAI,CAAC,IAAI,MAAM,OAAK,OAAO,MAAM,QAAQ,GAAG;AACxC,aAAO,KAAK,MAAM,2CAA2C;;AAEjE,QAAI,IAAI,WAAW;AAAG,aAAO,gBAAgB,IAAI;AAEjD,WAAO,KAAK,WAAW,IAAI,IAAI,CAAC,QAAgB,CAAC,KAAK,MAAM,SAAS,CAAC,CAAC;;;SCvV3D,6BAA6B,IAAS;AACpD,SAAO,qBACL,YAAY,WAEZ,SAASE,aAA+B,OAAc,OAAgB,cAAyB;AAC7F,SAAK,KAAK;AACV,SAAK,OAAO;MACV;MACA,OAAO,UAAU,QAAQ,OAAO;MAChC,IAAI;;AAEN,UAAMC,aAAY,GAAG,MAAM;AAC3B,QAAI,CAACA;AAAW,YAAM,IAAI,WAAW,WAAU;AAC/C,SAAK,OAAO,KAAK,aAAaA,WAAU,IAAI,KAAKA,UAAS;AAC1D,SAAK,cAAc,CAAC,GAAG,MAAMA,WAAU,IAAI,GAAG,CAAC;AAC/C,SAAK,OAAO,CAAC,GAAG,MAAMA,WAAU,IAAI,GAAE,CAAC,IAAI,IAAI,IAAI;AACnD,SAAK,OAAO,CAAC,GAAG,MAAMA,WAAU,IAAI,GAAE,CAAC,IAAI,IAAI,IAAI;AACnD,SAAK,eAAe,GAAG,MAAM;GAC9B;AAEL;SCpCgB,mBAAmB,QAAM;AACvC,SAAO,KAAK,SAAU,OAAK;AACvB,mBAAe,KAAK;AACpB,WAAQ,MAAM,OAAO,KAAK;AAC1B,WAAO;GACV;AACH;SA4CgB,eAAe,OAAK;AAClC,MAAI,MAAM;AACN,UAAM,gBAAe;AACzB,MAAI,MAAM;AACN,UAAM,eAAc;AAC1B;ACtDO,IAAM,mCAAmC;AAazC,IAAM,iCAAiC;AAEvC,IAAM,eAAe,OAAO,MAAM,gCAAgC;ICC5D,oBAAW;EA6BtB,QAAK;AACH,WAAO,CAAC,IAAI,MAAM;AAElB,MAAE,KAAK;AACP,QAAI,KAAK,cAAc,KAAK,CAAC,IAAI;AAAQ,UAAI,eAAe;AAC5D,WAAO;;EAOT,UAAO;AACL,WAAO,CAAC,IAAI,MAAM;AAClB,QAAI,EAAE,KAAK,cAAc,GAAG;AAC1B,UAAI,CAAC,IAAI;AAAQ,YAAI,eAAe;AACpC,aAAO,KAAK,cAAc,SAAS,KAAK,CAAC,KAAK,QAAO,GAAI;AACvD,YAAI,WAAW,KAAK,cAAc,MAAK;AACvC,YAAI;AAAE,iBAAO,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;iBAAY,GAAG;QAAA;;;AAGxD,WAAO;;EAOT,UAAO;AAWL,WAAO,KAAK,aAAa,IAAI,iBAAiB;;EAQhD,OAAO,UAAyB;AAC9B,QAAI,CAAC,KAAK;AAAM,aAAO;AACvB,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,cAAc,KAAK,GAAG,OAAO;AACnC,WAAO,CAAC,KAAK,QAAQ;AACrB,QAAI,CAAC,YAAY,CAAC,OAAO;AACvB,cAAQ,eAAe,YAAY,MAAI;QACrC,KAAK;AAEH,gBAAM,IAAI,WAAW,eAAe,WAAW;QACjD,KAAK;AAEH,gBAAM,IAAI,WAAW,WAAW,YAAY,SAAS,WAAW;QAClE;AAEE,gBAAM,IAAI,WAAW,WAAW,WAAW;;;AAGjD,QAAI,CAAC,KAAK;AAAQ,YAAM,IAAI,WAAW,oBAAmB;AAC1D,WAAO,KAAK,YAAY,WAAW,IAAI;AAEvC,eAAW,KAAK,WAAW,aACxB,KAAK,GAAG,OACL,KAAK,GAAG,KAAK,YAAY,KAAK,YAAY,KAAK,MAAkC,EAAE,YAAY,KAAK,4BAA2B,CAAE,IACjI,MAAM,YAAY,KAAK,YAAY,KAAK,MAAM,EAAE,YAAY,KAAK,4BAA2B,CAAE;AAGpG,aAAS,UAAU,KAAK,QAAE;AACxB,qBAAe,EAAE;AACjB,WAAK,QAAQ,SAAS,KAAK;KAC5B;AACD,aAAS,UAAU,KAAK,QAAE;AACxB,qBAAe,EAAE;AACjB,WAAK,UAAU,KAAK,QAAQ,IAAI,WAAW,MAAM,SAAS,KAAK,CAAC;AAChE,WAAK,SAAS;AACd,WAAK,GAAG,OAAO,EAAE,KAAK,EAAE;KACzB;AACD,aAAS,aAAa,KAAK,MAAA;AACzB,WAAK,SAAS;AACd,WAAK,SAAQ;AACb,UAAI,kBAAkB,UAAU;AAC9B,qBAAa,eAAe,KAAK,SAAS,cAAc,CAAC;;KAE5D;AACD,WAAO;;EAOT,SACE,MACA,IACA,YAA6B;AAE7B,QAAI,SAAS,eAAe,KAAK,SAAS;AACxC,aAAO,UAAU,IAAI,WAAW,SAAS,yBAAyB,CAAC;AAErE,QAAI,CAAC,KAAK;AACR,aAAO,UAAU,IAAI,WAAW,oBAAmB,CAAE;AAEvD,QAAI,KAAK,QAAO,GAAI;AAClB,aAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACtC,aAAK,cAAc,KAAK,CAAC,MAAA;AACvB,eAAK,SAAS,MAAM,IAAI,UAAU,EAAE,KAAK,SAAS,MAAM;WACvD,GAAG,CAAC;OACR;eAEQ,YAAY;AACrB,aAAO,SAAS,MAAA;AACd,YAAIC,KAAI,IAAI,aAAa,CAAC,SAAS,WAAM;AACvC,eAAK,MAAK;AACV,gBAAM,KAAK,GAAG,SAAS,QAAQ,IAAI;AACnC,cAAI,MAAM,GAAG;AAAM,eAAG,KAAK,SAAS,MAAM;SAC3C;AACD,QAAAA,GAAE,QAAQ,MAAM,KAAK,QAAO,CAAE;AAC9B,QAAAA,GAAE,OAAO;AACT,eAAOA;OACR;WAEI;AACL,UAAI,IAAI,IAAI,aAAa,CAAC,SAAS,WAAM;AACvC,YAAI,KAAK,GAAG,SAAS,QAAQ,IAAI;AACjC,YAAI,MAAM,GAAG;AAAM,aAAG,KAAK,SAAS,MAAM;OAC3C;AACD,QAAE,OAAO;AACT,aAAO;;;EAQX,QAAK;AACH,WAAO,KAAK,SAAS,KAAK,OAAO,MAAK,IAAK;;EAS7C,QAAQ,aAA6B;AAEnC,QAAI,OAAO,KAAK,MAAK;AAGrB,UAAM,UAAU,aAAa,QAAQ,WAAW;AAChD,QAAI,KAAK,aAAa;AAEpB,WAAK,cAAc,KAAK,YAAY,KAAK,MAAM,OAAO;WACjD;AAEL,WAAK,cAAc;AACnB,WAAK,gBAAgB,CAAA;AAErB,UAAI,QAAQ,KAAK,SAAS,YAAY,KAAK,WAAW,CAAC,CAAC;AACxD,OAAC,SAAS,OAAI;AACZ,UAAE,KAAK;AACP,eAAO,KAAK,cAAc;AAAQ,UAAC,KAAK,cAAc,MAAK,EAAE;AAC7D,YAAI,KAAK;AAAa,gBAAM,IAAI,SAAS,EAAE,YAAY;SACxD;;AAEH,QAAI,qBAAqB,KAAK;AAC9B,WAAO,IAAI,aAAa,CAAC,SAAS,WAAM;AACtC,cAAQ,KACN,SAAO,KAAK,cAAc,KAAK,KAAK,QAAQ,KAAK,MAAM,GAAG,CAAC,CAAC,GAC5D,SAAO,KAAK,cAAc,KAAK,KAAK,OAAO,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC,EAC5D,QAAQ,MAAA;AACR,YAAI,KAAK,gBAAgB,oBAAoB;AAE3C,eAAK,cAAc;;OAEtB;KACF;;EAOH,QAAK;AACH,QAAI,KAAK,QAAQ;AACf,WAAK,SAAS;AACd,UAAI,KAAK;AAAU,aAAK,SAAS,MAAK;AACtC,WAAK,QAAQ,IAAI,WAAW,MAAK,CAAE;;;EAQvC,MAAM,WAAiB;AACrB,UAAM,iBAAkB,KAAK,oBAAoB,KAAK,kBAAkB,CAAA;AACxE,QAAI,OAAO,gBAAgB,SAAS;AAClC,aAAO,eAAe,SAAS;AACjC,UAAM,cAAc,KAAK,OAAO,SAAS;AACzC,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,WAAW,SAAS,WAAW,YAAY,0BAA0B;;AAGjF,UAAM,wBAAwB,IAAI,KAAK,GAAG,MAAM,WAAW,aAAa,IAAI;AAC5E,0BAAsB,OAAO,KAAK,GAAG,KAAK,MAAM,SAAS;AACzD,mBAAe,SAAS,IAAI;AAC5B,WAAO;;;SChPK,6BAA6B,IAAS;AACpD,SAAO,qBACL,YAAY,WACZ,SAASC,aAEP,MACA,YACA,UACA,6BACA,QAAoB;AAEpB,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,8BAA8B;AACnC,SAAK,WAAW;AAChB,SAAK,KAAK,OAAO,MAAM,YAAY,SAAS,OAAO;AACnD,SAAK,SAAS,UAAU;AACxB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,gBAAgB,CAAA;AACrB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc,IAAIf,aAAS,CAAC,SAAS,WAAM;AAC5C,WAAK,WAAW;AAChB,WAAK,UAAU;KAClB;AAED,SAAK,YAAY,KACb,MAAA;AACI,WAAK,SAAS;AACd,WAAK,GAAG,SAAS,KAAI;OAEzB,OAAC;AACG,UAAI,YAAY,KAAK;AACrB,WAAK,SAAS;AACd,WAAK,GAAG,MAAM,KAAK,CAAC;AACpB,WAAK,SACD,KAAK,OAAO,QAAQ,CAAC,IACrB,aAAa,KAAK,YAAY,KAAK,SAAS,MAAK;AACrD,aAAO,UAAU,CAAC;KACrB;GAEN;AACL;SCrEgB,gBACd,MACA,SACA,QACA,OACA,MACA,UACA,WAAkB;AAElB,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,UAAU,CAAC,YAAY,MAAM,OAAO,QAAQ,MAAM,OAAO,OAAO,OAAO,MAAM,gBAAgB,OAAO;;AAE9G;SAEgB,gBAAiB,SAA2B;AAC1D,SAAO,OAAO,YAAY,WACxB,UACA,UAAW,MAAM,CAAA,EAAG,KAAK,KAAK,SAAS,GAAG,IAAI,MAAO;AACzD;SCrBgB,kBACd,MACA,SACA,SAAoB;AAEpB,SAAO;IACL;IACA;IACA;IACA,aAAa;IACb,WAAW,cAAc,SAAS,WAAS,CAAC,MAAM,MAAM,KAAK,CAAC;;AAElE;SCfgB,oBAAoB,YAAoB;AACtD,SAAO,WAAW,WAAW,IAAI,WAAW,CAAC,IAAI;AACnD;AAOO,IAAI,YAAY,CAAC,gBAA+B;AACrD,MAAI;AACF,gBAAY,KAAK,CAAC,CAAA,CAAE,CAAC;AACrB,gBAAY,MAAM,CAAC,CAAA,CAAE;AACrB,WAAO,CAAC,CAAA,CAAE;WACH,GAAG;AACV,gBAAY,MAAM;AAClB,WAAO;;AAEX;SClBgB,gBAAiB,SAAiC;AAChE,MAAI,WAAW,MAAM;AACnB,WAAO,MAAM;aACJ,OAAO,YAAY,UAAU;AACtC,WAAO,0BAA0B,OAAO;SACnC;AACL,WAAO,SAAO,aAAa,KAAK,OAAO;;AAE3C;SAEgB,0BAA0B,SAAe;AACvD,QAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,SAAO,IAAI,OAAO;SACpB;AACL,WAAO,SAAO,aAAa,KAAK,OAAO;;AAE3C;SCCgB,SAAY,WAA+C;AACzE,SAAO,CAAA,EAAG,MAAM,KAAK,SAAS;AAChC;AAOA,IAAI,cAAc;SAEF,gBAAgB,SAAiC;AAC/D,SAAO,WAAW,OAChB,QACA,OAAO,YAAY,WACjB,UACA,IAAI,QAAQ,KAAK,GAAG,CAAC;AAC3B;SAEgB,aACd,IACA,aACA,UAAwB;AAExB,WAAS,cAAcgB,KAAiB,OAAqB;AAC3D,UAAMC,UAAS,SAASD,IAAG,gBAAgB;AAC3C,WAAO;MACL,QAAQ;QACN,MAAMA,IAAG;QACT,QAAQC,QAAO,IAAI,WAAS,MAAM,YAAY,KAAK,CAAC,EAAE,IAAI,WAAK;AAC7D,gBAAM,EAAC,SAAS,cAAa,IAAI;AACjC,gBAAM,WAAW,QAAQ,OAAO;AAChC,gBAAM,WAAW,WAAW;AAC5B,gBAAM,iBAAwD,CAAA;AAC9D,gBAAM,SAAS;YACb,MAAM,MAAM;YACZ,YAAY;cACV,MAAM;cACN,cAAc;cACd;cACA;cACA;cACA;cACA,QAAQ;cACR,YAAY,gBAAgB,OAAO;;YAErC,SAAS,SAAS,MAAM,UAAU,EAAE,IAAI,eAAa,MAAM,MAAM,SAAS,CAAC,EACxE,IAAI,WAAK;AACR,oBAAM,EAAC,MAAM,QAAQ,YAAY,SAAAC,SAAO,IAAI;AAC5C,oBAAMC,YAAW,QAAQD,QAAO;AAChC,oBAAME,UAAsB;gBAC1B;gBACA,UAAAD;gBACA,SAAAD;gBACA;gBACA;gBACA,YAAY,gBAAgBA,QAAO;;AAErC,6BAAe,gBAAgBA,QAAO,CAAC,IAAIE;AAC3C,qBAAOA;aACR;YACH,mBAAmB,CAACF,aAAsC,eAAe,gBAAgBA,QAAO,CAAC;;AAEnG,yBAAe,KAAK,IAAI,OAAO;AAC/B,cAAI,WAAW,MAAM;AACnB,2BAAe,gBAAgB,OAAO,CAAC,IAAI,OAAO;;AAEpD,iBAAO;SACR;;MAEH,WAAWD,QAAO,SAAS,KAAM,YAAY,MAAM,YAAYA,QAAO,CAAC,CAAC,KACtE,EAAE,OAAO,cAAc,eAAe,SAAS,KAAK,UAAU,SAAS,KACvE,CAAC,oBAAoB,KAAK,UAAU,SAAS,KAC7C,CAAA,EAAG,OAAO,UAAU,UAAU,MAAM,eAAe,CAAC,EAAE,CAAC,IAAI;;;AAIjE,WAAS,gBAAiB,OAAqB;AAC7C,QAAI,MAAM,SAAI;AAA0B,aAAO;AAC/C,QAAI,MAAM,SAAI;AAA4B,YAAM,IAAI,MAAM,0CAA0C;AACpG,UAAM,EAAC,OAAO,OAAO,WAAW,UAAS,IAAI;AAC7C,UAAM,WAAW,UAAU,SACzB,UAAU,SACR,OACA,YAAY,WAAW,OAAO,CAAC,CAAC,SAAS,IAC3C,UAAU,SACR,YAAY,WAAW,OAAO,CAAC,CAAC,SAAS,IACzC,YAAY,MAAM,OAAO,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC,SAAS;AAC5D,WAAO;;AAGT,WAAS,kBAAkB,aAA8B;AACvD,UAAM,YAAY,YAAY;AAE9B,aAAS,OAAQ,EAAC,OAAO,MAAAtB,OAAM,MAAAM,OAAM,QAAQ,MAAK,GAAC;AACjD,aAAO,IAAI,QAA8B,CAAC,SAAS,WAAM;AACvD,kBAAU,KAAK,OAAO;AACtB,cAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,cAAM,WAAW,MAAM,WAAW;AAClC,cAAM,aAAaN,UAAS,SAASA,UAAS;AAC9C,YAAI,CAAC,cAAcA,UAAS,YAAYA,UAAS;AAC/C,gBAAM,IAAI,MAAO,6BAA6BA,KAAI;AAEpD,cAAM,EAAC,OAAM,IAAIM,SAAQ,UAAU,EAAC,QAAQ,EAAC;AAC7C,YAAIA,SAAQ,UAAUA,MAAK,WAAW,OAAO,QAAQ;AACnD,gBAAM,IAAI,MAAM,+DAA+D;;AAEjF,YAAI,WAAW;AAEb,iBAAO,QAAQ,EAAC,aAAa,GAAG,UAAU,CAAA,GAAI,SAAS,CAAA,GAAI,YAAY,OAAS,CAAC;AAEnF,YAAI;AACJ,cAAM,OAAqB,CAAA;AAE3B,cAAM,WAA+C,CAAA;AACrD,YAAI,cAAc;AAClB,cAAM,eACJ,WAAK;AACH,YAAE;AACF,yBAAe,KAAK;;AAGxB,YAAIN,UAAS,eAAe;AAE1B,cAAI,MAAM,SAAI;AACZ,mBAAO,QAAQ,EAAC,aAAa,UAAU,SAAS,CAAA,GAAI,YAAY,OAAS,CAAC;AAC5E,cAAI,MAAM,SAAI;AACZ,iBAAK,KAAK,MAAM,MAAM,MAAK,CAAE;;AAE7B,iBAAK,KAAK,MAAM,MAAM,OAAO,gBAAgB,KAAK,CAAC,CAAC;eACjD;AAEL,gBAAM,CAAC,OAAO,KAAK,IAAI,aACrB,WACE,CAAC,QAAQM,KAAI,IACb,CAAC,QAAQ,IAAI,IACf,CAACA,OAAM,IAAI;AAEb,cAAI,YAAY;AACd,qBAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,mBAAK,KAAK,MAAO,SAAS,MAAM,CAAC,MAAM,SACrC,MAAMN,KAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,IAC9B,MAAMA,KAAI,EAAE,MAAM,CAAC,CAAC,CAAgB;AACtC,kBAAI,UAAU;;iBAEX;AACL,qBAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,mBAAK,KAAK,MAAM,MAAMA,KAAI,EAAE,MAAM,CAAC,CAAC,CAAe;AACnD,kBAAI,UAAU;;;;AAIpB,cAAM,OAAO,WAAK;AAChB,gBAAM,aAAa,MAAM,OAAO;AAChC,eAAK,QAAQ,CAAC0B,MAAK,MAAMA,KAAI,SAAS,SAAS,SAAS,CAAC,IAAIA,KAAI,MAAM;AACvE,kBAAQ;YACN;YACA;YACA,SAAS1B,UAAS,WAAWM,QAAO,KAAK,IAAI,CAAAoB,SAAOA,KAAI,MAAM;YAC9D;WACD;;AAGH,YAAI,UAAU,WAAK;AACjB,uBAAa,KAAK;AAClB,eAAK,KAAK;;AAGZ,YAAI,YAAY;OACjB;;AAGH,aAASC,YAAY,EAAC,OAAO,QAAQ,OAAAC,QAAO,SAAS,OAAM,GAA0B;AAEnF,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAM;AACjC,kBAAU,KAAK,OAAO;AACtB,cAAM,EAAC,OAAO,MAAK,IAAIA;AACvB,cAAM,QAAS,MAAyB,YAAY,SAAS;AAE7D,cAAM,SAAS,MAAM,eACnB,QACA,MAAM,MAAM,MAAM,IAAI;AAExB,cAAM,YAAY,UAChB,SACE,eACA,SACF,SACE,eACA;AAEJ,cAAM,MAAM,UAAU,EAAE,mBAAmB,UACzC,OAAO,WAAW,gBAAgB,KAAK,GAAG,SAAS,IACnD,OAAO,cAAc,gBAAgB,KAAK,GAAG,SAAS;AAGxD,YAAI,UAAU,mBAAmB,MAAM;AACvC,YAAI,YAAY,KAAK,QAAE;AAErB,gBAAM,SAAS,IAAI;AACnB,cAAI,CAAC,QAAQ;AACX,oBAAQ,IAAI;AACZ;;AAED,iBAAe,QAAQ,EAAE;AACzB,iBAAe,OAAO;AACvB,gBAAM,kBAAkB,OAAO,SAAS,KAAK,MAAM;AACnD,cAAI,4BAA4B,OAAO;AACvC,cAAI;AAA2B,wCAA4B,0BAA0B,KAAK,MAAM;AAChG,gBAAM,iBAAiB,OAAO,QAAQ,KAAK,MAAM;AACjD,gBAAM,4BAA4B,MAAA;AAAK,kBAAM,IAAI,MAAM,oBAAoB;UAAE;AAC7E,gBAAM,yBAAyB,MAAA;AAAK,kBAAM,IAAI,MAAM,oBAAoB;UAAE;AACzE,iBAAe,QAAQ;AACxB,iBAAO,OAAO,OAAO,WAAW,OAAO,qBAAqB,OAAO,UAAU;AAC7E,iBAAO,OAAO,KAAK,MAAM;AACzB,iBAAO,OAAO,WAAA;AAGZ,gBAAI,SAAS;AACb,mBAAO,KAAK,MAAM,MAAM,WAAW,KAAK,SAAQ,IAAK,KAAK,KAAI,CAAE,EAAE,KAAK,MAAM,IAAI;;AAEnF,iBAAO,QAAQ,CAAC,aAAQ;AAEtB,kBAAM,mBAAmB,IAAI,QAAc,CAAC,kBAAkB,oBAAe;AAC3E,iCAAmB,KAAK,gBAAgB;AACxC,kBAAI,UAAU,mBAAmB,eAAe;AAChD,qBAAO,OAAO;AACd,qBAAO,OAAO,WAAK;AAEjB,uBAAO,OAAO,OAAO,WAAW,OAAO,qBAAqB,OAAO,UAAU;AAC7E,iCAAiB,KAAK;;aAEzB;AAED,kBAAM,kBAAkB,MAAA;AACtB,kBAAI,IAAI,QAAQ;AAEd,oBAAI;AACF,2BAAQ;yBACD,KAAK;AACZ,yBAAO,KAAK,GAAG;;qBAEZ;AACJ,uBAAe,OAAO;AACvB,uBAAO,QAAQ,MAAA;AAAK,wBAAM,IAAI,MAAM,0BAA0B;gBAAE;AAChE,uBAAO,KAAI;;;AAGf,gBAAI,YAAY,KAAK,CAAAC,QAAE;AAIrB,kBAAI,YAAY;AAChB,8BAAe;aAChB;AACD,mBAAO,WAAW;AAClB,mBAAO,qBAAqB;AAC5B,mBAAO,UAAU;AACjB,4BAAe;AACf,mBAAO;;AAET,kBAAQ,MAAM;WACb,MAAM;OACV;;AAGH,aAAS,MAAOC,YAAkB;AAChC,aAAO,CAAC,YAA2B;AACjC,eAAO,IAAI,QAA6B,CAAC,SAAS,WAAM;AACtD,oBAAU,KAAK,OAAO;AACtB,gBAAM,EAAC,OAAO,QAAQ,OAAO,OAAAF,OAAK,IAAI;AACtC,gBAAM,kBAAkB,UAAU,WAAW,SAAY;AACzD,gBAAM,EAAC,OAAO,MAAK,IAAIA;AACvB,gBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,gBAAM,SAAS,MAAM,eAAe,QAAQ,MAAM,MAAM,MAAM,IAAI;AAClE,gBAAM,cAAc,gBAAgB,KAAK;AACzC,cAAI,UAAU;AAAG,mBAAO,QAAQ,EAAC,QAAQ,CAAA,EAAE,CAAC;AAC5C,cAAIE,YAAW;AACb,kBAAM,MAAM,SACP,OAAe,OAAO,aAAa,eAAe,IAClD,OAAe,WAAW,aAAa,eAAe;AAC3D,gBAAI,YAAY,WAAS,QAAQ,EAAC,QAAQ,MAAM,OAAO,OAAM,CAAC;AAC9D,gBAAI,UAAU,mBAAmB,MAAM;iBAClC;AACL,gBAAI,QAAQ;AACZ,kBAAM,MAAM,UAAU,EAAE,mBAAmB,UACzC,OAAO,WAAW,WAAW,IAC7B,OAAO,cAAc,WAAW;AAClC,kBAAM,SAAS,CAAA;AACf,gBAAI,YAAY,WAAK;AACnB,oBAAM,SAAS,IAAI;AACnB,kBAAI,CAAC;AAAQ,uBAAO,QAAQ,EAAC,OAAM,CAAC;AACpC,qBAAO,KAAK,SAAS,OAAO,QAAQ,OAAO,UAAU;AACrD,kBAAI,EAAE,UAAU;AAAO,uBAAO,QAAQ,EAAC,OAAM,CAAC;AAC9C,qBAAO,SAAQ;;AAEjB,gBAAI,UAAU,mBAAmB,MAAM;;SAE1C;;;AAIL,WAAO;MACL,MAAM;MACN,QAAQ;MAER;MAEA,QAAS,EAAC,OAAO,MAAAxB,MAAI,GAAC;AACpB,eAAO,IAAI,QAAe,CAAC,SAAS,WAAM;AACxC,oBAAU,KAAK,OAAO;AACtB,gBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,gBAAM,SAASA,MAAK;AACpB,gBAAM,SAAS,IAAI,MAAM,MAAM;AAC/B,cAAI,WAAW;AACf,cAAI,gBAAgB;AAEpB,cAAI;AAEJ,gBAAM,iBAAiB,WAAK;AAC1B,kBAAMoB,OAAM,MAAM;AAClB,iBAAK,OAAOA,KAAI,IAAI,IAAIA,KAAI,WAAW;AAAM;AAC7C,gBAAI,EAAE,kBAAkB;AAAU,sBAAQ,MAAM;;AAElD,gBAAM,eAAe,mBAAmB,MAAM;AAE9C,mBAAS,IAAE,GAAG,IAAE,QAAQ,EAAE,GAAG;AAC3B,kBAAM,MAAMpB,MAAK,CAAC;AAClB,gBAAI,OAAO,MAAM;AACf,oBAAM,MAAM,IAAIA,MAAK,CAAC,CAAC;AACvB,kBAAI,OAAO;AACX,kBAAI,YAAY;AAChB,kBAAI,UAAU;AACd,gBAAE;;;AAGN,cAAI,aAAa;AAAG,oBAAQ,MAAM;SACnC;;MAGH,IAAK,EAAC,OAAO,IAAG,GAAC;AACf,eAAO,IAAI,QAAa,CAAC,SAAS,WAAM;AACtC,oBAAU,KAAM,OAAO;AACvB,gBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,gBAAM,MAAM,MAAM,IAAI,GAAG;AACzB,cAAI,YAAY,WAAS,QAAS,MAAM,OAAe,MAAM;AAC7D,cAAI,UAAU,mBAAmB,MAAM;SACxC;;MAGH,OAAO,MAAM,SAAS;MAEtB,YAAAqB;MAEA,MAAO,EAAC,OAAAC,QAAO,MAAK,GAAC;AACnB,cAAM,EAAC,OAAO,MAAK,IAAIA;AACvB,eAAO,IAAI,QAAgB,CAAC,SAAS,WAAM;AACzC,gBAAM,QAAS,MAAyB,YAAY,SAAS;AAC7D,gBAAM,SAAS,MAAM,eAAe,QAAQ,MAAM,MAAM,MAAM,IAAI;AAClE,gBAAM,cAAc,gBAAgB,KAAK;AACzC,gBAAM,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI,OAAO,MAAK;AAClE,cAAI,YAAY,KAAK,QAAM,QAAS,GAAG,OAAsB,MAAM,CAAC;AACpE,cAAI,UAAU,mBAAmB,MAAM;SACxC;;;;AAKP,QAAM,EAAC,QAAQ,UAAS,IAAI,cAAc,IAAI,QAAQ;AACtD,QAAM,SAAS,OAAO,OAAO,IAAI,iBAAe,kBAAkB,WAAW,CAAC;AAC9E,QAAM,WAA0C,CAAA;AAChD,SAAO,QAAQ,WAAS,SAAS,MAAM,IAAI,IAAI,KAAK;AACpD,SAAO;IACL,OAAO;IAEP,aAAa,GAAG,YAAY,KAAK,EAAE;IAEnC,MAAM,MAAY;AAChB,YAAM,SAAS,SAAS,IAAI;AAC5B,UAAI,CAAC;AAAQ,cAAM,IAAI,MAAM,UAAU,IAAI,aAAa;AACxD,aAAO,SAAS,IAAI;;IAGtB,SAAS;IAET,SAAS,UAAU,WAAW;IAE9B;;AAGJ;ACnZA,SAAS,sBACP,WACA,aAA0C;AAC1C,SAAO,YAAY,OAAO,CAAC,MAAM,EAAC,OAAM,OAAO,EAAC,GAAG,MAAM,GAAG,OAAO,IAAI,EAAC,IAAI,SAAS;AACvF;AAEA,SAAS,uBACP,aACA,OACA,EAAC,aAAa,WAAAV,WAAS,GACvB,UAAwB;AAExB,QAAM,SAAS,sBACb,aAAa,OAAO,aAAa,QAAQ,GACzC,YAAY,MAAM;AAKpB,SAAO;IACL;;AAEJ;SAEgB,yBAAyB,EAAC,QAAQ,GAAE,GAAU,UAAwB;AACpF,QAAM,QAAQ,SAAS;AACvB,QAAM,SAAS,uBAAuB,GAAG,cAAc,OAAO,GAAG,OAAO,QAAQ;AAChF,KAAG,OAAO,OAAO;AACjB,KAAG,OAAO,QAAQ,WAAK;AACrB,UAAM,YAAY,MAAM;AACxB,QAAI,GAAG,KAAK,OAAO,OAAO,KAAK,SAAO,IAAI,SAAS,SAAS,GAAG;AAC7D,YAAM,OAAO,GAAG,KAAK,MAAM,SAAS;AACpC,UAAI,GAAG,SAAS,aAAa,GAAG,OAAO;AACnC,WAAG,SAAS,EAAE,OAAO,MAAM;;;GAGlC;AACH;SC5BgB,cAAc,EAAC,QAAQ,GAAE,GAAU,MAAgB,YAAsB,UAAkB;AACzG,aAAW,QAAQ,eAAS;AAC1B,UAAM,SAAS,SAAS,SAAS;AACjC,SAAK,QAAQ,SAAG;AACd,YAAM,WAAW,sBAAsB,KAAK,SAAS;AACrD,UAAI,CAAC,YAAa,WAAW,YAAY,SAAS,UAAU,QAAY;AAEtE,YAAI,QAAQ,GAAG,YAAY,aAAa,eAAe,GAAG,aAAa;AAGrE,kBAAQ,KAAK,WAAW;YACtB,MAAG;AAAsB,qBAAO,KAAK,MAAM,SAAS;YAAE;YACtD,IAAI,OAAU;AAGZ,6BAAe,MAAM,WAAW,EAAC,OAAO,UAAU,MAAM,cAAc,MAAM,YAAY,KAAI,CAAC;;WAEhG;eACI;AAEL,cAAI,SAAS,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM;;;KAGpD;GACF;AACH;SAEgB,gBAAgB,EAAC,QAAQ,GAAE,GAAU,MAAc;AACjE,OAAK,QAAQ,SAAG;AACd,aAAS,OAAO,KAAK;AACnB,UAAI,IAAI,GAAG,aAAa,GAAG;AAAO,eAAO,IAAI,GAAG;;GAEnD;AACH;SAEgB,kBAAkB,GAAY,GAAU;AACtD,SAAO,EAAE,KAAK,UAAU,EAAE,KAAK;AACjC;SAEgB,aAAa,IAAW,YAAoB,iBAAiC,QAAM;AACjG,QAAM,eAAe,GAAG;AACxB,QAAM,QAAQ,GAAG,mBAAmB,aAAa,GAAG,aAAa,YAAY;AAC7E,QAAM,OAAO,eAAe;AAC5B,QAAM,YAAY,MAAM,MAAM;AAC9B,QAAM,oBAAoB,MAAM,QAAQ,KAAK,KAAK;AAClD,QAAM,YAAY,IAAI,aAAa;AACnC,WAAS,MAAA;AACP,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,QAAI,eAAe,GAAG;AAEpB,WAAK,YAAY,EAAE,QAAQ,eAAS;AAClC,oBAAY,iBAAiB,WAAW,aAAa,SAAS,EAAE,SAAS,aAAa,SAAS,EAAE,OAAO;OACzG;AACD,+BAAyB,IAAI,eAAe;AAC5Cb,mBAAQ,OAAO,MAAM,GAAG,GAAG,SAAS,KAAK,KAAK,CAAC,EAAE,MAAM,iBAAiB;;AAExE,6BAAuB,IAAI,YAAY,OAAO,eAAe,EAAE,MAAM,iBAAiB;GACzF;AACH;SAIgB,uBACd,EAAC,QAAQ,GAAE,GACX,YACA,OACA,iBAA+B;AAI/B,QAAM,QAA4B,CAAA;AAClC,QAAM,WAAW,GAAG;AACpB,MAAI,eAAe,GAAG,YAAY,kBAAkB,IAAI,GAAG,OAAO,eAAe;AACjF,MAAI,2BAA2B;AAE/B,QAAM,YAAY,SAAS,OAAO,OAAK,EAAE,KAAK,WAAW,UAAU;AACnE,YAAU,QAAQ,aAAO;AACvB,UAAM,KAAK,MAAA;AACT,YAAM,YAAY;AAClB,YAAM,YAAY,QAAQ,KAAK;AAC/B,iCAA2B,IAAI,WAAW,eAAe;AACzD,iCAA2B,IAAI,WAAW,eAAe;AAEzD,qBAAe,GAAG,YAAY;AAE9B,YAAM,OAAO,cAAc,WAAW,SAAS;AAE/C,WAAK,IAAI,QAAQ,WAAK;AACpB,oBAAY,iBAAiB,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,SAAS,MAAM,CAAC,EAAE,OAAO;OAC1E;AAED,WAAK,OAAO,QAAQ,YAAM;AACxB,YAAI,OAAO,UAAU;AACnB,gBAAM,IAAI,WAAW,QAAQ,0CAA0C;eAClE;AACL,gBAAM,QAAQ,gBAAgB,YAAY,OAAO,IAAI;AAErD,iBAAO,IAAI,QAAQ,SAAO,SAAS,OAAO,GAAG,CAAC;AAE9C,iBAAO,OAAO,QAAQ,SAAG;AACvB,kBAAM,YAAY,IAAI,IAAI;AAC1B,qBAAS,OAAO,GAAG;WACpB;AAED,iBAAO,IAAI,QAAQ,aAAW,MAAM,YAAY,OAAO,CAAC;;OAE3D;AAED,YAAM,iBAAiB,QAAQ,KAAK;AAEpC,UAAI,kBAAkB,QAAQ,KAAK,UAAU,YAAY;AAEvD,iCAAyB,IAAI,eAAe;AAC5C,cAAM,kBAAkB,CAAA;AAExB,mCAA2B;AAG3B,YAAI,gBAAgB,aAAa,SAAS;AAC1C,aAAK,IAAI,QAAQ,WAAK;AACpB,wBAAc,KAAK,IAAI,UAAU,KAAK;SACvC;AAMD,wBAAgB,IAAI,CAAC,GAAG,YAAY,SAAS,CAAC;AAC9C,sBAAc,IAAI,CAAC,GAAG,YAAY,SAAS,GAAG,KAAK,aAAa,GAAG,aAAa;AAChF,cAAM,SAAS;AAGf,cAAM,wBAAwB,gBAAgB,cAAc;AAC5D,YAAI,uBAAuB;AACzB,kCAAuB;;AAGzB,YAAI;AACJ,cAAM,kBAAkBA,aAAQ,OAAO,MAAA;AAErC,wBAAc,eAAe,KAAK;AAClC,cAAI,aAAa;AACf,gBAAI,uBAAuB;AAEzB,kBAAI,cAAc,wBAAwB,KAAK,MAAM,IAAI;AACzD,0BAAY,KAAK,aAAa,WAAW;;;SAG9C;AACD,eAAQ,eAAe,OAAO,YAAY,SAAS,aACjDA,aAAQ,QAAQ,WAAW,IAAI,gBAAgB,KAAK,MAAI,WAAW;;KAExE;AACD,UAAM,KAAK,cAAQ;AACjB,UAAI,CAAC,4BAA4B,CAAC,2BAA2B;AAC3D,cAAM,YAAY,QAAQ,KAAK;AAE/B,4BAAoB,WAAW,QAAQ;;AAGzC,sBAAgB,IAAI,CAAC,GAAG,YAAY,SAAS,CAAC;AAC9C,oBAAc,IAAI,CAAC,GAAG,YAAY,SAAS,GAAG,GAAG,aAAa,GAAG,SAAS;AAC1E,YAAM,SAAS,GAAG;KACnB;GACF;AAGD,WAAS,WAAQ;AACf,WAAO,MAAM,SAASA,aAAQ,QAAQ,MAAM,MAAK,EAAG,MAAM,QAAQ,CAAC,EAAE,KAAK,QAAQ,IAChFA,aAAQ,QAAO;;AAGnB,SAAO,SAAQ,EAAG,KAAK,MAAA;AACrB,wBAAoB,cAAc,eAAe;GAClD;AACH;SAgBgB,cAAc,WAAqB,WAAmB;AACpE,QAAM,OAAmB;IACvB,KAAK,CAAA;IACL,KAAK,CAAA;IACL,QAAQ,CAAA;;AAEV,MAAI;AACJ,OAAK,SAAS,WAAW;AACvB,QAAI,CAAC,UAAU,KAAK;AAAG,WAAK,IAAI,KAAK,KAAK;;AAE5C,OAAK,SAAS,WAAW;AACvB,UAAM,SAAS,UAAU,KAAK,GAC5B,SAAS,UAAU,KAAK;AAC1B,QAAI,CAAC,QAAQ;AACX,WAAK,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC;WACxB;AACL,YAAM,SAAS;QACb,MAAM;QACN,KAAK;QACL,UAAU;QACV,KAAK,CAAA;QACL,KAAK,CAAA;QACL,QAAQ,CAAA;;AAEV,UAIM,MAAI,OAAO,QAAQ,WAAS,QAE5B,MAAI,OAAO,QAAQ,WAAS,OAG7B,OAAO,QAAQ,SAAS,OAAO,QAAQ,QAAQ,CAAC,YACrD;AAEE,eAAO,WAAW;AAClB,aAAK,OAAO,KAAK,MAAM;aAClB;AAEL,cAAM,aAAa,OAAO;AAC1B,cAAM,aAAa,OAAO;AAC1B,YAAI;AACJ,aAAK,WAAW,YAAY;AAC1B,cAAI,CAAC,WAAW,OAAO;AAAG,mBAAO,IAAI,KAAK,OAAO;;AAEnD,aAAK,WAAW,YAAY;AAC1B,gBAAM,SAAS,WAAW,OAAO,GAC/B,SAAS,WAAW,OAAO;AAC7B,cAAI,CAAC;AAAQ,mBAAO,IAAI,KAAK,MAAM;mBAC1B,OAAO,QAAQ,OAAO;AAAK,mBAAO,OAAO,KAAK,MAAM;;AAE/D,YAAI,OAAO,IAAI,SAAS,KAAK,OAAO,IAAI,SAAS,KAAK,OAAO,OAAO,SAAS,GAAG;AAC9E,eAAK,OAAO,KAAK,MAAM;;;;;AAK/B,SAAO;AACT;SAEgB,YACd,UACA,WACA,SACA,SAAoB;AAEpB,QAAM,QAAQ,SAAS,GAAG,kBACxB,WACA,QAAQ,UACN,EAAE,SAAS,QAAQ,SAAS,eAAe,QAAQ,KAAI,IACvD,EAAE,eAAe,QAAQ,KAAI,CAAE;AAEnC,UAAQ,QAAQ,SAAO,SAAS,OAAO,GAAG,CAAC;AAC3C,SAAO;AACT;SAEgB,oBAAoB,WAAqB,UAAwB;AAC/E,OAAK,SAAS,EAAE,QAAQ,eAAS;AAC/B,QAAI,CAAC,SAAS,GAAG,iBAAiB,SAAS,SAAS,GAAG;AACrD,kBAAY,UAAU,WAAW,UAAU,SAAS,EAAE,SAAS,UAAU,SAAS,EAAE,OAAO;;GAE9F;AACH;SAEgB,oBAAoB,WAAqB,UAAwB;AAC/E,GAAA,EAAG,MAAM,KAAK,SAAS,GAAG,gBAAgB,EAAE,QAAQ,eAClD,UAAU,SAAS,KAAK,QAAQ,SAAS,GAAG,kBAAkB,SAAS,CAAC;AAC5E;SAEgB,SAAS,OAAuB,KAAc;AAC5D,QAAM,YAAY,IAAI,MAAM,IAAI,SAAS,EAAE,QAAQ,IAAI,QAAQ,YAAY,IAAI,MAAK,CAAE;AACxF;AAEA,SAAS,kBACP,IACA,OACA,UAAwB;AAExB,QAAM,eAAe,CAAA;AACrB,QAAM,eAAe,MAAM,MAAM,kBAAkB,CAAC;AACpD,eAAa,QAAQ,eAAS;AAC5B,UAAM,QAAQ,SAAS,YAAY,SAAS;AAC5C,QAAI,UAAU,MAAM;AACpB,UAAM,UAAU,gBACd,gBAAgB,OAAO,GACvB,WAAW,IACX,OACA,OACA,CAAC,CAAC,MAAM,eACR,WAAW,OAAO,YAAY,UAC9B,IAAI;AAEN,UAAM,UAAuB,CAAA;AAC7B,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,YAAM,WAAW,MAAM,MAAM,MAAM,WAAW,CAAC,CAAC;AAChD,gBAAU,SAAS;AACnB,UAAI,QAAQ,gBACV,SAAS,MACT,SACA,CAAC,CAAC,SAAS,QACX,CAAC,CAAC,SAAS,YACX,OACA,WAAW,OAAO,YAAY,UAC9B,KAAK;AAEP,cAAQ,KAAK,KAAK;;AAEpB,iBAAa,SAAS,IAAI,kBAAkB,WAAW,SAAS,OAAO;GACxE;AACD,SAAO;AACT;SAEgB,iBAAiB,EAAC,QAAQ,GAAE,GAAU,OAAoB,UAAwB;AAChG,KAAG,QAAQ,MAAM,UAAU;AAC3B,QAAM,eAAe,GAAG,YAAY,kBAAkB,IAAI,OAAO,QAAQ;AACzE,KAAG,cAAc,MAAM,MAAM,kBAAkB,CAAC;AAChD,gBAAc,IAAI,CAAC,GAAG,UAAU,GAAG,KAAK,YAAY,GAAG,YAAY;AACrE;SAEgB,sBAAsB,IAAW,UAAwB;AACvE,QAAM,kBAAkB,kBAAkB,IAAI,GAAG,OAAO,QAAQ;AAChE,QAAM,OAAO,cAAc,iBAAiB,GAAG,SAAS;AACxD,SAAO,EAAE,KAAK,IAAI,UAAU,KAAK,OAAO,KAAK,QAAM,GAAG,IAAI,UAAU,GAAG,OAAO,MAAM;AACtF;SAEgB,2BAA2B,EAAC,QAAQ,GAAE,GAAU,QAAkB,UAAwB;AAExG,QAAM,aAAa,SAAS,GAAG;AAE/B,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,UAAM,YAAY,WAAW,CAAC;AAC9B,UAAM,QAAQ,SAAS,YAAY,SAAS;AAC5C,OAAG,aAAa,YAAY;AAE5B,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,YAAM,YAAY,MAAM,WAAW,CAAC;AACpC,YAAM,UAAU,MAAM,MAAM,SAAS,EAAE;AACvC,YAAM,YAAY,OAAO,YAAY,WAAW,UAAU,MAAM,MAAM,OAAO,EAAE,KAAK,GAAG,IAAI;AAC3F,UAAI,OAAO,SAAS,GAAG;AACrB,cAAM,YAAY,OAAO,SAAS,EAAE,UAAU,SAAS;AACvD,YAAI,WAAW;AACb,oBAAU,OAAO;AACjB,iBAAO,OAAO,SAAS,EAAE,UAAU,SAAS;AAC5C,iBAAO,SAAS,EAAE,UAAU,SAAS,IAAI;;;;;AAOjD,MAAI,OAAO,cAAc,eAAe,SAAS,KAAK,UAAU,SAAS,KACvE,CAAC,oBAAoB,KAAK,UAAU,SAAS,KAC7C,QAAQ,qBAAqB,mBAAmB,QAAQ,qBACxD,CAAA,EAAG,OAAO,UAAU,UAAU,MAAM,eAAe,CAAC,EAAE,CAAC,IAAI,KAC7D;AACE,OAAG,aAAa;;AAEpB;SAEgB,iBAAiB,mBAAyB;AACxD,SAAO,kBAAkB,MAAM,GAAG,EAAE,IAAI,CAAC,OAAO,aAAQ;AACtD,YAAQ,MAAM,KAAI;AAClB,UAAM,OAAO,MAAM,QAAQ,gBAAgB,EAAE;AAE7C,UAAM,UAAU,MAAM,KAAK,IAAI,IAAI,KAAK,MAAM,YAAY,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;AAE5E,WAAO,gBACL,MACA,WAAW,MACX,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,OAAO,KAAK,KAAK,GACjB,QAAQ,OAAO,GACf,aAAa,CAAC;GAEjB;AACH;ICvYa,gBAAO;EAUlB,iBAAiB,QAAgD,WAAmB;AAClF,SAAK,MAAM,EAAE,QAAQ,eAAS;AAC5B,UAAI,OAAO,SAAS,MAAM,MAAM;AAC5B,YAAI,UAAU,iBAAiB,OAAO,SAAS,CAAC;AAChD,YAAI,UAAU,QAAQ,MAAK;AAC3B,YAAI,QAAQ;AAAO,gBAAM,IAAI,WAAW,OAAO,oCAAoC;AACnF,gBAAQ,QAAQ,SAAG;AACf,cAAI,IAAI;AAAM,kBAAM,IAAI,WAAW,OAAO,sDAAsD;AAChG,cAAI,CAAC,IAAI;AAAS,kBAAM,IAAI,WAAW,OAAO,sDAAsD;SACvG;AACD,kBAAU,SAAS,IAAI,kBAAkB,WAAW,SAAS,OAAO;;KAEzE;;EAGH,OAAO,QAAyC;AAC9C,UAAM,KAAK,KAAK;AAChB,SAAK,KAAK,eAAe,KAAK,KAAK,eACjC,OAAO,KAAK,KAAK,cAAc,MAAM,IACrC;AACF,UAAM,WAAW,GAAG;AAGpB,UAAM,aAAyC,CAAA;AAC/C,QAAI,WAAW,CAAA;AACf,aAAS,QAAQ,aAAO;AACtB,aAAO,YAAY,QAAQ,KAAK,YAAY;AAC5C,iBAAY,QAAQ,KAAK,WAAW,CAAA;AACpC,cAAQ,iBAAiB,YAAY,QAAQ;KAC9C;AAED,OAAG,YAAY;AAEf,oBAAgB,IAAI,CAAC,GAAG,YAAY,IAAI,GAAG,YAAY,SAAS,CAAC;AACjE,kBAAc,IAAI,CAAC,GAAG,YAAY,IAAI,GAAG,YAAY,WAAW,KAAK,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,QAAQ;AAC3G,OAAG,cAAc,KAAK,QAAQ;AAC9B,WAAO;;EAGT,QAAQ,iBAAgE;AACtE,SAAK,KAAK,iBAAiB,gBAAgB,KAAK,KAAK,kBAAkB,KAAK,eAAe;AAC3F,WAAO;;;SClDK,yBAAyB,IAAS;AAChD,SAAO,qBACL,QAAQ,WAER,SAAS0B,SAAuB,eAAqB;AACnD,SAAK,KAAK;AACV,SAAK,OAAO;MACV,SAAS;MACT,cAAc;MACd,UAAU,CAAA;MACV,QAAQ,CAAA;MACR,gBAAgB;;GAEnB;AAEL;ACtBA,SAAS,gBAAgBb,YAAuB,aAA2B;AACzE,MAAI,YAAYA,WAAU,YAAY;AACtC,MAAI,CAAC,WAAW;AACd,gBAAYA,WAAU,YAAY,IAAI,IAAIc,QAAM,YAAY;MAC1D,QAAQ,CAAA;MACR,WAAAd;MACA;KACD;AACD,cAAU,QAAQ,CAAC,EAAE,OAAO,EAAE,SAAS,OAAM,CAAE;;AAEjD,SAAO,UAAU,MAAM,SAAS;AAClC;AAEA,SAAS,mBAAmBA,YAAqB;AAC/C,SAAOA,cAAa,OAAOA,WAAU,cAAc;AACrD;SAEgB,iBAAiB,EAC/B,WAAAA,YACA,YAAW,GACU;AACrB,SAAO,mBAAmBA,UAAS,IAC/B,QAAQ,QAAQA,WAAU,UAAS,CAAE,EAAE,KAAK,CAAC,UAC3C,MAEG,IAAI,CAAC,SAAS,KAAK,IAAI,EAEvB,OAAO,CAAC,SAAS,SAAS,UAAU,CAAC,IAE1C,gBAAgBA,YAAW,WAAW,EAAE,aAAY,EAAG,YAAW;AACxE;SAEgB,mBACd,EAAE,WAAAA,YAAW,YAAW,GACxB,MAAY;AAEZ,GAAC,mBAAmBA,UAAS,KAC3B,SAAS,cACT,gBAAgBA,YAAW,WAAW,EAAE,IAAI,EAAC,KAAI,CAAC,EAAE,MAAM,GAAG;AACjE;SAEgB,mBACd,EAAE,WAAAA,YAAW,YAAW,GACxB,MAAY;AAEZ,GAAC,mBAAmBA,UAAS,KAC3B,SAAS,cACT,gBAAgBA,YAAW,WAAW,EAAE,OAAO,IAAI,EAAE,MAAM,GAAG;AAClE;SCrDgB,IAAK,IAAE;AASrB,SAAO,SAAS,WAAA;AACd,QAAI,aAAa;AACjB,WAAO,GAAE;GACV;AACH;ACVA,SAAS,WAAW;AAChB,MAAI,WAAW,CAAC,UAAU,iBACtB,WAAW,KAAK,UAAU,SAAS,KACnC,CAAC,iBAAiB,KAAK,UAAU,SAAS;AAE9C,MAAI,CAAC,YAAY,CAAC,UAAU;AACxB,WAAO,QAAQ,QAAO;AAC1B,MAAI;AACJ,SAAO,IAAI,QAAQ,SAAU,SAAS;AAClC,QAAI,SAAS,WAAY;AAAE,aAAO,UAAU,UAAS,EAAG,QAAQ,OAAO;IAAE;AACzE,iBAAa,YAAY,QAAQ,GAAG;AACpC,WAAM;EACd,CAAK,EAAE,QAAQ,WAAY;AAAE,WAAO,cAAc,UAAU;EAAE,CAAE;AAChE;SCHgB,UAAW,IAAS;AAClC,QAAM,QAAQ,GAAG;AACjB,QAAM,EAAC,WAAAA,WAAS,IAAI,GAAG;AACvB,MAAI,MAAM,iBAAiB,GAAG;AAC1B,WAAO,MAAM,eAAe,KAAY,MAAM,MAAM,cAClD,UAAW,MAAM,WAAW,IAC5B,EAAE;AACRe,YAAgB,MAAM,cAAc,eAAeC,kBAAuB;AAC1E,QAAM,gBAAgB;AACtB,QAAM,cAAc;AACpB,QAAM,eAAe;AACrB,QAAM,gBAAgB,MAAM;AAE5B,WAAS,mBAAgB;AAGvB,QAAI,MAAM,kBAAkB;AAAe,YAAM,IAAI,WAAW,eAAe,yBAAyB;;AAI1G,MAAI,iBAAiB,MAAM,gBAEvB,qBAA8C,MAC9C,aAAa;AAEjB,QAAM,YAAY,MAAM,IAAI7B,aAAQ,CAAC,SAAS,WAAM;AAMlD,qBAAgB;AAEhB,QAAI,CAACa;AAAW,YAAM,IAAI,WAAW,WAAU;AAC/C,UAAM,SAAS,GAAG;AAElB,UAAM,MAAM,MAAM,aAChBA,WAAU,KAAK,MAAM,IACrBA,WAAU,KAAK,QAAQ,KAAK,MAAM,GAAG,QAAQ,EAAE,CAAC;AAClD,QAAI,CAAC;AAAK,YAAM,IAAI,WAAW,WAAU;AACzC,QAAI,UAAU,mBAAmB,MAAM;AACvC,QAAI,YAAY,KAAK,GAAG,cAAc;AACtC,QAAI,kBAAkB,KAAM,OAAC;AACzB,2BAAqB,IAAI;AACzB,UAAI,MAAM,cAAc,CAAC,GAAG,SAAS,cAAc;AAI/C,YAAI,UAAU;AACd,2BAAmB,MAAK;AAExB,YAAI,OAAO,MAAK;AAChB,cAAM,SAASA,WAAU,eAAe,MAAM;AAC9C,eAAO,YAAY,OAAO,UAAU,KAAK,MAAA;AACrC,iBAAQ,IAAI,WAAW,eAAe,YAAY,MAAM,eAAe,CAAC;SAC3E;aACE;AACH,2BAAmB,UAAU,mBAAmB,MAAM;AACtD,YAAI,SAAS,EAAE,aAAa,KAAK,IAAI,GAAG,EAAE,IAAI,IAAI,EAAE;AACpD,qBAAa,SAAS;AACtB,WAAG,OAAO,QAAQ,IAAI;AACtB,qBAAa,IAAI,SAAS,IAAI,oBAAoB,MAAM;;OAE7D,MAAM;AAET,QAAI,YAAY,KAAM,MAAA;AAElB,2BAAqB;AACrB,YAAM,QAAQ,GAAG,OAAO,QAAQ,IAAI;AAEpC,YAAM,mBAAmB,MAAM,MAAM,gBAAgB;AACrD,UAAI,iBAAiB,SAAS;AAAG,YAAI;AACnC,gBAAM,WAAW,MAAM,YAAY,oBAAoB,gBAAgB,GAAG,UAAU;AACpF,cAAI,MAAM;AAAY,6BAAiB,IAAI,OAAO,QAAQ;eACrD;AACD,uCAA2B,IAAI,GAAG,WAAW,QAAQ;AACrD,gBAAI,CAAC,sBAAsB,IAAI,QAAQ,GAAG;AACtC,sBAAQ,KAAK,oHAAoH;;;AAGzI,mCAAyB,IAAI,QAAQ;iBAC9B,GAAG;;AASZ,kBAAY,KAAK,EAAE;AAEnB,YAAM,kBAAkB,KAAK,QAAE;AAC3B,cAAM,UAAU;AAChB,WAAG,GAAG,eAAe,EAAE,KAAK,EAAE;OACjC;AAED,YAAM,UAAU,KAAK,QAAE;AACnB,WAAG,GAAG,OAAO,EAAE,KAAK,EAAE;OACzB;AAED,UAAI;AAAY,2BAAmB,GAAG,OAAO,MAAM;AAEnD,cAAO;OAER,MAAM;GACV,EAAE,MAAM,SAAG;AACV,QAAI,OAAO,IAAI,SAAS,kBAAkB,MAAM,iBAAiB,GAAG;AAGlE,YAAM;AACN,cAAQ,KAAK,qDAAqD;AAClE,aAAO,UAAS;WACX;AACL,aAAOb,aAAQ,OAAO,GAAG;;GAE5B;AAGD,SAAOA,aAAQ,KAAK;IAClB;KACC,OAAO,cAAc,cAAcA,aAAQ,QAAO,IAAK8B,SAAkB,GAAI,KAAK,SAAS;GAC7F,EAAE,KAAK,MAAA;AAKJ,qBAAgB;AAChB,UAAM,oBAAoB,CAAA;AAC1B,WAAO9B,aAAQ,QAAQ,IAAI,MAAI,GAAG,GAAG,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,SAAS,iBAAc;AAClF,UAAI,MAAM,kBAAkB,SAAS,GAAG;AAEpC,YAAI,aAAa,MAAM,kBAAkB,OAAO,iBAAiB,GAAG;AACpE,cAAM,oBAAoB,CAAA;AAC1B,eAAOA,aAAQ,QAAQ,IAAI,MAAI,WAAW,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,cAAc;;KAE9E;GACJ,EAAE,QAAQ,MAAA;AACP,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB;GACzB,EAAE,KAAK,MAAA;AAEJ,WAAO;GACV,EAAE,MAAM,SAAG;AACR,UAAM,cAAc;AACpB,QAAI;AAEF,4BAAsB,mBAAmB,MAAK;aAC9C,IAAM;IAAA;AACR,QAAI,kBAAkB,MAAM,eAAe;AAGzC,SAAG,OAAM;;AAEX,WAAO,UAAW,GAAG;GACxB,EAAE,QAAQ,MAAA;AACP,UAAM,eAAe;AACrB,mBAAc;GACjB;AACH;SC7KgB,cAAe,UAAuB;AACpD,MAAI,WAAW,YAAU,SAAS,KAAK,MAAM,GACzC,UAAU,WAAS,SAAS,MAAM,KAAK,GACvC,YAAY,KAAK,QAAQ,GACzB,UAAU,KAAK,OAAO;AAE1B,WAAS,KAAK,SAAmB;AAC7B,WAAO,CAAC,QAAI;AACR,UAAI,OAAO,QAAQ,GAAG,GAClB,QAAQ,KAAK;AAEjB,aAAO,KAAK,OAAO,QACd,CAAC,SAAS,OAAO,MAAM,SAAS,aAC7B,QAAQ,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,KAAK,WAAW,OAAO,IAAI,UAAU,KAAK,IAC9E,MAAM,KAAK,WAAW,OAAO;;;AAI7C,SAAO,KAAK,QAAQ,EAAC;AACvB;SCPgB,uBAAuB,MAAuB,aAAa,WAAS;AAElF,MAAI,IAAI,UAAU;AAClB,MAAI,IAAI;AAAG,UAAM,IAAI,WAAW,gBAAgB,mBAAmB;AAGnE,MAAI,OAAO,IAAI,MAAM,IAAI,CAAC;AAC1B,SAAO,EAAE;AAAG,SAAK,IAAI,CAAC,IAAI,UAAU,CAAC;AAErC,cAAY,KAAK,IAAG;AACpB,MAAI,SAAS,QAAQ,IAAI;AACzB,SAAO,CAAC,MAAM,QAAQ,SAAS;AACjC;SAEgB,sBACd,IACA,MACA,YACA,mBACA,WAAqC;AAErC,SAAOA,aAAQ,QAAO,EAAG,KAAK,MAAA;AAE5B,UAAM,YAAY,IAAI,aAAa;AAGnC,UAAM,QAAQ,GAAG,mBAAmB,MAAM,YAAY,GAAG,WAAW,iBAAiB;AAErF,UAAM,YAAY;MAChB;MACA;;AAGF,QAAI,mBAAmB;AAErB,YAAM,WAAW,kBAAkB;WAC9B;AACL,UAAI;AACF,cAAM,OAAM;AACZ,WAAG,OAAO,iBAAiB;eACpB,IAAI;AACX,YAAI,GAAG,SAAS,SAAS,gBAAgB,GAAG,OAAM,KAAM,EAAE,GAAG,OAAO,iBAAiB,GAAG;AACtF,kBAAQ,KAAK,0BAA0B;AACvC,aAAG,OAAM;AACT,iBAAO,GAAG,KAAI,EAAG,KAAK,MAAM,sBAC1B,IACA,MACA,YACA,MACA,SAAS,CACV;;AAEH,eAAO,UAAU,EAAE;;;AAKvB,UAAM,mBAAmB,gBAAgB,SAAS;AAClD,QAAI,kBAAkB;AACpB,8BAAuB;;AAGzB,QAAI;AACJ,UAAM,kBAAkBA,aAAQ,OAAO,MAAA;AAErC,oBAAc,UAAU,KAAK,OAAO,KAAK;AACzC,UAAI,aAAa;AACf,YAAI,kBAAkB;AAEpB,cAAI,cAAc,wBAAwB,KAAK,MAAM,IAAI;AACzD,sBAAY,KAAK,aAAa,WAAW;mBAChC,OAAO,YAAY,SAAS,cAAc,OAAO,YAAY,UAAU,YAAY;AAE5F,wBAAc,cAAc,WAAW;;;OAG1C,SAAS;AACZ,YAAQ,eAAe,OAAO,YAAY,SAAS,aAEjDA,aAAQ,QAAQ,WAAW,EAAE,KAAK,OAAK,MAAM,SAC3C,IACE,UAAU,IAAI,WAAW,gBACzB,4DAA4D,CAAC,CAAC,IAEhE,gBAAgB,KAAK,MAAM,WAAW,GACxC,KAAK,OAAC;AAEN,UAAI;AAAmB,cAAM,SAAQ;AAGrC,aAAO,MAAM,YAAY,KAAK,MAAM,CAAC;KACtC,EAAE,MAAM,OAAC;AACR,YAAM,QAAQ,CAAC;AACf,aAAO,UAAU,CAAC;KACnB;GACF;AACH;SC7EgB,IAAK,GAAgB,OAAY,OAAa;AAC5D,QAAM,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAK,IAAK,CAAC,CAAC;AAC1C,WAAS,IAAE,GAAG,IAAE,OAAO,EAAE;AAAG,WAAO,KAAK,KAAK;AAC7C,SAAO;AACT;SAGgB,6BAA8B,MAAY;AACxD,SAAO;IACL,GAAG;IACH,MAAM,WAAiB;AACrB,YAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,YAAM,EAAC,OAAM,IAAI;AACjB,YAAM,cAAsD,CAAA;AAC5D,YAAM,oBAAoC,CAAA;AAE1C,eAAS,kBAAmB,SAAmC,SAAiB,eAA0B;AACxG,cAAM,eAAe,gBAAgB,OAAO;AAC5C,cAAM,YAAa,YAAY,YAAY,IAAI,YAAY,YAAY,KAAK,CAAA;AAC5E,cAAM,YAAY,WAAW,OAAO,IAAG,OAAO,YAAY,WAAW,IAAI,QAAQ;AACjF,cAAM,YAAY,UAAU;AAC5B,cAAM,eAAe;UACnB,GAAG;UACH;UACA;UACA;UACA,YAAY,gBAAgB,OAAO;UACnC,QAAQ,CAAC,aAAa,cAAc;;AAEtC,kBAAU,KAAK,YAAY;AAC3B,YAAI,CAAC,aAAa,cAAc;AAC9B,4BAAkB,KAAK,YAAY;;AAErC,YAAI,YAAY,GAAG;AACjB,gBAAM,iBAAiB,cAAc,IACnC,QAAQ,CAAC,IACT,QAAQ,MAAM,GAAG,YAAY,CAAC;AAChC,4BAAkB,gBAAgB,UAAU,GAAG,aAAa;;AAE9D,kBAAU,KAAK,CAAC,GAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAC7C,eAAO;;AAGT,YAAM,aAAa,kBAAkB,OAAO,WAAW,SAAS,GAAG,OAAO,UAAU;AACpF,kBAAY,KAAK,IAAI,CAAC,UAAU;AAChC,iBAAW,SAAS,OAAO,SAAS;AAClC,0BAAkB,MAAM,SAAS,GAAG,KAAK;;AAG3C,eAAS,cAAc,SAAiC;AACtD,cAAMoB,UAAS,YAAY,gBAAgB,OAAO,CAAC;AACnD,eAAOA,WAAUA,QAAO,CAAC;;AAG3B,eAAS,eAAgB,OAAuB,SAAe;AAC7D,eAAO;UACL,MAAM,MAAM,SAAI,QAEd,MAAM;UACR,OAAO,IAAI,MAAM,OAAO,MAAM,YAAY,KAAK,UAAU,KAAK,SAAS,OAAO;UAC9E,WAAW;UACX,OAAO,IAAI,MAAM,OAAO,MAAM,YAAY,KAAK,UAAU,KAAK,SAAS,OAAO;UAC9E,WAAW;;;AAMf,eAAS,iBAAkB,KAAuB;AAChD,cAAM,QAAQ,IAAI,MAAM;AACxB,eAAO,MAAM,YAAY;UACvB,GAAG;UACH,OAAO;YACL;YACA,OAAO,eAAe,IAAI,MAAM,OAAO,MAAM,OAAO;;YAEpD;;AAGN,YAAM,SAAsB;QAC1B,GAAG;QACH,QAAQ;UACN,GAAG;UACH;UACA,SAAS;UACT,mBAAmB;;QAGrB,MAAM,KAAG;AACP,iBAAO,MAAM,MAAM,iBAAiB,GAAG,CAAC;;QAG1C,MAAM,KAAG;AACP,iBAAO,MAAM,MAAM,iBAAiB,GAAG,CAAC;;QAG1C,WAAW,KAAG;AACZ,gBAAM,EAAC,SAAS,WAAW,UAAS,IAAK,IAAI,MAAM;AACnD,cAAI,CAAC;AAAW,mBAAO,MAAM,WAAW,GAAG;AAE3C,mBAAS,oBAAoB,QAAoB;AAC/C,qBAAS,UAAW,KAAS;AAC3B,qBAAO,OACL,OAAO,SAAS,IAAI,KAAK,IAAI,UAAU,KAAK,UAAU,KAAK,SAAS,OAAO,CAAC,IAC5E,IAAI,SACF,OAAO,SACL,OAAO,IAAI,MAAM,GAAG,SAAS,EAC1B,OAAO,IAAI,UACR,KAAK,UACL,KAAK,SAAS,OAAO,CAAC,IAE9B,OAAO,SAAQ;;AAErB,kBAAM,gBAAgB,OAAO,OAAO,QAAQ;cAC1C,UAAU,EAAC,OAAO,UAAS;cAC3B,oBAAoB;gBAClB,MAAM,KAAUW,aAAe;AAC7B,yBAAO,mBAAmB,IAAI,KAAK,KAAK,SAAS,OAAO,GAAGA,WAAU;;;cAGzE,YAAY;gBACV,MAAG;AACD,yBAAO,OAAO;;;cAGlB,KAAK;gBACH,MAAG;AACD,wBAAM,MAAM,OAAO;AACnB,yBAAO,cAAc,IACnB,IAAI,CAAC,IACL,IAAI,MAAM,GAAG,SAAS;;;cAG5B,OAAO;gBACL,MAAG;AACD,yBAAO,OAAO;;;aAGnB;AACD,mBAAO;;AAGT,iBAAO,MAAM,WAAW,iBAAiB,GAAG,CAAC,EAC1C,KAAK,YAAU,UAAU,oBAAoB,MAAM,CAAC;;;AAG3D,aAAO;;;AAGb;AAEO,IAAM,yBAA8C;EACzD,OAAO;EACP,MAAM;EACN,OAAO;EACP,QAAQ;;SC1LM,cAAc,GAAQ,GAAQ,IAAU,MAAa;AAEnE,OAAK,MAAM,CAAA;AACX,SAAO,QAAQ;AACf,OAAK,CAAC,EAAE,QAAQ,CAAC,SAAI;AACnB,QAAI,CAAC,OAAO,GAAG,IAAI,GAAG;AAEpB,SAAG,OAAO,IAAI,IAAI;WACb;AACL,UAAI,KAAK,EAAE,IAAI,GACb,KAAK,EAAE,IAAI;AACb,UAAI,OAAO,OAAO,YAAY,OAAO,OAAO,YAAY,MAAM,IAAI;AAChE,cAAM,aAAa,YAAY,EAAE;AACjC,cAAM,aAAa,YAAY,EAAE;AAEjC,YAAI,eAAe,YAAY;AAC7B,aAAG,OAAO,IAAI,IAAI,EAAE,IAAI;mBACf,eAAe,UAAU;AAElC,wBAAc,IAAI,IAAI,IAAI,OAAO,OAAO,GAAG;mBAClC,OAAO,IAAI;AAKpB,aAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;iBAEjB,OAAO;AAAI,WAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;GAEjD;AACD,OAAK,CAAC,EAAE,QAAQ,CAAC,SAAI;AACnB,QAAI,CAAC,OAAO,GAAG,IAAI,GAAG;AACpB,SAAG,OAAO,IAAI,IAAI,EAAE,IAAI;;GAE3B;AACD,SAAO;AACT;SC9BgB,iBACd,YACA,KAAiI;AAGjI,MAAI,IAAI,SAAS;AAAU,WAAO,IAAI;AACtC,SAAO,IAAI,QAAQ,IAAI,OAAO,IAAI,WAAW,UAAU;AACzD;ACKO,IAAM,kBAAuC;EAClD,OAAO;EACP,MAAM;EACN,OAAO;EACP,QAAQ,CAAC,cAAsB;IAC7B,GAAG;IACH,MAAM,WAAiB;AACrB,YAAM,YAAY,SAAS,MAAM,SAAS;AAC1C,YAAM,EAAC,WAAU,IAAI,UAAU;AAE/B,YAAM,kBAA+B;QACnC,GAAG;QACH,OAAO,KAAG;AACR,gBAAM,UAAU,IAAI;AAGpB,gBAAM,EAAC,UAAU,UAAU,SAAQ,IAAI,QAAQ,MAAM,SAAS,EAAE;AAChE,kBAAQ,IAAI,MAAI;YACd,KAAK;AACH,kBAAI,SAAS,SAAS;AAAK;AAC3B,qBAAO,QAAQ,SAAS,aAAa,MAAI,eAAe,GAAG,GAAG,IAAI;YACpE,KAAK;AACH,kBAAI,SAAS,SAAS,OAAO,SAAS,SAAS;AAAK;AACpD,qBAAO,QAAQ,SAAS,aAAa,MAAI,eAAe,GAAG,GAAG,IAAI;YACpE,KAAK;AACH,kBAAI,SAAS,SAAS;AAAK;AAC3B,qBAAO,QAAQ,SAAS,aAAa,MAAI,eAAe,GAAG,GAAG,IAAI;YACpE,KAAK;AACH,kBAAI,SAAS,SAAS;AAAK;AAC3B,qBAAO,QAAQ,SAAS,aAAa,MAAI,YAAY,GAAG,GAAG,IAAI;;AAGnE,iBAAO,UAAU,OAAO,GAAG;AAG3B,mBAAS,eAAeV,MAA8D;AACpF,kBAAMW,WAAU,IAAI;AACpB,kBAAM/B,QAAOoB,KAAI,QAAQ,iBAAiB,YAAYA,IAAG;AACzD,gBAAI,CAACpB;AAAM,oBAAM,IAAI,MAAM,cAAc;AAEzC,YAAAoB,OAAMA,KAAI,SAAS,SAASA,KAAI,SAAS,QACvC,EAAC,GAAGA,MAAK,MAAApB,MAAI,IACb,EAAC,GAAGoB,KAAG;AACT,gBAAIA,KAAI,SAAS;AAAU,cAAAA,KAAI,SAAS,CAAC,GAAGA,KAAI,MAAM;AACtD,gBAAIA,KAAI;AAAM,cAAAA,KAAI,OAAO,CAAC,GAAGA,KAAI,IAAI;AAErC,mBAAO,kBAAkB,WAAWA,MAAKpB,KAAI,EAAE,KAAM,oBAAc;AACjE,oBAAM,WAAWA,MAAK,IAAI,CAAC,KAAK,MAAC;AAC/B,sBAAM,gBAAgB,eAAe,CAAC;AACtC,sBAAM,MAAM,EAAE,SAAS,MAAM,WAAW,KAAI;AAC5C,oBAAIoB,KAAI,SAAS,UAAU;AAEzB,2BAAS,KAAK,KAAK,KAAK,KAAK,eAAeW,QAAO;2BAC1CX,KAAI,SAAS,SAAS,kBAAkB,QAAW;AAE5D,wBAAM,sBAAsB,SAAS,KAAK,KAAK,KAAK,KAAKA,KAAI,OAAO,CAAC,GAAGW,QAAO;AAC/E,sBAAI,OAAO,QAAQ,uBAAuB,MAAM;AAC9C,0BAAM;AACN,oBAAAX,KAAI,KAAK,CAAC,IAAI;AACd,wBAAI,CAAC,WAAW,UAAU;AACxB,mCAAaA,KAAI,OAAO,CAAC,GAAG,WAAW,SAAS,GAAG;;;uBAGlD;AAEL,wBAAM,aAAa,cAAc,eAAeA,KAAI,OAAO,CAAC,CAAC;AAC7D,wBAAM,oBAAoB,SAAS,KAAK,KAAK,KAAK,YAAY,KAAK,eAAeW,QAAO;AACzF,sBAAI,mBAAmB;AACrB,0BAAM,iBAAiBX,KAAI,OAAO,CAAC;AACnC,2BAAO,KAAK,iBAAiB,EAAE,QAAQ,aAAO;AAC5C,0BAAI,OAAO,gBAAgB,OAAO,GAAG;AAEnC,uCAAe,OAAO,IAAI,kBAAkB,OAAO;6BAC9C;AAEL,qCAAa,gBAAgB,SAAS,kBAAkB,OAAO,CAAC;;qBAEnE;;;AAGL,uBAAO;eACR;AACD,qBAAO,UAAU,OAAOA,IAAG,EAAE,KAAK,CAAC,EAAC,UAAU,SAAS,aAAa,WAAU,MAAC;AAC7E,yBAAS,IAAE,GAAG,IAAEpB,MAAK,QAAQ,EAAE,GAAG;AAChC,wBAAM,UAAU,UAAU,QAAQ,CAAC,IAAIA,MAAK,CAAC;AAC7C,wBAAM,MAAM,SAAS,CAAC;AACtB,sBAAI,WAAW,MAAM;AACnB,wBAAI,WAAW,IAAI,QAAQ,SAAS,CAAC,CAAC;yBACjC;AACL,wBAAI,aAAa,IAAI;sBACnBoB,KAAI,SAAS,SAAS,eAAe,CAAC,IACpCA,KAAI,OAAO,CAAC,IACZ;;;;AAIR,uBAAO,EAAC,UAAU,SAAS,aAAa,WAAU;eACnD,EAAE,MAAM,WAAK;AACZ,yBAAS,QAAQ,SAAO,IAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACzD,uBAAO,QAAQ,OAAO,KAAK;eAC5B;aACF;;AAGH,mBAAS,YAAYA,MAA6B;AAChD,mBAAO,gBAAgBA,KAAI,OAAOA,KAAI,OAAO,GAAK;;AAGpD,mBAAS,gBAAgB,OAA0B,OAAuB,OAAa;AAErF,mBAAO,UAAU,MAAM,EAAC,OAAO,QAAQ,OAAO,OAAO,EAAC,OAAO,YAAY,MAAK,GAAG,MAAK,CAAC,EACtF,KAAK,CAAC,EAAC,OAAM,MAAC;AAGb,qBAAO,eAAe,EAAC,MAAM,UAAU,MAAM,QAAQ,MAAK,CAAC,EAAE,KAAK,SAAG;AACnE,oBAAI,IAAI,cAAc;AAAG,yBAAO,QAAQ,OAAO,IAAI,SAAS,CAAC,CAAC;AAC9D,oBAAI,OAAO,SAAS,OAAO;AACzB,yBAAO,EAAC,UAAU,CAAA,GAAI,aAAa,GAAG,YAAY,OAAS;uBACtD;AACL,yBAAO,gBAAgB,OAAO,EAAC,GAAG,OAAO,OAAO,OAAO,OAAO,SAAS,CAAC,GAAG,WAAW,KAAI,GAAG,KAAK;;eAErG;aACF;;;;AAMP,aAAO;;;;AAKb,SAAS,kBACP,OACA,KACA,eAAoB;AAEpB,SAAO,IAAI,SAAS,QAChB,QAAQ,QAAQ,CAAA,CAAE,IAClB,MAAM,QAAQ,EAAE,OAAO,IAAI,OAAO,MAAM,eAAe,OAAO,YAAW,CAAE;AACjF;SC3JgB,wBACdpB,OACA,OACA,OAAe;AAEf,MAAI;AACF,QAAI,CAAC;AAAO,aAAO;AACnB,QAAI,MAAM,KAAK,SAASA,MAAK;AAAQ,aAAO;AAC5C,UAAM,SAAgB,CAAA;AAItB,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,KAAK,UAAU,IAAIA,MAAK,QAAQ,EAAE,GAAG;AACpE,UAAI,IAAI,MAAM,KAAK,CAAC,GAAGA,MAAK,CAAC,CAAC,MAAM;AAAG;AACvC,aAAO,KAAK,QAAQ,UAAU,MAAM,OAAO,CAAC,CAAC,IAAI,MAAM,OAAO,CAAC,CAAC;AAChE,QAAE;;AAGJ,WAAO,OAAO,WAAWA,MAAK,SAAS,SAAS;WAChD,IAAM;AACN,WAAO;;AAEX;AAEO,IAAM,gCAAoD;EAC/D,OAAO;EACP,OAAO;EACP,QAAQ,CAAC,SAAI;AACX,WAAO;MACL,OAAO,CAAC,cAAS;AACf,cAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,eAAO;UACL,GAAG;UACH,SAAS,CAAC,QAAG;AACX,gBAAI,CAAC,IAAI,OAAO;AACd,qBAAO,MAAM,QAAQ,GAAG;;AAE1B,kBAAM,eAAe,wBACnB,IAAI,MACJ,IAAI,MAAM,QAAQ,GAClB,IAAI,UAAU,OAAO;AAEvB,gBAAI,cAAc;AAChB,qBAAOD,aAAQ,QAAQ,YAAY;;AAErC,mBAAO,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAC,QAAG;AACjC,kBAAI,MAAM,QAAQ,IAAI;gBACpB,MAAM,IAAI;gBACV,QAAQ,IAAI,UAAU,UAAU,UAAU,GAAG,IAAI;;AAEnD,qBAAO;aACR;;UAEH,QAAQ,CAAC,QAAG;AAEV,gBAAI,IAAI,SAAS;AAAO,kBAAI,MAAM,QAAQ,IAAI;AAC9C,mBAAO,MAAM,OAAO,GAAG;;;;;;;AC7CnC,SAAS,aAAa,MAA6D;AACjF,SAAO,EAAE,UAAU;AACrB;IAIa,WAAW,SAAS,YAAiB,IAAQ;AACxD,MAAI,MAAM;AAER,WAAO,MAAM,UAAU,SAAS,EAAC,GAAE,GAAG,MAAM,YAAY,IAAI,UAAU,SAAS,IAAI,KAAK,WAAU,IAAI,EAAC,GAAE,EAAC,CAAC;SACtG;AAEL,UAAM,KAAK,IAAI,SAAQ;AACvB,QAAI,cAAe,OAAO,YAAa;AACrC,aAAO,IAAI,UAAU;;AAEvB,WAAO;;AAEX;AAEA,MAAM,SAAS,WAAW;EACxB,IAAI,UAAiE;AACnE,gBAAY,MAAM,QAAQ;AAC1B,WAAO;;EAET,OAAO,KAAkB;AACvB,aAAS,MAAM,KAAK,GAAG;AACvB,WAAO;;EAET,QAAQC,OAAqB;AAC3B,IAAAA,MAAK,QAAQ,SAAO,SAAS,MAAM,KAAK,GAAG,CAAC;AAC5C,WAAO;;EAGT,CAAC,cAAc,IAAC;AACd,WAAO,oBAAoB,IAAI;;CAElC;AAED,SAAS,SAAS,QAAsB,MAAqB,IAAiB;AAC5E,QAAM,OAAO,IAAI,MAAM,EAAE;AAGzB,MAAI,MAAM,IAAI;AAAG;AAGjB,MAAI,OAAO;AAAG,UAAM,WAAU;AAE9B,MAAI,aAAa,MAAM;AAAG,WAAO,OAAO,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAC,CAAE;AAClE,QAAM,OAAO,OAAO;AACpB,QAAM,QAAQ,OAAO;AACrB,MAAI,IAAI,IAAI,OAAO,IAAI,IAAI,GAAG;AAC5B,WACI,SAAS,MAAM,MAAM,EAAE,IACtB,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,KAAI;AAClD,WAAO,UAAU,MAAM;;AAEzB,MAAI,IAAI,MAAM,OAAO,EAAE,IAAI,GAAG;AAC5B,YACI,SAAS,OAAO,MAAM,EAAE,IACvB,OAAO,IAAI,EAAE,MAAM,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,KAAI;AAClD,WAAO,UAAU,MAAM;;AAKzB,MAAI,IAAI,MAAM,OAAO,IAAI,IAAI,GAAG;AAC9B,WAAO,OAAO;AACd,WAAO,IAAI;AACX,WAAO,IAAI,QAAQ,MAAM,IAAI,IAAI;;AAGnC,MAAI,IAAI,IAAI,OAAO,EAAE,IAAI,GAAG;AAC1B,WAAO,KAAK;AACZ,WAAO,IAAI;AACX,WAAO,IAAI,OAAO,IAAI,OAAO,EAAE,IAAI,IAAI;;AAEzC,QAAM,iBAAiB,CAAC,OAAO;AAE/B,MAAI,QAAQ,CAAC,OAAO,GAAG;AAGrB,gBAAY,QAAQ,IAAI;;AAG1B,MAAI,SAAS,gBAAgB;AAG3B,gBAAY,QAAQ,KAAK;;AAE7B;SAEgB,YAAY,QAAsB,QAA+D;AAC/G,WAAS,aACPgC,SACA,EAAE,MAAM,IAAI,GAAG,EAAC,GAA6F;AAE7G,aAASA,SAAQ,MAAM,EAAE;AACzB,QAAI;AAAG,mBAAaA,SAAQ,CAAC;AAC7B,QAAI;AAAG,mBAAaA,SAAQ,CAAC;;AAG/B,MAAG,CAAC,aAAa,MAAM;AAAG,iBAAa,QAAQ,MAAM;AACvD;SAEgB,cACd,WACA,WAAuB;AAGrB,QAAM,KAAK,oBAAoB,SAAS;AACxC,MAAI,cAAc,GAAG,KAAI;AACzB,MAAI,YAAY;AAAM,WAAO;AAC7B,MAAI,IAAI,YAAY;AAGpB,QAAM,KAAK,oBAAoB,SAAS;AACxC,MAAI,cAAc,GAAG,KAAK,EAAE,IAAI;AAChC,MAAI,IAAI,YAAY;AAEpB,SAAO,CAAC,YAAY,QAAQ,CAAC,YAAY,MAAM;AAC7C,QAAI,IAAI,EAAG,MAAM,EAAE,EAAE,KAAK,KAAK,IAAI,EAAG,IAAI,EAAE,IAAI,KAAK;AAAG,aAAO;AAC/D,QAAI,EAAE,MAAM,EAAG,IAAI,IAAI,IAClB,KAAK,cAAc,GAAG,KAAK,EAAG,IAAI,GAAG,QACrC,KAAK,cAAc,GAAG,KAAK,EAAE,IAAI,GAAG;;AAE7C,SAAO;AACT;SAUgB,oBACd,MAAmC;AAEnC,MAAI,QAA+B,aAAa,IAAI,IAAI,OAAO,EAAE,GAAG,GAAG,GAAG,KAAI;AAE9E,SAAO;IACL,KAAK,KAAI;AACP,YAAM,cAAc,UAAU,SAAS;AACvC,aAAO,OAAO;AACZ,gBAAQ,MAAM,GAAC;UACb,KAAK;AAGH,kBAAM,IAAI;AACV,gBAAI,aAAa;AACf,qBAAO,MAAM,EAAE,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI;AAC3C,wBAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;mBACpC;AACL,qBAAO,MAAM,EAAE;AAAG,wBAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;;UAG7D,KAAK;AAEH,kBAAM,IAAI;AACV,gBAAI,CAAC,eAAe,IAAI,KAAK,MAAM,EAAE,EAAE,KAAK;AAC1C,qBAAO,EAAE,OAAO,MAAM,GAAG,MAAM,MAAK;UACxC,KAAK;AAEH,gBAAI,MAAM,EAAE,GAAG;AACb,oBAAM,IAAI;AACV,sBAAQ,EAAE,IAAI,OAAO,GAAG,MAAM,EAAE,GAAG,GAAG,EAAC;AACvC;;UAGJ,KAAK;AACH,oBAAQ,MAAM;;;AAGpB,aAAO,EAAE,MAAM,KAAI;;;AAGzB;AAEA,SAAS,UAAU,QAAwB;;AACzC,QAAM,UAAQ,KAAA,OAAO,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK,QAAM,KAAA,OAAO,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;AAClD,QAAM,IAAI,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM;AAC7C,MAAI,GAAG;AAsBL,UAAM,IAAI,MAAM,MAAM,MAAM;AAC5B,UAAM,YAAY,EAAE,GAAG,OAAM;AAI7B,UAAM,eAAe,OAAO,CAAC;AAC7B,WAAO,OAAO,aAAa;AAC3B,WAAO,KAAK,aAAa;AACzB,WAAO,CAAC,IAAI,aAAa,CAAC;AAC1B,cAAU,CAAC,IAAI,aAAa,CAAC;AAC7B,WAAO,CAAC,IAAI;AACZ,cAAU,IAAI,aAAa,SAAS;;AAEtC,SAAO,IAAI,aAAa,MAAM;AAChC;AAEA,SAAS,aAAa,EAAE,GAAG,EAAC,GAAqC;AAC/D,UAAQ,IAAK,IAAI,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,IAAK,IAAI,EAAE,IAAI,KAAK;AAC9D;AC1NO,IAAM,0BAA8C;EACzD,OAAO;EACP,OAAO;EACP,QAAQ,CAAC,SAAI;AACX,UAAM,SAAS,KAAK,OAAO;AAC3B,UAAM,aAAa,IAAI,SAAS,KAAK,SAAS,KAAK,OAAO;AAE1D,WAAO;MACL,GAAG;MACH,OAAO,CAAC,cAAS;AACf,cAAM,QAAQ,KAAK,MAAM,SAAS;AAClC,cAAM,EAAE,OAAM,IAAK;AACnB,cAAM,EAAE,WAAU,IAAK;AACvB,cAAM,EAAE,YAAY,SAAQ,IAAK;AACjC,cAAM,aAA0B;UAC9B,GAAG;UACH,QAAQ,CAAC,QAAG;AACV,kBAAM,QAAQ,IAAI;AAGlB,kBAAM,eACJ,MAAM,iBAAiB,MAAM,eAAe,CAAA;AAC9C,kBAAM,cAAc,CAAC,cAAiB;AACpC,oBAAM,OAAO,SAAS,MAAM,IAAI,SAAS,IAAI,SAAS;AACtD,qBAAQ,aAAa,IAAI,MACtB,aAAa,IAAI,IAAI,IAAI,SAAQ;;AAEtC,kBAAM,aAAa,YAAY,EAAE;AACjC,kBAAM,eAAe,YAAY,OAAO;AAExC,kBAAM,EAAE,MAAAtC,MAAI,IAAK;AACjB,gBAAI,CAACM,OAAM,OAAO,IAChB,IAAI,SAAS,gBACT,CAAC,IAAI,KAAK,IACV,IAAI,SAAS,WACb,CAAC,IAAI,IAAI,IACT,IAAI,OAAO,SAAS,KACpB,CAAC,CAAA,GAAI,IAAI,MAAM,IACf,CAAA;AACN,kBAAM,WAAW,IAAI,MAAM,QAAQ;AACnC,mBAAO,MAAM,OAAO,GAAG,EAAE,KAAK,CAAC,QAAG;AAGhC,kBAAI,QAAQA,KAAI,GAAG;AAEjB,oBAAIN,UAAS;AAAU,kBAAAM,QAAO,IAAI;AAElC,2BAAW,QAAQA,KAAI;AAGvB,sBAAM,UAAU,wBAAwBA,OAAM,QAAQ;AAGtD,oBAAI,CAAC,WAAWN,UAAS,OAAO;AAG9B,+BAAa,QAAQM,KAAI;;AAE3B,oBAAI,WAAW,SAAS;AAEtB,uCAAqB,aAAa,QAAQ,SAAS,OAAO;;yBAEnDA,OAAM;AAEf,sBAAM,QAAQ,EAAE,MAAMA,MAAK,OAAO,IAAIA,MAAK,MAAK;AAChD,6BAAa,IAAI,KAAK;AAEtB,2BAAW,IAAI,KAAK;qBACf;AAIL,2BAAW,IAAI,UAAU;AACzB,6BAAa,IAAI,UAAU;AAC3B,uBAAO,QAAQ,QAAQ,SAAO,YAAY,IAAI,IAAI,EAAE,IAAI,UAAU,CAAC;;AAErE,qBAAO;aACR;;;AAIL,cAAM,WAAkD,CAAC,EACvD,OAAO,EAAE,OAAO,MAAK,EAAE,MAIE;;AAAK,iBAAA;YAC9B;YACA,IAAI,UAAS,KAAA,MAAM,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK,UAAS,KAAA,MAAM,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK,OAAO;;;AAGvE,cAAM,kBAGF;UACF,KAAK,CAAC,QAAQ,CAAC,YAAY,IAAI,SAAS,IAAI,GAAG,CAAC;UAChD,SAAS,CAAC,QAAQ,CAAC,YAAY,IAAI,SAAQ,EAAG,QAAQ,IAAI,IAAI,CAAC;UAC/D,OAAO;UACP,OAAO;UACP,YAAY;;AAGd,aAAK,eAAe,EAAE,QAAQ,YAAM;AAClC,qBAAW,MAAM,IAAI,SACnB,KAK2B;AAE3B,kBAAM,EAAE,OAAM,IAAK;AACnB,gBAAI,QAAQ;AAKV,oBAAM,cAAc,CAAC,cAAiB;AACpC,sBAAM,OAAO,SAAS,MAAM,IAAI,SAAS,IAAI,SAAS;AACtD,uBAAQ,OAAO,IAAI,MAChB,OAAO,IAAI,IAAI,IAAI,SAAQ;;AAEhC,oBAAM,aAAa,YAAY,EAAE;AACjC,oBAAM,eAAe,YAAY,OAAO;AACxC,oBAAM,CAAC,cAAc,aAAa,IAAI,gBAAgB,MAAM,EAAE,GAAG;AAEjE,0BAAY,aAAa,QAAQ,EAAE,EAAE,IAAI,aAAa;AACtD,kBAAI,CAAC,aAAa,cAAc;AAU9B,oBAAI,WAAW,SAAS;AAKtB,+BAAa,IAAI,UAAU;uBACtB;AAIL,wBAAM,cACJ,WAAW,WACX,YACC,IAA2B,UAC5B,MAAM,MAAM;oBACV,GAAI;oBACJ,QAAQ;mBACT;AAEH,yBAAO,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS,EAAE,KAAK,CAAC,QAAG;AACnD,wBAAI,WAAW,SAAS;AACtB,0BAAI,YAAa,IAA2B,QAAQ;AAMlD,+BAAO,YAAY,KACjB,CAAC,EAAE,QAAQ,cAAa,MAAuB;AAC7C,qCAAW,QAAQ,aAAa;AAChC,iCAAO;yBACR;;AAKL,4BAAM,QAAS,IAA2B,SACrC,IAA4B,OAAO,IAAI,UAAU,IACjD,IAA4B;AACjC,0BAAK,IAA2B,QAAQ;AAGtC,mCAAW,QAAQ,KAAK;6BACnB;AAQL,qCAAa,QAAQ,KAAK;;+BAEnB,WAAW,cAAc;AAKlC,4BAAM,SAA8B;AACpC,4BAAM,aAAc,IAAgC;AACpD,6BACE,UACA,OAAO,OAAO,QAAQ;wBACpB,KAAK;0BACH,MAAG;AACD,yCAAa,OAAO,OAAO,UAAU;AACrC,mCAAO,OAAO;;;wBAGlB,YAAY;0BACV,MAAG;AACD,kCAAM,OAAO,OAAO;AACpB,yCAAa,OAAO,IAAI;AACxB,mCAAO;;;wBAGX,OAAO;0BACL,MAAG;AACD,0CAAc,WAAW,OAAO,OAAO,UAAU;AACjD,mCAAO,OAAO;;;uBAGnB;;AAGL,2BAAO;mBACR;;;;AAIP,mBAAO,MAAM,MAAM,EAAE,MAAM,MAAM,SAAS;;SAE7C;AACD,eAAO;;;;;AAMf,SAAS,qBACP,aACA,QACA,SACA,SAA0B;AAE1B,WAAS,iBAAiB,IAAe;AACvC,UAAM,WAAW,YAAY,GAAG,QAAQ,EAAE;AAC1C,aAAS,WAAW,KAAQ;AAC1B,aAAO,OAAO,OAAO,GAAG,WAAW,GAAG,IAAI;;AAE5C,UAAM,eAAe,CAAC,QAAa,GAAG,cAAc,QAAQ,GAAG,IAE3D,IAAI,QAAQ,CAAAiC,SAAO,SAAS,OAAOA,IAAG,CAAC,IAEvC,SAAS,OAAO,GAAG;AAEvB,KAAC,WAAW,SAAS,QAAQ,CAAC,GAAG,MAAC;AAChC,YAAM,SAAS,WAAW,WAAW,QAAQ,CAAC,CAAC;AAC/C,YAAM,SAAS,WAAW,WAAW,QAAQ,CAAC,CAAC;AAC/C,UAAI,IAAI,QAAQ,MAAM,MAAM,GAAG;AAE7B,YAAI,UAAU;AAAM,uBAAa,MAAM;AACvC,YAAI,UAAU;AAAM,uBAAa,MAAM;;KAE1C;;AAEH,SAAO,QAAQ,QAAQ,gBAAgB;AACzC;ICjOaP,gBAAAA,SAAK;EA6BhB,YAAY,MAAc,SAAsB;AAjBhD,SAAA,eAA0F,CAAA;AAM1F,SAAA,QAAgB;AAYd,UAAM,OAAQA,SAAkC;AAChD,SAAK,WAAW,UAAU;MAExB,QAASA,SAAkC;MAC3C,UAAU;MAEV,WAAW,KAAK;MAChB,aAAa,KAAK;MAClB,GAAG;;AAEL,SAAK,QAAQ;MACX,WAAW,QAAQ;MACnB,aAAa,QAAQ;;AAEvB,UAAM,EACJ,OAAM,IACJ;AACJ,SAAK,YAAY,CAAA;AACjB,SAAK,YAAY,CAAA;AACjB,SAAK,cAAc,CAAA;AACnB,SAAK,aAAa,CAAA;AAClB,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,UAAM,QAAsB;MAC1B,aAAa;MACb,eAAe;MACf,mBAAmB;MACnB,cAAc;MACd,gBAAgB;MAChB,gBAAgB;MAChB,YAAY;MACZ,eAAe;MACf,YAAY;MACZ,gBAAgB;;AAElB,UAAM,iBAAiB,IAAI3B,aAAQ,aAAO;AACxC,YAAM,iBAAiB;KACxB;AACD,UAAM,gBAAgB,IAAIA,aAAQ,CAAC,GAAG,WAAM;AAC1C,YAAM,aAAa;KACpB;AACD,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,KAAK,OAAO,MAAM,YAAY,WAAW,iBAAiB,SAAS,EAAE,OAAO,CAAC,iBAAiB,GAAG,EAAC,CAAE;AACzG,SAAK,GAAG,MAAM,YAAY,SAAS,KAAK,GAAG,MAAM,WAAW,eAAS;AACnE,aAAO,CAAC,YAAY,YAAO;AACxB2B,iBAAkC,IAAI,MAAA;AACrC,gBAAMQ,SAAQ,KAAK;AACnB,cAAIA,OAAM,cAAc;AAEtB,gBAAI,CAACA,OAAM;AAAanC,2BAAQ,QAAO,EAAG,KAAK,UAAU;AAEzD,gBAAI;AAAS,wBAAU,UAAU;qBACxBmC,OAAM,mBAAmB;AAElC,YAAAA,OAAM,kBAAkB,KAAK,UAAU;AACvC,gBAAI;AAAS,wBAAU,UAAU;iBAC5B;AAEL,sBAAU,UAAU;AAEpB,kBAAM,KAAK;AACX,gBAAI,CAAC;AAAS,wBAAU,SAAS,cAAW;AAC1C,mBAAG,GAAG,MAAM,YAAY,UAAU;AAClC,mBAAG,GAAG,MAAM,YAAY,WAAW;eACpC;;SAEJ;;KAEJ;AAGD,SAAK,aAAa,4BAA4B,IAAI;AAClD,SAAK,QAAQ,uBAAuB,IAAI;AACxC,SAAK,cAAc,6BAA6B,IAAI;AACpD,SAAK,UAAU,yBAAyB,IAAI;AAC5C,SAAK,cAAc,6BAA6B,IAAI;AAKpD,SAAK,GAAG,iBAAiB,QAAE;AAKzB,UAAI,GAAG,aAAa;AAClB,gBAAQ,KAAK,iDAAiD,KAAK,IAAI,0CAA0C;;AAEjH,gBAAQ,KAAK,gDAAgD,KAAK,IAAI,iDAAiD;AACzH,WAAK,MAAK;KAOX;AACD,SAAK,GAAG,WAAW,QAAE;AACnB,UAAI,CAAC,GAAG,cAAc,GAAG,aAAa,GAAG;AACvC,gBAAQ,KAAK,iBAAiB,KAAK,IAAI,gBAAgB;;AAEvD,gBAAQ,KAAK,YAAY,KAAK,IAAI,iDAAiD,GAAG,aAAa,EAAE,EAAE;KAC1G;AAED,SAAK,UAAU,UAAU,QAAQ,WAAiC;AAElE,SAAK,qBAAqB,CACxB,MACA,YACA,UACA,sBAAoC,IAAI,KAAK,YAAY,MAAM,YAAY,UAAU,KAAK,SAAS,6BAA6B,iBAAiB;AAEnJ,SAAK,iBAAiB,QAAE;AACtB,WAAK,GAAG,SAAS,EAAE,KAAK,EAAE;AAE1B,kBACG,OAAO,OAAK,EAAE,SAAS,KAAK,QAAQ,MAAM,QAAQ,CAAC,EAAE,OAAO,OAAO,EACnE,IAAI,OAAK,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,CAAC;;AAI5C,SAAK,IAAI,sBAAsB;AAC/B,SAAK,IAAI,eAAe;AACxB,SAAK,IAAI,uBAAuB;AAChC,SAAK,IAAI,6BAA6B;AAEtC,SAAK,MAAM,OAAO,OAAO,MAAM,EAAC,MAAM,EAAC,OAAO,KAAI,EAAC,CAAC;AAGpD,WAAO,QAAQ,WAAS,MAAM,IAAI,CAAC;;EAGrC,QAAQ,eAAqB;AAC3B,QAAI,MAAM,aAAa,KAAK,gBAAgB;AAAK,YAAM,IAAI,WAAW,KAAK,wCAAwC;AACnH,oBAAgB,KAAK,MAAM,gBAAgB,EAAE,IAAI;AACjD,QAAI,KAAK,SAAS,KAAK,OAAO;AAC5B,YAAM,IAAI,WAAW,OAAO,0CAA0C;AACxE,SAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,aAAa;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI,kBAAkB,SAAS,OAC7B,OAAK,EAAE,KAAK,YAAY,aAAa,EAAE,CAAC;AAC1C,QAAI;AAAiB,aAAO;AAC5B,sBAAkB,IAAI,KAAK,QAAQ,aAAa;AAChD,aAAS,KAAK,eAAe;AAC7B,aAAS,KAAK,iBAAiB;AAC/B,oBAAgB,OAAO,CAAA,CAAE;AAEzB,SAAK,OAAO,aAAa;AACzB,WAAO;;EAGT,WAAc,IAAoB;AAChC,WAAQ,KAAK,UAAU,KAAK,OAAO,gBAAgB,IAAI,cAAc,KAAK,QAAS,GAAE,IAAK,IAAInC,aAAW,CAAC,SAAS,WAAM;AACvH,UAAI,KAAK,OAAO,cAAc;AAG5B,eAAO,OAAO,IAAI,WAAW,eAAe,KAAK,OAAO,WAAW,CAAC;;AAEtE,UAAI,CAAC,KAAK,OAAO,eAAe;AAC9B,YAAI,CAAC,KAAK,SAAS,UAAU;AAC3B,iBAAO,IAAI,WAAW,eAAc,CAAE;AACtC;;AAEF,aAAK,KAAI,EAAG,MAAM,GAAG;;AAEvB,WAAK,OAAO,eAAe,KAAK,SAAS,MAAM;KAChD,EAAE,KAAK,EAAE;;EAGZ,IAAI,EAAC,OAAO,QAAQ,OAAO,KAAI,GAAqB;AAClD,QAAI;AAAM,WAAK,MAAM,EAAC,OAAO,KAAI,CAAC;AAClC,UAAM,cAAc,KAAK,aAAa,KAAK,MAAM,KAAK,aAAa,KAAK,IAAI,CAAA;AAC5E,gBAAY,KAAK,EAAC,OAAO,QAAQ,OAAO,SAAS,OAAO,KAAK,OAAO,KAAI,CAAC;AACzE,gBAAY,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAG5C,WAAO;;EAKT,MAAM,EAAC,OAAO,MAAM,OAAM,GAA+D;AACvF,QAAI,SAAS,KAAK,aAAa,KAAK,GAAG;AACrC,WAAK,aAAa,KAAK,IAAI,KAAK,aAAa,KAAK,EAAE,OAAO,QACzD,SAAS,GAAG,WAAW,SACvB,OAAO,GAAG,SAAS,OACnB,KAAK;;AAET,WAAO;;EAGT,OAAI;AACF,WAAO,UAAU,IAAI;;EAGvB,SAAM;AACJ,UAAM,QAAQ,KAAK;AACnB,UAAM,MAAM,YAAY,QAAQ,IAAI;AACpC,QAAI,OAAO;AAAG,kBAAY,OAAO,KAAK,CAAC;AACvC,QAAI,KAAK,OAAO;AACd,UAAI;AAAE,aAAK,MAAM,MAAK;eAAa,GAAG;MAAA;AACtC,WAAK,OAAO,QAAQ;;AAGtB,UAAM,iBAAiB,IAAIA,aAAQ,aAAO;AACxC,YAAM,iBAAiB;KACxB;AACD,UAAM,gBAAgB,IAAIA,aAAQ,CAAC,GAAG,WAAM;AAC1C,YAAM,aAAa;KACpB;;EAGH,QAAK;AACH,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK;AACnB,SAAK,SAAS,WAAW;AACzB,UAAM,cAAc,IAAI,WAAW,eAAc;AACjD,QAAI,MAAM;AACR,YAAM,WAAW,MAAM,WAAW;;EAGtC,SAAM;AACJ,UAAM,eAAe,UAAU,SAAS;AACxC,UAAM,QAAQ,KAAK;AACnB,WAAO,IAAIA,aAAQ,CAAC,SAAS,WAAM;AACjC,YAAM,WAAW,MAAA;AACf,aAAK,MAAK;AACV,YAAI,MAAM,KAAK,MAAM,UAAU,eAAe,KAAK,IAAI;AACvD,YAAI,YAAY,KAAK,MAAA;AACnB,6BAAmB,KAAK,OAAO,KAAK,IAAI;AACxC,kBAAO;SACR;AACD,YAAI,UAAU,mBAAmB,MAAM;AACvC,YAAI,YAAY,KAAK;;AAGvB,UAAI;AAAc,cAAM,IAAI,WAAW,gBAAgB,sCAAsC;AAC7F,UAAI,MAAM,eAAe;AACvB,cAAM,eAAe,KAAK,QAAQ;aAC7B;AACL,iBAAQ;;KAEX;;EAGH,YAAS;AACP,WAAO,KAAK;;EAGd,SAAM;AACJ,WAAO,KAAK,UAAU;;EAGxB,gBAAa;AACX,UAAM,cAAc,KAAK,OAAO;AAChC,WAAO,eAAgB,YAAY,SAAS;;EAG9C,YAAS;AACP,WAAO,KAAK,OAAO,gBAAgB;;EAGrC,oBAAiB;AACf,WAAO,KAAK,OAAO;;EAGrB,IAAI,SAAM;AACR,WAAO,KAAK,KAAK,UAAU,EAAE,IAAI,UAAQ,KAAK,WAAW,IAAI,CAAC;;EAGhE,cAAW;AACT,UAAM,OAAO,uBAAuB,MAAM,MAAM,SAAS;AACzD,WAAO,KAAK,aAAa,MAAM,MAAM,IAAI;;EAG3C,aAAa,MAAuB,QAAgC,WAAmB;AACrF,QAAI,oBAAoB,IAAI;AAE5B,QAAI,CAAC,qBAAqB,kBAAkB,OAAO,QAAQ,KAAK,QAAQ,GAAG,MAAM;AAAI,0BAAoB;AACzG,UAAM,mBAAmB,KAAK,QAAQ,GAAG,MAAM;AAC/C,WAAO,KAAK,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE;AAC5C,QAAI,SACA;AAEJ,QAAI;AAIA,mBAAa,OAAO,IAAI,WAAK;AACzB,YAAI,YAAY,iBAAiB,KAAK,QAAQ,MAAM,OAAO;AAC3D,YAAI,OAAO,cAAc;AAAU,gBAAM,IAAI,UAAU,iFAAiF;AACxI,eAAO;OACV;AAKD,UAAI,QAAQ,OAAO,SAAS;AAC1B,kBAAU;eACH,QAAQ,QAAQ,QAAQ;AAC/B,kBAAU;;AAER,cAAM,IAAI,WAAW,gBAAgB,+BAA+B,IAAI;AAE5E,UAAI,mBAAmB;AAEnB,YAAI,kBAAkB,SAAS,YAAY,YAAY,WAAW;AAC9D,cAAI,kBAAkB;AAElB,gCAAoB;;AAEnB,kBAAM,IAAI,WAAW,eAAe,wFAAwF;;AAErI,YAAI,mBAAmB;AACnB,qBAAW,QAAQ,eAAS;AACxB,gBAAI,qBAAqB,kBAAkB,WAAW,QAAQ,SAAS,MAAM,IAAI;AAC7E,kBAAI,kBAAkB;AAElB,oCAAoB;;AAEnB,sBAAM,IAAI,WAAW,eAAe,WAAW,YAChD,sCAAsC;;WAEjD;;AAEL,YAAI,oBAAoB,qBAAqB,CAAC,kBAAkB,QAAQ;AAEpE,8BAAoB;;;aAGvB,GAAG;AACR,aAAO,oBACH,kBAAkB,SAAS,MAAM,CAAC,GAAG,WAAM;AAAM,eAAO,CAAC;MAAE,CAAC,IAC5D,UAAW,CAAC;;AAGpB,UAAM,mBAAmB,sBAAsB,KAAK,MAAM,MAAM,SAAS,YAAY,mBAAmB,SAAS;AACjH,WAAQ,oBACJ,kBAAkB,SAAS,SAAS,kBAAkB,MAAM,IAC5D,IAAI,QAIA,OAAO,IAAI,WAAW,MAAI,KAAK,WAAW,gBAAgB,CAAC,IAC3D,KAAK,WAAY,gBAAgB;;EAK3C,MAAM,WAAiB;AACrB,QAAI,CAAC,OAAO,KAAK,YAAY,SAAS,GAAG;AACvC,YAAM,IAAI,WAAW,aAAa,SAAS,SAAS,iBAAiB;;AACvE,WAAO,KAAK,WAAW,SAAS;;;ACtbpC,IAAM,mBACJ,OAAO,WAAW,eAAe,gBAAgB,SAC7C,OAAO,aACP;IAEO,mBAAU;EAKrB,YAAY,WAAkD;AAC5D,SAAK,aAAa;;EASpB,UAAU,GAAS,OAAa,UAAc;AAC5C,WAAO,KAAK,WACV,CAAC,KAAK,OAAO,MAAM,aAAa,EAAE,MAAM,GAAG,OAAO,SAAQ,IAAK,CAAC;;EAIpE,CAAC,gBAAgB,IAAC;AAChB,WAAO;;;SC7BK,uBACd,QACA,QAAwB;AAExB,OAAK,MAAM,EAAE,QAAQ,UAAI;AACvB,UAAM,WAAW,OAAO,IAAI,MAAM,OAAO,IAAI,IAAI,IAAI,SAAQ;AAC7D,gBAAY,UAAU,OAAO,IAAI,CAAC;GACnC;AACD,SAAO;AACT;SCKgB,UAAa,SAA6B;AACxD,MAAI,WAAW;AACf,MAAI,eAAkB;AACtB,QAAM,aAAa,IAAI,WAAc,CAAC,aAAQ;AAC5C,UAAM,mBAAmB,gBAAgB,OAAO;AAChD,aAAS,QAAQ,QAAwB;AACvC,UAAI,kBAAkB;AACpB,gCAAuB;;AAEzB,YAAM,OAAO,MAAM,SAAS,SAAS,EAAE,QAAQ,OAAO,KAAI,CAAE;AAC5D,YAAM,KAAK,IAAI,QAEX,OAAO,IAAI,WAAW,IAAI,IAC1B,KAAI;AACR,UAAI,kBAAkB;AACnB,WAAoB,KACnB,yBACA,uBAAuB;;AAG3B,aAAO;;AAGT,QAAI,SAAS;AAEb,QAAI,YAA8B,CAAA;AAClC,QAAI,aAA+B,CAAA;AAEnC,UAAM,eAA6B;MACjC,IAAI,SAAM;AACR,eAAO;;MAET,aAAa,MAAA;AACX,iBAAS;AACT,qBAAa,eAAe,YAAY,gBAAgB;;;AAI5D,aAAS,SAAS,SAAS,MAAM,YAAY;AAE7C,QAAI,WAAW,OACb,mBAAmB;AAErB,aAAS,eAAY;AACnB,aAAO,KAAK,UAAU,EAAE,KACtB,CAAC,QACC,UAAU,GAAG,KAAK,cAAc,UAAU,GAAG,GAAG,WAAW,GAAG,CAAC,CAAC;;AAItE,UAAM,mBAAmB,CAAC,UAAuB;AAC/C,6BAAuB,WAAW,KAAK;AACvC,UAAI,aAAY,GAAI;AAClB,gBAAO;;;AAIX,UAAM,UAAU,MAAA;AACd,UAAI,YAAY;AAAQ;AACxB,kBAAY,CAAA;AACZ,YAAM,SAA2B,CAAA;AACjC,YAAM,MAAM,QAAQ,MAAM;AAC1B,UAAI,CAAC,kBAAkB;AACrB,qBAAa,kCAAkC,gBAAgB;AAC/D,2BAAmB;;AAErB,iBAAW;AACX,cAAQ,QAAQ,GAAG,EAAE,KACnB,CAAC,WAAM;AACL,mBAAW;AACX,uBAAe;AACf,mBAAW;AACX,YAAI;AAAQ;AACZ,YAAI,aAAY,GAAI;AAElB,kBAAO;eACF;AACL,sBAAY,CAAA;AAEZ,uBAAa;AACb,mBAAS,QAAQ,SAAS,KAAK,MAAM;;SAGzC,CAAC,QAAG;AACF,mBAAW;AACX,mBAAW;AACX,iBAAS,SAAS,SAAS,MAAM,GAAG;AACpC,qBAAa,YAAW;OACzB;;AAIL,YAAO;AACP,WAAO;GACR;AACD,aAAW,WAAW,MAAM;AAC5B,aAAW,WAAW,MAAM;AAC5B,SAAO;AACT;ACjHO,IAAI;AAEX,IAAI;AACF,YAAU;IAER,WAAW,QAAQ,aAAa,QAAQ,gBAAgB,QAAQ,mBAAmB,QAAQ;IAC3F,aAAa,QAAQ,eAAe,QAAQ;;SAEvC,GAAG;AACV,YAAU,EAAE,WAAW,MAAM,aAAa,KAAI;;ACyBhD,IAAM,QAAQoC;AAKd,MAAM,OAAO;EAIX,GAAG;EAKH,OAAO,cAAoB;AACzB,UAAM,KAAK,IAAI,MAAM,cAAc,EAAC,QAAQ,CAAA,EAAE,CAAC;AAC/C,WAAO,GAAG,OAAM;;EAMlB,OAAO,MAAY;AACjB,WAAO,IAAI,MAAM,MAAM,EAAE,QAAQ,CAAA,EAAE,CAAE,EAAE,KAAI,EAAG,KAAK,QAAE;AACnD,SAAG,MAAK;AACR,aAAO;KACR,EAAE,MAAM,uBAAuB,MAAM,KAAK;;EAM7C,iBAAiB,IAAE;AACjB,QAAI;AACF,aAAO,iBAAiB,MAAM,YAAY,EAAE,KAAK,EAAE;aACnD,IAAM;AACN,aAAO,UAAU,IAAI,WAAW,WAAU,CAAE;;;EAKhD,cAAW;AACT,aAAS,MAAM,SAAO;AACpB,aAAO,MAAM,OAAO;;AAEtB,WAAO;;EAGT,kBAAkB,WAAS;AAsBzB,WAAO,IAAI,QACT,OAAO,IAAI,WAAW,SAAS,IAC/B,UAAS;;EAGb;EAEA,OAAO,SAAU,aAAqB;AACpC,WAAO,WAAA;AACL,UAAI;AACF,YAAI,KAAK,cAAc,YAAY,MAAM,MAAM,SAAS,CAAC;AACzD,YAAI,CAAC,MAAM,OAAO,GAAG,SAAS;AAC5B,iBAAOpC,aAAQ,QAAQ,EAAE;AAC3B,eAAO;eACA,GAAG;AACV,eAAO,UAAU,CAAC;;;;EAKxB,OAAO,SAAU,aAAa,MAAM,MAAI;AACtC,QAAI;AACF,UAAI,KAAK,cAAc,YAAY,MAAM,MAAM,QAAQ,CAAA,CAAE,CAAC;AAC1D,UAAI,CAAC,MAAM,OAAO,GAAG,SAAS;AAC5B,eAAOA,aAAQ,QAAQ,EAAE;AAC3B,aAAO;aACA,GAAG;AACV,aAAO,UAAU,CAAC;;;EAKtB,oBAAoB;IAClB,KAAK,MAAM,IAAI,SAAS;;EAG1B,SAAS,SAAU,mBAAmB,iBAAe;AAEnD,UAAM,UAAUA,aAAQ,QACtB,OAAO,sBAAsB,aAC3B,MAAM,kBAAkB,iBAAiB,IACzC,iBAAiB,EAClB,QAAQ,mBAAmB,GAAK;AAInC,WAAO,IAAI,QACT,IAAI,MAAM,QAAQ,OAAO,IACzB;;EAIJ,SAASA;EAMT,OAAO;IACL,KAAK,MAAM4B;IACX,KAAK,WAAK;AACRS,eAAe,OAAO,UAAU,UAAU,MAAM,OAAO,qBAAqB;;;EAKhF;EACA;EACA;EACA;EAEA;EACA,IAAI;EACJ;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM5C;EAEN;EAEA,QAAQ,CAAA;EAER;EAGA;EAcA,cAAc;EAGd,QAAQ;EACR,SAAS,cAAc,MAAM,GAAG,EAC7B,IAAI,OAAK,SAAS,CAAC,CAAC,EACpB,OAAO,CAAC,GAAG,GAAG,MAAM,IAAK,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,CAAE;CAYrD;AAED,MAAM,SAAS,UAAU,MAAM,aAAa,WAAW;ACrOvD,IAAI,OAAO,kBAAkB,eAAe,OAAO,qBAAqB,aAAa;AACnF,eAAa,kCAAkC,kBAAY;AACzD,QAAI,CAAC,oBAAoB;AACvB,UAAI;AACJ,UAAI,YAAY;AACd,gBAAQ,SAAS,YAAY,aAAa;AAC1C,cAAM,gBAAgB,gCAAgC,MAAM,MAAM,YAAY;aACzE;AACL,gBAAQ,IAAI,YAAY,gCAAgC;UACtD,QAAQ;SACT;;AAEH,2BAAqB;AACrB,oBAAc,KAAK;AACnB,2BAAqB;;GAExB;AACD,mBAAiB,gCAAgC,CAAC,EAAC,OAAM,MAAgC;AACvF,QAAI,CAAC,oBAAoB;AACvB,uBAAiB,MAAM;;GAE1B;;SAGa,iBAAiB,aAA6B;AAC5D,MAAI,QAAQ;AACZ,MAAI;AACF,yBAAqB;AACrB,iBAAa,eAAe,KAAK,WAAW;;AAE5C,yBAAqB;;AAEzB;AAEO,IAAI,qBAAqB;AC/BhC,IAAI,OAAO,qBAAqB,aAAa;AAC3C,QAAM,KAAK,IAAI,iBAAiB,8BAA8B;AAU9D,MAAI,OAAQ,GAAW,UAAU,YAAY;AAC1C,OAAW,MAAK;;AAMnB,eAAa,kCAAkC,CAAC,iBAAY;AAC1D,QAAI,CAAC,oBAAoB;AACvB,SAAG,YAAY,YAAY;;GAE9B;AAKD,KAAG,YAAY,CAAC,OAAE;AAChB,QAAI,GAAG;AAAM,uBAAiB,GAAG,IAAI;;WAE9B,OAAO,SAAS,eAAe,OAAO,cAAc,aAAa;AAO1E,eAAa,kCAAkC,CAAC,iBAAY;AAC1D,QAAI;AACF,UAAI,CAAC,oBAAoB;AACvB,YAAI,OAAO,iBAAiB,aAAa;AAEvC,uBAAa,QACX,gCACA,KAAK,UAAU;YACb,MAAM,KAAK,OAAM;YACjB;WACD,CAAC;;AAGN,YAAI,OAAO,KAAK,SAAS,MAAM,UAAU;AAEvC,WAAC,GAAG,KAAK,SAAS,EAAE,SAAS,EAAE,qBAAqB,KAAI,CAAE,CAAC,EAAE,QAC3D,CAAC,WACC,OAAO,YAAY;YACjB,MAAM;YACN;WACD,CAAC;;;aAIV,IAAM;IAAA;GACT;AAKD,MAAI,OAAO,qBAAqB,aAAa;AACzC,qBAAiB,WAAW,CAAC,OAAgB;AAC7C,UAAI,GAAG,QAAQ,gCAAgC;AAC7C,cAAM,OAAO,KAAK,MAAM,GAAG,QAAQ;AACnC,YAAI;AAAM,2BAAiB,KAAK,YAAY;;KAE/C;;AAMH,QAAM,cAAc,KAAK,YAAY,UAAU;AAC/C,MAAI,aAAa;AAEf,gBAAY,iBAAiB,WAAW,uBAAuB;;;AAInE,SAAS,wBAAwB,EAAE,KAAI,GAAgB;AACrD,MAAI,QAAQ,KAAK,SAAS,gCAAgC;AACxD,qBAAiB,KAAK,YAAY;;AAEtC;AChFA,aAAa,kBAAkB;AAG/B4C,SAAeT,OAAa,qBAAqB;", "names": ["asap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "resolve", "reject", "props", "trans", "Promise", "keys", "i", "args", "Table", "count", "ctx", "Collection", "cmp", "direction", "addRange", "ranges", "<PERSON><PERSON><PERSON><PERSON>", "indexedDB", "p", "Transaction", "db", "tables", "keyP<PERSON>", "compound", "result", "req", "openCursor", "query", "ev", "hasGetAll", "Version", "<PERSON><PERSON>", "Debug.debug", "Debug.getErrorWithStack", "safari14Workaround", "<PERSON><PERSON><PERSON>", "dxTrans", "target", "key", "state", "_<PERSON><PERSON>", "Debug.setDebug"]}