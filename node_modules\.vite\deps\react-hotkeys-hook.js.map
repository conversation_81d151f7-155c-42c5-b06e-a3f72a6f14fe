{"version": 3, "sources": ["../../react-hotkeys-hook/src/parseHotkeys.ts", "../../react-hotkeys-hook/src/isHotkeyPressed.ts", "../../react-hotkeys-hook/src/validators.ts", "../../react-hotkeys-hook/src/BoundHotkeysProxyProvider.tsx", "../../react-hotkeys-hook/src/deepEqual.ts", "../../react-hotkeys-hook/src/HotkeysProvider.tsx", "../../react-hotkeys-hook/src/useDeepEqualMemo.ts", "../../react-hotkeys-hook/src/useHotkeys.ts", "../../react-hotkeys-hook/src/useRecordHotkeys.ts"], "sourcesContent": ["import { Hotkey, KeyboardModifiers } from './types'\n\nconst reservedModifierKeywords = ['shift', 'alt', 'meta', 'mod', 'ctrl']\n\nconst mappedKeys: Record<string, string> = {\n  esc: 'escape',\n  return: 'enter',\n  '.': 'period',\n  ',': 'comma',\n  '-': 'slash',\n  ' ': 'space',\n  '`': 'backquote',\n  '#': 'backslash',\n  '+': 'bracketright',\n  ShiftLeft: 'shift',\n  ShiftRight: 'shift',\n  AltLeft: 'alt',\n  AltRight: 'alt',\n  MetaLeft: 'meta',\n  MetaRight: 'meta',\n  OSLeft: 'meta',\n  OSRight: 'meta',\n  ControlLeft: 'ctrl',\n  ControlRight: 'ctrl',\n}\n\nexport function mapKey(key?: string): string {\n  return ((key && mappedKeys[key]) || key || '')\n    .trim()\n    .toLowerCase()\n    .replace(/key|digit|numpad|arrow/, '')\n}\n\nexport function isHotkeyModifier(key: string) {\n  return reservedModifierKeywords.includes(key)\n}\n\nexport function parseKeysHookInput(keys: string, splitKey = ','): string[] {\n  return keys.split(splitKey)\n}\n\nexport function parseHotkey(hotkey: string, combinationKey = '+', description?: string): Hotkey {\n  const keys = hotkey\n    .toLocaleLowerCase()\n    .split(combinationKey)\n    .map((k) => mapKey(k))\n\n  const modifiers: KeyboardModifiers = {\n    alt: keys.includes('alt'),\n    ctrl: keys.includes('ctrl') || keys.includes('control'),\n    shift: keys.includes('shift'),\n    meta: keys.includes('meta'),\n    mod: keys.includes('mod'),\n  }\n\n  const singleCharKeys = keys.filter((k) => !reservedModifierKeywords.includes(k))\n\n  return {\n    ...modifiers,\n    keys: singleCharKeys,\n    description,\n    hotkey,\n  }\n}\n", "import { isHotkeyModifier, mapKey } from './parseHotkeys'\n;(() => {\n  if (typeof document !== 'undefined') {\n    document.addEventListener('keydown', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n\n    document.addEventListener('keyup', (e) => {\n      if (e.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys([mapKey(e.key), mapKey(e.code)])\n    })\n  }\n\n  if (typeof window !== 'undefined') {\n    window.addEventListener('blur', () => {\n      currentlyPressedKeys.clear()\n    })\n  }\n})()\n\nconst currentlyPressedKeys: Set<string> = new Set<string>()\n\n// https://github.com/microsoft/TypeScript/issues/17002\nexport function isReadonlyArray(value: unknown): value is readonly unknown[] {\n  return Array.isArray(value)\n}\n\nexport function isHotkeyPressed(key: string | readonly string[], splitKey = ','): boolean {\n  const hotkeyArray = isReadonlyArray(key) ? key : key.split(splitKey)\n\n  return hotkeyArray.every((hotkey) => currentlyPressedKeys.has(hotkey.trim().toLowerCase()))\n}\n\nexport function pushToCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (currentlyPressedKeys.has('meta')) {\n    currentlyPressedKeys.forEach((key) => !isHotkeyModifier(key) && currentlyPressedKeys.delete(key.toLowerCase()))\n  }\n\n  hotkeyArray.forEach((hotkey) => currentlyPressedKeys.add(hotkey.toLowerCase()))\n}\n\nexport function removeFromCurrentlyPressedKeys(key: string | string[]): void {\n  const hotkeyArray = Array.isArray(key) ? key : [key]\n\n  /*\n  Due to a weird behavior on macOS we need to clear the set if the user pressed down the meta key and presses another key.\n  https://stackoverflow.com/questions/11818637/why-does-javascript-drop-keyup-events-when-the-metakey-is-pressed-on-mac-browser\n  Otherwise the set will hold all ever pressed keys while the meta key is down which leads to wrong results.\n   */\n  if (key === 'meta') {\n    currentlyPressedKeys.clear()\n  } else {\n    hotkeyArray.forEach((hotkey) => currentlyPressedKeys.delete(hotkey.toLowerCase()))\n  }\n}\n", "import { FormT<PERSON><PERSON>, Hotkey, Scopes, Trigger } from './types'\nimport { isHotkeyPressed, isReadonlyArray } from './isHotkeyPressed'\nimport { mapKey } from './parseHotkeys'\n\nexport function maybePreventDefault(e: KeyboardEvent, hotkey: Hotkey, preventDefault?: Trigger): void {\n  if ((typeof preventDefault === 'function' && preventDefault(e, hotkey)) || preventDefault === true) {\n    e.preventDefault()\n  }\n}\n\nexport function isHotkeyEnabled(e: KeyboardEvent, hotkey: Hotkey, enabled?: Trigger): boolean {\n  if (typeof enabled === 'function') {\n    return enabled(e, hotkey)\n  }\n\n  return enabled === true || enabled === undefined\n}\n\nexport function isKeyboardEventTriggeredByInput(ev: KeyboardEvent): boolean {\n  return isHotkeyEnabledOnTag(ev, ['input', 'textarea', 'select'])\n}\n\nexport function isHotkeyEnabledOnTag(\n  event: KeyboardEvent,\n  enabledOnTags: readonly FormTags[] | boolean = false\n): boolean {\n  const {target, composed} = event;\n\n  let targetTagName: string | null = null\n\n  if (isCustomElement(target as HTMLElement) && composed) {\n    targetTagName = event.composedPath()[0] && (event.composedPath()[0] as HTMLElement).tagName;\n  } else {\n    targetTagName = target && (target as HTMLElement).tagName;\n  }\n\n  if (isReadonlyArray(enabledOnTags)) {\n    return Boolean(\n      targetTagName && enabledOnTags && enabledOnTags.some((tag) => tag.toLowerCase() === targetTagName?.toLowerCase())\n    )\n  }\n\n  return Boolean(targetTagName && enabledOnTags && enabledOnTags)\n}\n\nexport function isCustomElement(element: HTMLElement): boolean {\n  // We just do a basic check w/o any complex RegEx or validation against the list of legacy names containing a hyphen,\n  // as none of them is likely to be an event target, and it won't hurt anyway if we miss.\n  // see: https://html.spec.whatwg.org/multipage/custom-elements.html#prod-potentialcustomelementname\n  return !!element.tagName && !element.tagName.startsWith(\"-\") && element.tagName.includes(\"-\");\n}\n\nexport function isScopeActive(activeScopes: string[], scopes?: Scopes): boolean {\n  if (activeScopes.length === 0 && scopes) {\n    console.warn(\n      'A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'\n    )\n\n    return true\n  }\n\n  if (!scopes) {\n    return true\n  }\n\n  return activeScopes.some((scope) => scopes.includes(scope)) || activeScopes.includes('*')\n}\n\nexport const isHotkeyMatchingKeyboardEvent = (e: KeyboardEvent, hotkey: Hotkey, ignoreModifiers = false): boolean => {\n  const { alt, meta, mod, shift, ctrl, keys } = hotkey\n  const { key: pressedKeyUppercase, code, ctrlKey, metaKey, shiftKey, altKey } = e\n\n  const keyCode = mapKey(code)\n  const pressedKey = pressedKeyUppercase.toLowerCase()\n\n  if (\n    !keys?.includes(keyCode) &&\n    !keys?.includes(pressedKey) &&\n    !['ctrl', 'control', 'unknown', 'meta', 'alt', 'shift', 'os'].includes(keyCode)\n  ) {\n    return false\n  }\n\n  if (!ignoreModifiers) {\n    // We check the pressed keys for compatibility with the keyup event. In keyup events the modifier flags are not set.\n    if (alt === !altKey && pressedKey !== 'alt') {\n      return false\n    }\n\n    if (shift === !shiftKey && pressedKey !== 'shift') {\n      return false\n    }\n\n    // Mod is a special key name that is checking for meta on macOS and ctrl on other platforms\n    if (mod) {\n      if (!metaKey && !ctrlKey) {\n        return false\n      }\n    } else {\n      if (meta === !metaKey && pressedKey !== 'meta' && pressedKey !== 'os') {\n        return false\n      }\n\n      if (ctrl === !ctrlKey && pressedKey !== 'ctrl' && pressedKey !== 'control') {\n        return false\n      }\n    }\n  }\n\n  // All modifiers are correct, now check the key\n  // If the key is set, we check for the key\n  if (keys && keys.length === 1 && (keys.includes(pressedKey) || keys.includes(keyCode))) {\n    return true\n  } else if (keys) {\n    // Check if all keys are present in pressedDownKeys set\n    return isHotkeyPressed(keys)\n  } else if (!keys) {\n    // If the key is not set, we only listen for modifiers, that check went alright, so we return true\n    return true\n  }\n\n  // There is nothing that matches.\n  return false\n}\n", "import { createContext, ReactNode, useContext } from 'react'\nimport { Hotkey } from './types'\n\ntype BoundHotkeysProxyProviderType = {\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nconst BoundHotkeysProxyProvider = createContext<BoundHotkeysProxyProviderType | undefined>(undefined)\n\nexport const useBoundHotkeysProxy = () => {\n  return useContext(BoundHotkeysProxyProvider)\n}\n\ninterface Props {\n  children: ReactNode\n  addHotkey: (hotkey: Hotkey) => void\n  removeHotkey: (hotkey: Hotkey) => void\n}\n\nexport default function BoundHotkeysProxyProviderProvider({ addHotkey, removeHotkey, children }: Props) {\n  return (\n    <BoundHotkeysProxyProvider.Provider value={{ addHotkey, removeHotkey }}>\n      {children}\n    </BoundHotkeysProxyProvider.Provider>\n  )\n}\n", "export default function deepEqual(x: any, y: any): boolean {\n  //@ts-ignore\n  return x && y && typeof x === 'object' && typeof y === 'object'\n    ? Object.keys(x).length === Object.keys(y).length &&\n        //@ts-ignore\n        Object.keys(x).reduce((isEqual, key) => isEqual && deepEqual(x[key], y[key]), true)\n    : x === y\n}\n", "import { Hotkey } from './types'\nimport { createContext, ReactNode, useState, useContext, useCallback } from 'react'\nimport BoundHotkeysProxyProviderProvider from './BoundHotkeysProxyProvider'\nimport deepEqual from './deepEqual'\n\nexport type HotkeysContextType = {\n  hotkeys: ReadonlyArray<Hotkey>\n  enabledScopes: string[]\n  toggleScope: (scope: string) => void\n  enableScope: (scope: string) => void\n  disableScope: (scope: string) => void\n}\n\n// The context is only needed for special features like global scoping, so we use a graceful default fallback\nconst HotkeysContext = createContext<HotkeysContextType>({\n  hotkeys: [],\n  enabledScopes: [], // This array has to be empty instead of containing '*' as default, to check if the provider is set or not\n  toggleScope: () => {},\n  enableScope: () => {},\n  disableScope: () => {},\n})\n\nexport const useHotkeysContext = () => {\n  return useContext(HotkeysContext)\n}\n\ninterface Props {\n  initiallyActiveScopes?: string[]\n  children: ReactNode\n}\n\nexport const HotkeysProvider = ({ initiallyActiveScopes = ['*'], children }: Props) => {\n  const [internalActiveScopes, setInternalActiveScopes] = useState(\n    initiallyActiveScopes?.length > 0 ? initiallyActiveScopes : ['*']\n  )\n  const [boundHotkeys, setBoundHotkeys] = useState<Hotkey[]>([])\n\n  const enableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes('*')) {\n        return [scope]\n      }\n\n      return Array.from(new Set([...prev, scope]))\n    })\n  }, [])\n\n  const disableScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.filter((s) => s !== scope).length === 0) {\n        return ['*']\n      } else {\n        return prev.filter((s) => s !== scope)\n      }\n    })\n  }, [])\n\n  const toggleScope = useCallback((scope: string) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes(scope)) {\n        if (prev.filter((s) => s !== scope).length === 0) {\n          return ['*']\n        } else {\n          return prev.filter((s) => s !== scope)\n        }\n      } else {\n        if (prev.includes('*')) {\n          return [scope]\n        }\n\n        return Array.from(new Set([...prev, scope]))\n      }\n    })\n  }, [])\n\n  const addBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => [...prev, hotkey])\n  }, [])\n\n  const removeBoundHotkey = useCallback((hotkey: Hotkey) => {\n    setBoundHotkeys((prev) => prev.filter((h) => !deepEqual(h, hotkey)))\n  }, [])\n\n  return (\n    <HotkeysContext.Provider\n      value={{ enabledScopes: internalActiveScopes, hotkeys: boundHotkeys, enableScope, disableScope, toggleScope }}\n    >\n      <BoundHotkeysProxyProviderProvider addHotkey={addBoundHotkey} removeHotkey={removeBoundHotkey}>\n        {children}\n      </BoundHotkeysProxyProviderProvider>\n    </HotkeysContext.Provider>\n  )\n}\n", "import { useRef } from 'react'\nimport deepEqual from './deepEqual'\n\nexport default function useDeepEqualMemo<T>(value: T) {\n  const ref = useRef<T | undefined>(undefined)\n\n  if (!deepEqual(ref.current, value)) {\n    ref.current = value\n  }\n\n  return ref.current\n}\n", "import { HotkeyCallback, Keys, Options, OptionsOrDependencyArray, RefType } from './types'\nimport { DependencyList, RefCallback, useCallback, useEffect, useState, useLayoutEffect, useRef } from 'react'\nimport { mapKey, parseHotkey, parseKeysHookInput } from './parseHotkeys'\nimport {\n  isHotkeyEnabled,\n  isHotkeyEnabledOnTag,\n  isHotkeyMatchingKeyboardEvent,\n  isKeyboardEventTriggeredByInput,\n  isScopeActive,\n  maybePreventDefault,\n} from './validators'\nimport { useHotkeysContext } from './HotkeysProvider'\nimport { useBoundHotkeysProxy } from './BoundHotkeysProxyProvider'\nimport useDeepEqualMemo from './useDeepEqualMemo'\nimport { isReadonlyArray, pushToCurrentlyPressedKeys, removeFromCurrentlyPressedKeys } from './isHotkeyPressed'\n\nconst stopPropagation = (e: KeyboardEvent): void => {\n  e.stopPropagation()\n  e.preventDefault()\n  e.stopImmediatePropagation()\n}\n\nconst useSafeLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport default function useHotkeys<T extends HTMLElement>(\n  keys: Keys,\n  callback: HotkeyCallback,\n  options?: OptionsOrDependencyArray,\n  dependencies?: OptionsOrDependencyArray\n) {\n  const [ref, setRef] = useState<RefType<T>>(null)\n  const hasTriggeredRef = useRef(false)\n\n  const _options: Options | undefined = !(options instanceof Array)\n    ? (options as Options)\n    : !(dependencies instanceof Array)\n    ? (dependencies as Options)\n    : undefined\n  const _keys: string = isReadonlyArray(keys) ? keys.join(_options?.splitKey) : keys\n  const _deps: DependencyList | undefined =\n    options instanceof Array ? options : dependencies instanceof Array ? dependencies : undefined\n\n  const memoisedCB = useCallback(callback, _deps ?? [])\n  const cbRef = useRef<HotkeyCallback>(memoisedCB)\n\n  if (_deps) {\n    cbRef.current = memoisedCB\n  } else {\n    cbRef.current = callback\n  }\n\n  const memoisedOptions = useDeepEqualMemo(_options)\n\n  const { enabledScopes } = useHotkeysContext()\n  const proxy = useBoundHotkeysProxy()\n\n  useSafeLayoutEffect(() => {\n    if (memoisedOptions?.enabled === false || !isScopeActive(enabledScopes, memoisedOptions?.scopes)) {\n      return\n    }\n\n    const listener = (e: KeyboardEvent, isKeyUp = false) => {\n      if (isKeyboardEventTriggeredByInput(e) && !isHotkeyEnabledOnTag(e, memoisedOptions?.enableOnFormTags)) {\n        return\n      }\n\n      // TODO: SINCE THE EVENT IS NOW ATTACHED TO THE REF, THE ACTIVE ELEMENT CAN NEVER BE INSIDE THE REF. THE HOTKEY ONLY TRIGGERS IF THE\n      // REF IS THE ACTIVE ELEMENT. THIS IS A PROBLEM SINCE FOCUSED SUB COMPONENTS WON'T TRIGGER THE HOTKEY.\n      if (ref !== null) {\n        const rootNode = ref.getRootNode()\n        if (\n          (rootNode instanceof Document || rootNode instanceof ShadowRoot) &&\n          rootNode.activeElement !== ref &&\n          !ref.contains(rootNode.activeElement)\n        ) {\n          stopPropagation(e)\n          return\n        }\n      }\n\n      if ((e.target as HTMLElement)?.isContentEditable && !memoisedOptions?.enableOnContentEditable) {\n        return\n      }\n\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) => {\n        const hotkey = parseHotkey(key, memoisedOptions?.combinationKey)\n\n        if (isHotkeyMatchingKeyboardEvent(e, hotkey, memoisedOptions?.ignoreModifiers) || hotkey.keys?.includes('*')) {\n          if (memoisedOptions?.ignoreEventWhen?.(e)) {\n            return\n          }\n\n          if (isKeyUp && hasTriggeredRef.current) {\n            return\n          }\n\n          maybePreventDefault(e, hotkey, memoisedOptions?.preventDefault)\n\n          if (!isHotkeyEnabled(e, hotkey, memoisedOptions?.enabled)) {\n            stopPropagation(e)\n\n            return\n          }\n\n          // Execute the user callback for that hotkey\n          cbRef.current(e, hotkey)\n\n          if (!isKeyUp) {\n            hasTriggeredRef.current = true\n          }\n        }\n      })\n    }\n\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      pushToCurrentlyPressedKeys(mapKey(event.code))\n\n      if ((memoisedOptions?.keydown === undefined && memoisedOptions?.keyup !== true) || memoisedOptions?.keydown) {\n        listener(event)\n      }\n    }\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      if (event.key === undefined) {\n        // Synthetic event (e.g., Chrome autofill).  Ignore.\n        return\n      }\n\n      removeFromCurrentlyPressedKeys(mapKey(event.code))\n\n      hasTriggeredRef.current = false\n\n      if (memoisedOptions?.keyup) {\n        listener(event, true)\n      }\n    }\n\n    const domNode = ref || _options?.document || document\n\n    // @ts-ignore\n    domNode.addEventListener('keyup', handleKeyUp, _options?.eventListenerOptions)\n    // @ts-ignore\n    domNode.addEventListener('keydown', handleKeyDown, _options?.eventListenerOptions)\n\n    if (proxy) {\n      parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n        proxy.addHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n      )\n    }\n\n    return () => {\n      // @ts-ignore\n      domNode.removeEventListener('keyup', handleKeyUp, _options?.eventListenerOptions)\n      // @ts-ignore\n      domNode.removeEventListener('keydown', handleKeyDown, _options?.eventListenerOptions)\n\n      if (proxy) {\n        parseKeysHookInput(_keys, memoisedOptions?.splitKey).forEach((key) =>\n          proxy.removeHotkey(parseHotkey(key, memoisedOptions?.combinationKey, memoisedOptions?.description))\n        )\n      }\n    }\n  }, [ref, _keys, memoisedOptions, enabledScopes])\n\n  return setRef as RefCallback<T>\n}\n", "import { useCallback, useState } from 'react'\nimport { mapKey } from './parseHotkeys'\n\nexport default function useRecordHotkeys() {\n  const [keys, setKeys] = useState(new Set<string>())\n  const [isRecording, setIsRecording] = useState(false)\n\n  const handler = useCallback((event: KeyboardEvent) => {\n    if (event.key === undefined) {\n      // Synthetic event (e.g., Chrome autofill).  Ignore.\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    setKeys((prev) => {\n      const newKeys = new Set(prev)\n\n      newKeys.add(mapKey(event.code))\n\n      return newKeys\n    })\n  }, [])\n\n  const stop = useCallback(() => {\n    if (typeof document !== 'undefined') {\n      document.removeEventListener('keydown', handler)\n\n      setIsRecording(false)\n    }\n  }, [handler])\n\n  const start = useCallback(() => {\n    setKeys(new Set<string>())\n\n    if (typeof document !== 'undefined') {\n      stop()\n\n      document.addEventListener('keydown', handler)\n\n      setIsRecording(true)\n    }\n  }, [handler, stop])\n\n  const resetKeys = useCallback(() => {\n    setKeys(new Set<string>())\n  }, [])\n\n  return [keys, { start, stop, resetKeys, isRecording }] as const\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,2BAA2B,CAAC,SAAS,OAAO,QAAQ,OAAO,MAAM;AAEvE,IAAMC,aAAqC;EACzCC,KAAK;EACL,UAAQ;EACR,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;EACLC,WAAW;EACXC,YAAY;EACZC,SAAS;EACTC,UAAU;EACVC,UAAU;EACVC,WAAW;EACXC,QAAQ;EACRC,SAAS;EACTC,aAAa;EACbC,cAAc;;SAGAC,OAAOC,KAAY;AACjC,UAASA,OAAOb,WAAWa,GAAG,KAAMA,OAAO,IACxCC,KAAI,EACJC,YAAW,EACXC,QAAQ,0BAA0B,EAAE;AACzC;SAEgBC,iBAAiBJ,KAAW;AAC1C,SAAOd,yBAAyBmB,SAASL,GAAG;AAC9C;SAEgBM,mBAAmBC,MAAcC,UAAQ;MAARA,aAAQ,QAAA;AAARA,eAAW;;AAC1D,SAAOD,KAAKE,MAAMD,QAAQ;AAC5B;SAEgBE,YAAYC,QAAgBC,gBAAsBC,aAAoB;MAA1CD,mBAAc,QAAA;AAAdA,qBAAiB;;AAC3D,MAAML,OAAOI,OACVG,kBAAiB,EACjBL,MAAMG,cAAc,EACpBG,IAAI,SAACC,GAAC;AAAA,WAAKjB,OAAOiB,CAAC;;AAEtB,MAAMC,YAA+B;IACnCC,KAAKX,KAAKF,SAAS,KAAK;IACxBc,MAAMZ,KAAKF,SAAS,MAAM,KAAKE,KAAKF,SAAS,SAAS;IACtDe,OAAOb,KAAKF,SAAS,OAAO;IAC5BgB,MAAMd,KAAKF,SAAS,MAAM;IAC1BiB,KAAKf,KAAKF,SAAS,KAAK;;AAG1B,MAAMkB,iBAAiBhB,KAAKiB,OAAO,SAACR,GAAC;AAAA,WAAK,CAAC9B,yBAAyBmB,SAASW,CAAC;;AAE9E,SAAAS,SAAA,CAAA,GACKR,WAAS;IACZV,MAAMgB;IACNV;IACAF;;AAEJ;CC9DE,WAAA;AACA,MAAI,OAAOe,aAAa,aAAa;AACnCA,aAASC,iBAAiB,WAAW,SAACC,GAAC;AACrC,UAAIA,EAAE5B,QAAQ6B,QAAW;AAEvB;;AAGFC,iCAA2B,CAAC/B,OAAO6B,EAAE5B,GAAG,GAAGD,OAAO6B,EAAEG,IAAI,CAAC,CAAC;KAC3D;AAEDL,aAASC,iBAAiB,SAAS,SAACC,GAAC;AACnC,UAAIA,EAAE5B,QAAQ6B,QAAW;AAEvB;;AAGFG,qCAA+B,CAACjC,OAAO6B,EAAE5B,GAAG,GAAGD,OAAO6B,EAAEG,IAAI,CAAC,CAAC;KAC/D;;AAGH,MAAI,OAAOE,WAAW,aAAa;AACjCA,WAAON,iBAAiB,QAAQ,WAAA;AAC9BO,2BAAqBC,MAAK;KAC3B;;AAEL,GAAC;AAED,IAAMD,uBAAoC,oBAAIE,IAAG;AAGjD,SAAgBC,gBAAgBC,OAAc;AAC5C,SAAOC,MAAMC,QAAQF,KAAK;AAC5B;AAEA,SAAgBG,gBAAgBzC,KAAiCQ,UAAQ;MAARA,aAAQ,QAAA;AAARA,eAAW;;AAC1E,MAAMkC,cAAcL,gBAAgBrC,GAAG,IAAIA,MAAMA,IAAIS,MAAMD,QAAQ;AAEnE,SAAOkC,YAAYC,MAAM,SAAChC,QAAM;AAAA,WAAKuB,qBAAqBU,IAAIjC,OAAOV,KAAI,EAAGC,YAAW,CAAE;;AAC3F;AAEA,SAAgB4B,2BAA2B9B,KAAsB;AAC/D,MAAM0C,cAAcH,MAAMC,QAAQxC,GAAG,IAAIA,MAAM,CAACA,GAAG;AAOnD,MAAIkC,qBAAqBU,IAAI,MAAM,GAAG;AACpCV,yBAAqBW,QAAQ,SAAC7C,MAAG;AAAA,aAAK,CAACI,iBAAiBJ,IAAG,KAAKkC,qBAAoB,QAAA,EAAQlC,KAAIE,YAAW,CAAE;;;AAG/GwC,cAAYG,QAAQ,SAAClC,QAAM;AAAA,WAAKuB,qBAAqBY,IAAInC,OAAOT,YAAW,CAAE;;AAC/E;AAEA,SAAgB8B,+BAA+BhC,KAAsB;AACnE,MAAM0C,cAAcH,MAAMC,QAAQxC,GAAG,IAAIA,MAAM,CAACA,GAAG;AAOnD,MAAIA,QAAQ,QAAQ;AAClBkC,yBAAqBC,MAAK;SACrB;AACLO,gBAAYG,QAAQ,SAAClC,QAAM;AAAA,aAAKuB,qBAAoB,QAAA,EAAQvB,OAAOT,YAAW,CAAE;;;AAEpF;SClEgB6C,oBAAoBnB,GAAkBjB,QAAgBqC,gBAAwB;AAC5F,MAAK,OAAOA,mBAAmB,cAAcA,eAAepB,GAAGjB,MAAM,KAAMqC,mBAAmB,MAAM;AAClGpB,MAAEoB,eAAc;;AAEpB;AAEA,SAAgBC,gBAAgBrB,GAAkBjB,QAAgBuC,SAAiB;AACjF,MAAI,OAAOA,YAAY,YAAY;AACjC,WAAOA,QAAQtB,GAAGjB,MAAM;;AAG1B,SAAOuC,YAAY,QAAQA,YAAYrB;AACzC;AAEA,SAAgBsB,gCAAgCC,IAAiB;AAC/D,SAAOC,qBAAqBD,IAAI,CAAC,SAAS,YAAY,QAAQ,CAAC;AACjE;AAEA,SAAgBC,qBACdC,OACAC,eAAAA;MAAAA,kBAAAA,QAAAA;AAAAA,oBAA+C;;AAE/C,MAAOC,SAAoBF,MAApBE,QAAQC,WAAYH,MAAZG;AAEf,MAAIC,gBAA+B;AAEnC,MAAIC,gBAAgBH,MAAqB,KAAKC,UAAU;AACtDC,oBAAgBJ,MAAMM,aAAY,EAAG,CAAC,KAAMN,MAAMM,aAAY,EAAG,CAAC,EAAkBC;SAC/E;AACLH,oBAAgBF,UAAWA,OAAuBK;;AAGpD,MAAIxB,gBAAgBkB,aAAa,GAAG;AAClC,WAAOO,QACLJ,iBAAiBH,iBAAiBA,cAAcQ,KAAK,SAACC,KAAG;AAAA,UAAAC;AAAA,aAAKD,IAAI9D,YAAW,QAAE+D,iBAAKP,kBAAa,OAAA,SAAbO,eAAe/D,YAAW;MAAG;;AAIrH,SAAO4D,QAAQJ,iBAAiBH,iBAAiBA,aAAa;AAChE;AAEA,SAAgBI,gBAAgBO,SAAoB;AAIlD,SAAO,CAAC,CAACA,QAAQL,WAAW,CAACK,QAAQL,QAAQM,WAAW,GAAG,KAAKD,QAAQL,QAAQxD,SAAS,GAAG;AAC9F;AAEA,SAAgB+D,cAAcC,cAAwBC,QAAe;AACnE,MAAID,aAAaE,WAAW,KAAKD,QAAQ;AACvCE,YAAQC,KACN,2KAA2K;AAG7K,WAAO;;AAGT,MAAI,CAACH,QAAQ;AACX,WAAO;;AAGT,SAAOD,aAAaN,KAAK,SAACW,OAAK;AAAA,WAAKJ,OAAOjE,SAASqE,KAAK;QAAML,aAAahE,SAAS,GAAG;AAC1F;AAEO,IAAMsE,gCAAgC,SAAhCA,+BAAiC/C,GAAkBjB,QAAgBiE,iBAAe;MAAfA,oBAAe,QAAA;AAAfA,sBAAkB;;AAChG,MAAQ1D,MAAsCP,OAAtCO,KAAKG,OAAiCV,OAAjCU,MAAMC,MAA2BX,OAA3BW,KAAKF,QAAsBT,OAAtBS,OAAOD,OAAeR,OAAfQ,MAAMZ,OAASI,OAATJ;AACrC,MAAasE,sBAAkEjD,EAAvE5B,KAA0B+B,OAA6CH,EAA7CG,MAAM+C,UAAuClD,EAAvCkD,SAASC,UAA8BnD,EAA9BmD,SAASC,WAAqBpD,EAArBoD,UAAUC,SAAWrD,EAAXqD;AAEpE,MAAMC,UAAUnF,OAAOgC,IAAI;AAC3B,MAAMoD,aAAaN,oBAAoB3E,YAAW;AAElD,MACE,EAACK,QAAI,QAAJA,KAAMF,SAAS6E,OAAO,MACvB,EAAC3E,QAAI,QAAJA,KAAMF,SAAS8E,UAAU,MAC1B,CAAC,CAAC,QAAQ,WAAW,WAAW,QAAQ,OAAO,SAAS,IAAI,EAAE9E,SAAS6E,OAAO,GAC9E;AACA,WAAO;;AAGT,MAAI,CAACN,iBAAiB;AAEpB,QAAI1D,QAAQ,CAAC+D,UAAUE,eAAe,OAAO;AAC3C,aAAO;;AAGT,QAAI/D,UAAU,CAAC4D,YAAYG,eAAe,SAAS;AACjD,aAAO;;AAIT,QAAI7D,KAAK;AACP,UAAI,CAACyD,WAAW,CAACD,SAAS;AACxB,eAAO;;WAEJ;AACL,UAAIzD,SAAS,CAAC0D,WAAWI,eAAe,UAAUA,eAAe,MAAM;AACrE,eAAO;;AAGT,UAAIhE,SAAS,CAAC2D,WAAWK,eAAe,UAAUA,eAAe,WAAW;AAC1E,eAAO;;;;AAOb,MAAI5E,QAAQA,KAAKgE,WAAW,MAAMhE,KAAKF,SAAS8E,UAAU,KAAK5E,KAAKF,SAAS6E,OAAO,IAAI;AACtF,WAAO;aACE3E,MAAM;AAEf,WAAOkC,gBAAgBlC,IAAI;aAClB,CAACA,MAAM;AAEhB,WAAO;;AAIT,SAAO;AACT;ACnHA,IAAM6E,gCAA4BC,4BAAyDxD,MAAS;AAE7F,IAAMyD,uBAAuB,SAAvBA,wBAAoBA;AAC/B,aAAOC,yBAAWH,yBAAyB;AAC7C;AAQA,SAAwBI,kCAAiCC,MAAA;MAAGC,YAASD,KAATC,WAAWC,eAAYF,KAAZE,cAAcC,WAAQH,KAARG;AACnF,aACEC,wBAACT,0BAA0BU,UAAQ;IAACxD,OAAO;MAAEoD;MAAWC;;IAAeC;GAEnC;AAExC;SC1BwBG,UAAUC,GAAQC,GAAM;AAE9C,SAAOD,KAAKC,KAAK,OAAOD,MAAM,YAAY,OAAOC,MAAM,WACnDC,OAAO3F,KAAKyF,CAAC,EAAEzB,WAAW2B,OAAO3F,KAAK0F,CAAC,EAAE1B;EAEvC2B,OAAO3F,KAAKyF,CAAC,EAAEG,OAAO,SAACC,SAASpG,KAAG;AAAA,WAAKoG,WAAWL,UAAUC,EAAEhG,GAAG,GAAGiG,EAAEjG,GAAG,CAAC;KAAG,IAAI,IACpFgG,MAAMC;AACZ;ACOA,IAAMI,qBAAiBhB,4BAAkC;EACvDiB,SAAS,CAAA;EACTC,eAAe,CAAA;EACfC,aAAa,SAAbA,cAAWA;EAAAA;EACXC,aAAa,SAAbA,cAAWA;EAAAA;EACXC,cAAc,SAAdA,eAAYA;EAAAA;CACb;AAED,IAAaC,oBAAoB,SAApBA,qBAAiBA;AAC5B,aAAOpB,yBAAWc,cAAc;AAClC;AAOA,IAAaO,kBAAkB,SAAlBA,iBAAenB,MAAA;mCAAMoB,uBAAAA,wBAAqBC,0BAAA,SAAG,CAAC,GAAG,IAACA,uBAAElB,WAAQH,KAARG;AAC/D,MAAAmB,gBAAwDC,wBACtDH,yBAAqB,OAAA,SAArBA,sBAAuBtC,UAAS,IAAIsC,wBAAwB,CAAC,GAAG,CAAC,GAD5DI,uBAAoBF,UAAA,CAAA,GAAEG,0BAAuBH,UAAA,CAAA;AAGpD,MAAAI,iBAAwCH,uBAAmB,CAAA,CAAE,GAAtDI,eAAYD,WAAA,CAAA,GAAEE,kBAAeF,WAAA,CAAA;AAEpC,MAAMV,mBAAca,0BAAY,SAAC5C,OAAa;AAC5CwC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAKlH,SAAS,GAAG,GAAG;AACtB,eAAO,CAACqE,KAAK;;AAGf,aAAOnC,MAAMiF,KAAK,IAAIpF,IAAG,CAAA,EAAAqF,OAAKF,MAAI,CAAE7C,KAAK,CAAA,CAAC,CAAC;KAC5C;KACA,CAAA,CAAE;AAEL,MAAMgC,oBAAeY,0BAAY,SAAC5C,OAAa;AAC7CwC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAK/F,OAAO,SAACkG,GAAC;AAAA,eAAKA,MAAMhD;SAAOH,WAAW,GAAG;AAChD,eAAO,CAAC,GAAG;aACN;AACL,eAAOgD,KAAK/F,OAAO,SAACkG,GAAC;AAAA,iBAAKA,MAAMhD;;;KAEnC;KACA,CAAA,CAAE;AAEL,MAAM8B,mBAAcc,0BAAY,SAAC5C,OAAa;AAC5CwC,4BAAwB,SAACK,MAAI;AAC3B,UAAIA,KAAKlH,SAASqE,KAAK,GAAG;AACxB,YAAI6C,KAAK/F,OAAO,SAACkG,GAAC;AAAA,iBAAKA,MAAMhD;WAAOH,WAAW,GAAG;AAChD,iBAAO,CAAC,GAAG;eACN;AACL,iBAAOgD,KAAK/F,OAAO,SAACkG,GAAC;AAAA,mBAAKA,MAAMhD;;;aAE7B;AACL,YAAI6C,KAAKlH,SAAS,GAAG,GAAG;AACtB,iBAAO,CAACqE,KAAK;;AAGf,eAAOnC,MAAMiF,KAAK,IAAIpF,IAAG,CAAA,EAAAqF,OAAKF,MAAI,CAAE7C,KAAK,CAAA,CAAC,CAAC;;KAE9C;KACA,CAAA,CAAE;AAEL,MAAMiD,qBAAiBL,0BAAY,SAAC3G,QAAc;AAChD0G,oBAAgB,SAACE,MAAI;AAAA,aAAA,CAAA,EAAAE,OAASF,MAAI,CAAE5G,MAAM,CAAA;KAAC;KAC1C,CAAA,CAAE;AAEL,MAAMiH,wBAAoBN,0BAAY,SAAC3G,QAAc;AACnD0G,oBAAgB,SAACE,MAAI;AAAA,aAAKA,KAAK/F,OAAO,SAACqG,GAAC;AAAA,eAAK,CAAC9B,UAAU8B,GAAGlH,MAAM;;;KAChE,CAAA,CAAE;AAEL,aACEkF,wBAACQ,eAAeP,UAAQ;IACtBxD,OAAO;MAAEiE,eAAeU;MAAsBX,SAASc;MAAcX,aAAAA;MAAaC,cAAAA;MAAcF,aAAAA;;IAAcZ,cAE9GC,wBAACL,mCAAiC;MAACE,WAAWiC;MAAgBhC,cAAciC;MAAkBhC;KAE3D;GACZ;AAE7B;SCzFwBkC,iBAAoBxF,OAAQ;AAClD,MAAMyF,UAAMC,qBAAsBnG,MAAS;AAE3C,MAAI,CAACkE,UAAUgC,IAAIE,SAAS3F,KAAK,GAAG;AAClCyF,QAAIE,UAAU3F;;AAGhB,SAAOyF,IAAIE;AACb;ACKA,IAAMC,kBAAkB,SAAlBA,iBAAmBtG,GAAgB;AACvCA,IAAEsG,gBAAe;AACjBtG,IAAEoB,eAAc;AAChBpB,IAAEuG,yBAAwB;AAC5B;AAEA,IAAMC,sBAAsB,OAAOnG,WAAW,cAAcoG,+BAAkBC;AAE9E,SAAwBC,WACtBhI,MACAiI,UACAC,SACAC,cAAuC;AAEvC,MAAA3B,gBAAsBC,uBAAqB,IAAI,GAAxCe,MAAGhB,UAAA,CAAA,GAAE4B,SAAM5B,UAAA,CAAA;AAClB,MAAM6B,sBAAkBZ,qBAAO,KAAK;AAEpC,MAAMa,WAAgC,EAAEJ,mBAAmBlG,SACtDkG,UACD,EAAEC,wBAAwBnG,SACzBmG,eACD7G;AACJ,MAAMiH,QAAgBzG,gBAAgB9B,IAAI,IAAIA,KAAKwI,KAAKF,YAAQ,OAAA,SAARA,SAAUrI,QAAQ,IAAID;AAC9E,MAAMyI,QACJP,mBAAmBlG,QAAQkG,UAAUC,wBAAwBnG,QAAQmG,eAAe7G;AAEtF,MAAMoH,iBAAa3B,0BAAYkB,UAAUQ,SAAK,OAALA,QAAS,CAAA,CAAE;AACpD,MAAME,YAAQlB,qBAAuBiB,UAAU;AAE/C,MAAID,OAAO;AACTE,UAAMjB,UAAUgB;SACX;AACLC,UAAMjB,UAAUO;;AAGlB,MAAMW,kBAAkBrB,iBAAiBe,QAAQ;AAEjD,MAAAO,qBAA0BzC,kBAAiB,GAAnCJ,gBAAa6C,mBAAb7C;AACR,MAAM8C,QAAQ/D,qBAAoB;AAElC8C,sBAAoB,WAAA;AAClB,SAAIe,mBAAe,OAAA,SAAfA,gBAAiBjG,aAAY,SAAS,CAACkB,cAAcmC,eAAe4C,mBAAe,OAAA,SAAfA,gBAAiB7E,MAAM,GAAG;AAChG;;AAGF,QAAMgF,WAAW,SAAXA,UAAY1H,GAAkB2H,SAAO;;UAAPA,YAAO,QAAA;AAAPA,kBAAU;;AAC5C,UAAIpG,gCAAgCvB,CAAC,KAAK,CAACyB,qBAAqBzB,GAAGuH,mBAAe,OAAA,SAAfA,gBAAiBK,gBAAgB,GAAG;AACrG;;AAKF,UAAIzB,QAAQ,MAAM;AAChB,YAAM0B,WAAW1B,IAAI2B,YAAW;AAChC,aACGD,oBAAoBE,YAAYF,oBAAoBG,eACrDH,SAASI,kBAAkB9B,OAC3B,CAACA,IAAI+B,SAASL,SAASI,aAAa,GACpC;AACA3B,0BAAgBtG,CAAC;AACjB;;;AAIJ,WAAKmI,YAAAnI,EAAE4B,WAAsB,QAAxBuG,UAA0BC,qBAAqB,EAACb,mBAAe,QAAfA,gBAAiBc,0BAAyB;AAC7F;;AAGF3J,yBAAmBwI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiB3I,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;;AAC/D,YAAMW,SAASD,YAAYV,KAAKmJ,mBAAe,OAAA,SAAfA,gBAAiBvI,cAAc;AAE/D,YAAI+D,8BAA8B/C,GAAGjB,QAAQwI,mBAAe,OAAA,SAAfA,gBAAiBvE,eAAe,MAACsF,eAAIvJ,OAAOJ,SAAI,QAAX2J,aAAa7J,SAAS,GAAG,GAAG;AAC5G,cAAI8I,mBAAe,QAAfA,gBAAiBgB,mBAAe,QAAhChB,gBAAiBgB,gBAAkBvI,CAAC,GAAG;AACzC;;AAGF,cAAI2H,WAAWX,gBAAgBX,SAAS;AACtC;;AAGFlF,8BAAoBnB,GAAGjB,QAAQwI,mBAAe,OAAA,SAAfA,gBAAiBnG,cAAc;AAE9D,cAAI,CAACC,gBAAgBrB,GAAGjB,QAAQwI,mBAAe,OAAA,SAAfA,gBAAiBjG,OAAO,GAAG;AACzDgF,4BAAgBtG,CAAC;AAEjB;;AAIFsH,gBAAMjB,QAAQrG,GAAGjB,MAAM;AAEvB,cAAI,CAAC4I,SAAS;AACZX,4BAAgBX,UAAU;;;OAG/B;;AAGH,QAAMmC,gBAAgB,SAAhBA,eAAiB9G,OAAoB;AACzC,UAAIA,MAAMtD,QAAQ6B,QAAW;AAE3B;;AAGFC,iCAA2B/B,OAAOuD,MAAMvB,IAAI,CAAC;AAE7C,WAAKoH,mBAAe,OAAA,SAAfA,gBAAiBkB,aAAYxI,WAAasH,mBAAe,OAAA,SAAfA,gBAAiBmB,WAAU,QAASnB,mBAAe,QAAfA,gBAAiBkB,SAAS;AAC3Gf,iBAAShG,KAAK;;;AAIlB,QAAMiH,cAAc,SAAdA,aAAejH,OAAoB;AACvC,UAAIA,MAAMtD,QAAQ6B,QAAW;AAE3B;;AAGFG,qCAA+BjC,OAAOuD,MAAMvB,IAAI,CAAC;AAEjD6G,sBAAgBX,UAAU;AAE1B,UAAIkB,mBAAe,QAAfA,gBAAiBmB,OAAO;AAC1BhB,iBAAShG,OAAO,IAAI;;;AAIxB,QAAMkH,UAAUzC,QAAOc,YAAQ,OAAA,SAARA,SAAUnH,aAAYA;AAG7C8I,YAAQ7I,iBAAiB,SAAS4I,aAAa1B,YAAQ,OAAA,SAARA,SAAU4B,oBAAoB;AAE7ED,YAAQ7I,iBAAiB,WAAWyI,eAAevB,YAAQ,OAAA,SAARA,SAAU4B,oBAAoB;AAEjF,QAAIpB,OAAO;AACT/I,yBAAmBwI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiB3I,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;AAAA,eAC/DqJ,MAAM3D,UAAUhF,YAAYV,KAAKmJ,mBAAe,OAAA,SAAfA,gBAAiBvI,gBAAgBuI,mBAAe,OAAA,SAAfA,gBAAiBtI,WAAW,CAAC;;;AAInG,WAAO,WAAA;AAEL2J,cAAQE,oBAAoB,SAASH,aAAa1B,YAAQ,OAAA,SAARA,SAAU4B,oBAAoB;AAEhFD,cAAQE,oBAAoB,WAAWN,eAAevB,YAAQ,OAAA,SAARA,SAAU4B,oBAAoB;AAEpF,UAAIpB,OAAO;AACT/I,2BAAmBwI,OAAOK,mBAAe,OAAA,SAAfA,gBAAiB3I,QAAQ,EAAEqC,QAAQ,SAAC7C,KAAG;AAAA,iBAC/DqJ,MAAM1D,aAAajF,YAAYV,KAAKmJ,mBAAe,OAAA,SAAfA,gBAAiBvI,gBAAgBuI,mBAAe,OAAA,SAAfA,gBAAiBtI,WAAW,CAAC;;;;KAIvG,CAACkH,KAAKe,OAAOK,iBAAiB5C,aAAa,CAAC;AAE/C,SAAOoC;AACT;SCvKwBgC,mBAAgBA;AACtC,MAAA5D,gBAAwBC,uBAAS,oBAAI5E,IAAG,CAAU,GAA3C7B,OAAIwG,UAAA,CAAA,GAAE6D,UAAO7D,UAAA,CAAA;AACpB,MAAAI,iBAAsCH,uBAAS,KAAK,GAA7C6D,cAAW1D,WAAA,CAAA,GAAE2D,iBAAc3D,WAAA,CAAA;AAElC,MAAM4D,cAAUzD,0BAAY,SAAChE,OAAoB;AAC/C,QAAIA,MAAMtD,QAAQ6B,QAAW;AAE3B;;AAGFyB,UAAMN,eAAc;AACpBM,UAAM4E,gBAAe;AAErB0C,YAAQ,SAACrD,MAAI;AACX,UAAMyD,UAAU,IAAI5I,IAAImF,IAAI;AAE5ByD,cAAQlI,IAAI/C,OAAOuD,MAAMvB,IAAI,CAAC;AAE9B,aAAOiJ;KACR;KACA,CAAA,CAAE;AAEL,MAAMC,WAAO3D,0BAAY,WAAA;AACvB,QAAI,OAAO5F,aAAa,aAAa;AACnCA,eAASgJ,oBAAoB,WAAWK,OAAO;AAE/CD,qBAAe,KAAK;;KAErB,CAACC,OAAO,CAAC;AAEZ,MAAMG,YAAQ5D,0BAAY,WAAA;AACxBsD,YAAQ,oBAAIxI,IAAG,CAAU;AAEzB,QAAI,OAAOV,aAAa,aAAa;AACnCuJ,WAAI;AAEJvJ,eAASC,iBAAiB,WAAWoJ,OAAO;AAE5CD,qBAAe,IAAI;;KAEpB,CAACC,SAASE,IAAI,CAAC;AAElB,MAAME,gBAAY7D,0BAAY,WAAA;AAC5BsD,YAAQ,oBAAIxI,IAAG,CAAU;KACxB,CAAA,CAAE;AAEL,SAAO,CAAC7B,MAAM;IAAE2K;IAAOD;IAAME;IAAWN;GAAa;AACvD;", "names": ["reservedModifierKeywords", "mapped<PERSON>eys", "esc", "ShiftLeft", "ShiftRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "OSLeft", "OSRight", "ControlLeft", "ControlRight", "mapKey", "key", "trim", "toLowerCase", "replace", "isHotkeyModifier", "includes", "parseKeysHookInput", "keys", "splitKey", "split", "parseHotkey", "hotkey", "combinationKey", "description", "toLocaleLowerCase", "map", "k", "modifiers", "alt", "ctrl", "shift", "meta", "mod", "singleCharKeys", "filter", "_extends", "document", "addEventListener", "e", "undefined", "pushToCurrentlyPressedKeys", "code", "removeFromCurrentlyPressedKeys", "window", "currentlyPressedKeys", "clear", "Set", "isReadonlyArray", "value", "Array", "isArray", "isHotkeyPressed", "hotkeyArray", "every", "has", "for<PERSON>ach", "add", "maybePreventDefault", "preventDefault", "isHotkeyEnabled", "enabled", "isKeyboardEventTriggeredByInput", "ev", "isHotkeyEnabledOnTag", "event", "enabledOnTags", "target", "composed", "targetTagName", "isCustomElement", "<PERSON><PERSON><PERSON>", "tagName", "Boolean", "some", "tag", "_targetTagName", "element", "startsWith", "isScopeActive", "activeScopes", "scopes", "length", "console", "warn", "scope", "isHotkeyMatchingKeyboardEvent", "ignoreModifiers", "pressedKeyUppercase", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "keyCode", "<PERSON><PERSON><PERSON>", "BoundHotkeysProxyProvider", "createContext", "useBoundHotkeysProxy", "useContext", "BoundHotkeysProxyProviderProvider", "_ref", "addHotkey", "removeHotkey", "children", "_jsx", "Provider", "deepEqual", "x", "y", "Object", "reduce", "isEqual", "HotkeysContext", "hotkeys", "enabledScopes", "toggleScope", "enableScope", "disableScope", "useHotkeysContext", "HotkeysProvider", "initiallyActiveScopes", "_ref$initiallyActiveS", "_useState", "useState", "internalActiveScopes", "setInternalActiveScopes", "_useState2", "boundHotkeys", "setBoundHotkeys", "useCallback", "prev", "from", "concat", "s", "addBoundHotkey", "removeBoundHotkey", "h", "useDeepEqualMemo", "ref", "useRef", "current", "stopPropagation", "stopImmediatePropagation", "useSafeLayoutEffect", "useLayoutEffect", "useEffect", "useHotkeys", "callback", "options", "dependencies", "setRef", "hasTriggeredRef", "_options", "_keys", "join", "_deps", "memoisedCB", "cbRef", "memoisedOptions", "_useHotkeysContext", "proxy", "listener", "isKeyUp", "enableOnFormTags", "rootNode", "getRootNode", "Document", "ShadowRoot", "activeElement", "contains", "_e$target", "isContentEditable", "enableOnContentEditable", "_hotkey$keys", "ignoreEventWhen", "handleKeyDown", "keydown", "keyup", "handleKeyUp", "domNode", "eventListenerOptions", "removeEventListener", "useRecordHotkeys", "set<PERSON><PERSON><PERSON>", "isRecording", "setIsRecording", "handler", "newKeys", "stop", "start", "resetKeys"]}